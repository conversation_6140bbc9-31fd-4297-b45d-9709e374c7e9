# **Fullstack Architektur: KI-Sprachassistenten-Plattform**

## **1\. Einleitung**

Dieses Dokument beschreibt die gesamte Full-Stack-Architektur für die KI-Sprachassistenten-Plattform, einschließlich der Backend-Systeme, des Frontends und deren Integration. Es dient als alleinige technische Quelle der Wahrheit für die KI-gesteuerte Entwicklung und gewährleistet die Konsistenz über den gesamten Technologie-Stack hinweg.

### **1.1 Starter-Template oder bestehendes Projekt**

Die Architektur basiert auf der Grundlage eines **Next.js-Projekts**. Dieses Framework wird für den gesamten Full-Stack-Ansatz genutzt, einschließlich des Frontends mit React/Shadcn/UI und des Backends mittels Next.js API Routes. Die bereits vorhandenen Design-Dateien und der Tech-Stack (Next.js, Tailwind, TypeScript, Shadcn/UI, pnpm) werden als gegeben vorausgesetzt und in diese Architektur integriert.

### **1.2 Änderungsprotokoll**

| Datum | Version | Beschreibung | Autor |
| :---- | :---- | :---- | :---- |
| 29\. Juli 2025 | 1.0 | Erster Entwurf des Dokuments | Winston (Architekt) |

## **2\. High-Level-Architektur**

### **2.1 Technische Zusammenfassung**

Die Anwendung wird als **Full-Stack Next.js-Anwendung** realisiert, die auf der **Vercel-Plattform** gehostet wird. Das Backend wird durch eine Kombination aus **Next.js API Routes** und einem **Supabase Backend-as-a-Service** (BaaS) bereitgestellt. Supabase liefert uns eine PostgreSQL-Datenbank, Authentifizierung, und Dateispeicher in einem integrierten Paket, was dem „All-in-One“-Ziel auf pragmatische Weise gerecht wird. Der gesamte Code wird in einer **Monorepo-Struktur** verwaltet, um die gemeinsame Nutzung von Code (z.B. Typ-Definitionen) zwischen Frontend und Backend zu vereinfachen.

### **2.2 Plattform und Infrastruktur**

* **Hosting-Plattform:** **Vercel**. Als Schöpfer von Next.js bietet Vercel eine nahtlose Integration, automatische Deployments, Skalierbarkeit und Serverless-Funktionen, die perfekt zu unserem Tech-Stack passen.  
* **Backend-Plattform:** **Supabase**. Dies gibt uns sofort eine robuste PostgreSQL-Datenbank, ein sicheres Authentifizierungssystem (Login, Registrierung) und Speicher für Dateien (z.B. RAG-Dokumente), ohne dass wir eine eigene Infrastruktur verwalten müssen.  
* **Deployment-Region:** EU (Frankfurt), um die Daten-Compliance in Europa zu gewährleisten.

### **2.3 Repository-Struktur**

* **Struktur:** **Monorepo**, verwaltet mit **pnpm Workspaces**.  
* **Begründung:** Ermöglicht die einfache gemeinsame Nutzung von Code und Typen zwischen der Frontend-Anwendung und den Backend-API-Routen in einem einzigen Repository. Dies verbessert die Konsistenz und reduziert Codeduplizierung.

### **2.4 High-Level-Architektur-Diagramm**

graph TD  
    User\[Benutzer\] \--\> Browser\[Browser\]  
    Browser \--\> Vercel\[Next.js Frontend @ Vercel\]  
    Vercel \--\> API\[Next.js API Routes @ Vercel\]  
      
    subgraph "Supabase Backend"  
        API \--\> Auth\[Authentifizierung\]  
        API \--\> DB\[(PostgreSQL Datenbank)\]  
        API \--\> Storage\[Dateispeicher\]  
    end

    style Vercel fill:\#9f9  
    style API fill:\#9cf

### **2.5 Architektonische Muster**

* **Gesamtarchitektur:** **Full-Stack Jamstack**. Das Frontend ist eine hochdynamische React-Anwendung, die über API-Routen mit den Backend-Diensten kommuniziert.  
* **Frontend-Muster:** **Component-Based UI** (React) und **zentrales State Management** (Zustand).  
* **Backend-Muster:** **Serverless Functions** (Next.js API Routes) und **Backend-as-a-Service** (Supabase).  
* **Datenmuster:** Die Next.js API-Routen fungieren als **Backend-for-Frontend (BFF)**, das die direkten Anfragen an Supabase kapselt und bei Bedarf Geschäftslogik hinzufügt.

## **3\. Tech Stack**

Jede Technologie wurde ausgewählt, um die Stärken des Vercel- und Supabase-Ökosystems optimal zu nutzen und eine hohe Entwicklungsgeschwindigkeit zu gewährleisten.

| Kategorie | Technologie | Version | Zweck & Begründung |
| :---- | :---- | :---- | :---- |
| **Frontend-Sprache** | TypeScript | \~5.x | Typsicherheit im gesamten Projekt. |
| **Frontend-Framework** | Next.js | \~14.x | Integrierte Full-Stack-Lösung, perfekt für Vercel. |
| **UI-Komponenten** | Shadcn/UI | Neueste | Schnelle, barrierefreie und anpassbare UI-Bausteine. |
| **State Management** | Zustand | \~4.x | Einfaches, leistungsstarkes globales State Management. |
| **Backend-Sprache** | TypeScript | \~5.x | Konsistente Sprache für Front- und Backend. |
| **Backend-Framework** | Next.js API Routes | \~14.x | Serverless-Funktionen, die nahtlos in die App integriert sind. |
| **API-Stil** | REST | N/A | Standardmäßiger, gut verstandener Ansatz für die API-Kommunikation. |
| **Datenbank** | Supabase (PostgreSQL) | Neueste | Robuste, skalierbare SQL-Datenbank, die von Supabase verwaltet wird. |
| **Dateispeicher** | Supabase Storage | Neueste | Integrierte Lösung für das Speichern von z.B. RAG-Dokumenten. |
| **Authentifizierung** | Supabase Auth | Neueste | Sichere und vollständige Lösung für Benutzer-Logins und \-verwaltung. |
| **Frontend-Testing** | Jest & React Testing Library | Neueste | Industriestandard für das Testen von React-Komponenten. |
| **Backend-Testing** | Jest | Neueste | Einheitliches Test-Framework für Front- und Backend-Logik. |
| **CI/CD** | Vercel | N/A | Nahtloses, Git-basiertes Continuous Deployment direkt aus der Plattform. |
| **Monitoring** | Vercel Analytics | N/A | Integrierte Analyse-Tools zur Überwachung der Anwendungsleistung. |
| **CSS-Framework** | Tailwind CSS | \~3.x | Utility-First-Ansatz für schnelles und konsistentes Styling. |

## **4\. Datenmodelle**

### **Agent**

* **Zweck:** Repräsentiert einen vom Benutzer erstellten KI-Sprachassistenten mit all seinen Konfigurationen.  
* **Beziehungen:**  
  * Gehört zu einem **User**.  
  * Kann eine **Telefonnummer** zugewiesen haben.  
  * Kann mit einer **Wissensdatenbank** verknüpft sein.  
  * Hat viele **Anrufprotokolle (Call Logs)**.

#### **TypeScript Interface**

export interface Agent {  
  id: string;                   // Eindeutige ID (UUID)  
  user\_id: string;              // Fremdschlüssel zum Benutzer  
  created\_at: string;           // Zeitstempel der Erstellung  
  name: string;                 // Name des Agenten  
  description?: string;          // Optionale Beschreibung  
  system\_prompt: string;        // Die Kernpersönlichkeit des Agenten  
  voice: string;                // Name der ausgewählten Stimme  
  language: 'de-DE' | 'en-US';  // Sprache des Agenten  
  status: 'active' | 'inactive';// Aktueller Status  
}

### **Wissensdatenbank & Datenquellen**

#### **KnowledgeBase (Wissensdatenbank)**

* **Zweck:** Dient als Container oder Ordner für eine Sammlung von thematisch zusammengehörigen Datenquellen.  
* **Beziehungen:**  
  * Gehört zu einem **User**.  
  * Hat viele **DataSources**.  
  * Kann mit vielen **Agenten** verknüpft sein.

##### **TypeScript Interface**

export interface KnowledgeBase {  
  id: string;  
  user\_id: string;  
  created\_at: string;  
  name: string;  
  description?: string;  
}

#### **DataSource (Datenquelle)**

* **Zweck:** Repräsentiert eine einzelne Informationsquelle innerhalb einer Wissensdatenbank.  
* **Beziehungen:**  
  * Gehört zu einer **KnowledgeBase**.

##### **TypeScript Interface**

export interface DataSource {  
  id: string;  
  knowledge\_base\_id: string;  
  created\_at: string;  
  type: 'file' | 'url' | 'text';  
  source: string;  
  status: 'pending' | 'training' | 'ready' | 'error';  
}

### **Telefonnummer & Anrufprotokoll**

#### **PhoneNumber (Telefonnummer)**

* **Zweck:** Repräsentiert eine von der Plattform bereitgestellte Telefonnummer.  
* **Beziehungen:**  
  * Gehört zu einem **User**.  
  * Kann einem **Agenten** zugewiesen werden.

##### **TypeScript Interface**

export interface PhoneNumber {  
  id: string;  
  user\_id: string;  
  created\_at: string;  
  number: string;  
  friendly\_name?: string;  
  assigned\_agent\_id?: string;  
}

#### **CallLog (Anrufprotokoll)**

* **Zweck:** Zeichnet die Details eines einzelnen Anrufs bei einem Agenten auf.  
* **Beziehungen:**  
  * Gehört zu einem **Agenten**.

##### **TypeScript Interface**

export type CallEndReason \= 'human\_hangup' | 'agent\_goodbye' | 'error' | 'undefined';

export interface CallLog {  
  id: string;  
  agent\_id: string;  
  phone\_number\_id: string;  
  start\_time: string;  
  end\_time: string;  
  duration\_in\_seconds: number;  
  status: 'completed' | 'failed' | 'in-progress';  
  end\_reason?: CallEndReason;  
  transcript\_url?: string;  
  cost?: number;  
}

## **5\. API-Spezifikation**

openapi: 3.0.1  
info:  
  title: KI-Sprachassistenten-Plattform API  
  description: API für die Verwaltung von KI-Sprachassistenten.  
  version: 1.0.0  
servers:  
  \- url: /api

paths:  
  /agents:  
    get:  
      summary: Ruft eine Liste aller Agenten des Benutzers ab  
      responses:  
        '200':  
          description: Eine Liste von Agenten.  
          content:  
            application/json:  
              schema:  
                type: array  
                items:  
                  $ref: '\#/components/schemas/Agent'  
    post:  
      summary: Erstellt einen neuen Agenten  
      responses:  
        '201':  
          description: Agent erfolgreich erstellt.  
            
  /agents/{agentId}:  
    parameters:  
      \- name: agentId  
        in: path  
        required: true  
        schema:  
          type: string  
          format: uuid  
    get:  
      summary: Ruft die Details eines spezifischen Agenten ab  
      responses:  
        '200':  
          description: Agenten-Details.  
          content:  
            application/json:  
              schema:  
                $ref: '\#/components/schemas/Agent'

components:  
  schemas:  
    Agent:  
      type: object  
      properties:  
        id:  
          type: string  
          format: uuid  
        name:  
          type: string  
        status:  
          type: string  
          enum: \[active, inactive\]  
