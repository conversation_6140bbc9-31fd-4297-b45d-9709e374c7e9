# Epic 2: Kernfunktion - Agenten-Verwaltung

**Status:** Not Started  
**Start Date:** 2025-01-29

## Übersicht

Dieses Epic implementiert die Kernfunktionalität der KI-Sprachassistenten-Plattform: die vollständige CRUD-Verwaltung (Create, Read, Update, Delete) für KI-Agenten.

## Ziele

- [ ] Benutzern ermöglichen, KI-Agenten zu erstellen, anzuzeigen, zu bearbeiten und zu löschen
- [ ] <PERSON><PERSON><PERSON>, benutzerspezifische Datenverwaltung mit Row Level Security
- [ ] Intuitive Benutzeroberfläche für die Agenten-Verwaltung
- [ ] Vollständige API-Implementierung für alle CRUD-Operationen

## Stories

### [ ] [Story 2.1: Agenten-Liste anzeigen](./../storys/Story%202.1.md)
**Status:** Not Started
- Übersichtsseite mit allen Agenten des Benutzers
- Datenbankschema und RLS-Policies
- API-Endpoint für Agenten-Liste

### [ ] [Story 2.2: Neuen Agenten erstellen](./../storys/Story%202.2.md)
**Status:** Not Started
- Formular zum Erstellen neuer Agenten
- Validierung aller Eingabefelder
- POST API-Endpoint

### [ ] [Story 2.3: Agenten-Details anzeigen](./../storys/Story%202.3.md)
**Status:** Not Started
- Detailansicht für einzelne Agenten
- Dynamic Routing mit Agent-ID
- GET API-Endpoint für einzelne Agenten

### [ ] [Story 2.4: Agenten bearbeiten](./../storys/Story%202.4.md)
**Status:** Not Started
- Bearbeitungsformular mit vorausgefüllten Werten
- PUT API-Endpoint für Updates
- Optimistic Updates

### [ ] [Story 2.5: Agenten löschen](./../storys/Story%202.5.md)
**Status:** Not Started
- Bestätigungsdialog für sicheres Löschen
- DELETE API-Endpoint
- Cascade-Löschung verknüpfter Daten

## Technische Implementierung

### Datenbankschema (Supabase)
```sql
CREATE TABLE agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL,
  description TEXT,
  system_prompt TEXT NOT NULL,
  voice TEXT NOT NULL,
  language TEXT NOT NULL CHECK (language IN ('de-DE', 'en-US')),
  status TEXT NOT NULL DEFAULT 'inactive' CHECK (status IN ('active', 'inactive'))
);

-- Row Level Security
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own agents" ON agents
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own agents" ON agents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own agents" ON agents
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own agents" ON agents
  FOR DELETE USING (auth.uid() = user_id);
```

### TypeScript Interface
```typescript
export interface Agent {
  id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  system_prompt: string;
  voice: string;
  language: 'de-DE' | 'en-US';
  status: 'active' | 'inactive';
}
```

### API-Endpoints
- `GET /api/agents` - Liste aller Agenten des Benutzers
- `POST /api/agents` - Neuen Agenten erstellen
- `GET /api/agents/[id]` - Einzelnen Agenten abrufen
- `PUT /api/agents/[id]` - Agenten aktualisieren
- `DELETE /api/agents/[id]` - Agenten löschen

### UI-Struktur
- `/dashboard/agents` - Agenten-Übersicht
- `/dashboard/agents/new` - Neuen Agenten erstellen
- `/dashboard/agents/[id]` - Agenten-Details
- `/dashboard/agents/[id]/edit` - Agenten bearbeiten

## Dateien zu erstellen/modifizieren

### Backend
- `types/agent.ts` - TypeScript Interfaces
- `app/api/agents/route.ts` - GET, POST Endpoints
- `app/api/agents/[id]/route.ts` - GET, PUT, DELETE für einzelne Agenten
- `lib/constants/voices.ts` - Verfügbare Stimmen

### Frontend
- `app/dashboard/agents/page.tsx` - Agenten-Übersicht
- `app/dashboard/agents/new/page.tsx` - Agenten erstellen
- `app/dashboard/agents/[id]/page.tsx` - Agenten-Details
- `app/dashboard/agents/[id]/edit/page.tsx` - Agenten bearbeiten
- `components/agents/agent-form.tsx` - Wiederverwendbares Formular
- `components/agents/agent-list.tsx` - Agenten-Liste
- `components/agents/agent-details.tsx` - Detail-Ansicht
- `components/agents/delete-agent-dialog.tsx` - Lösch-Bestätigung

### Navigation
- `components/app-sidebar.tsx` - Agenten-Link hinzufügen

## Akzeptanzkriterien (Epic-Level)

1. [ ] Benutzer können alle ihre Agenten in einer Übersicht sehen
2. [ ] Benutzer können neue Agenten mit allen erforderlichen Feldern erstellen
3. [ ] Benutzer können Details einzelner Agenten einsehen
4. [ ] Benutzer können bestehende Agenten bearbeiten
5. [ ] Benutzer können Agenten nach Bestätigung löschen
6. [ ] Alle Operationen sind sicher und benutzerspezifisch
7. [ ] Die UI ist responsive und benutzerfreundlich
8. [ ] Fehlerbehandlung und Loading-States sind implementiert

## Abhängigkeiten

- **Epic 1** muss abgeschlossen sein (Authentifizierung und Layout)
- Supabase-Projekt muss konfiguriert sein
- TypeScript-Interfaces müssen definiert sein

## Nächste Schritte nach Epic 2

Nach Abschluss dieses Epics können Benutzer ihre KI-Agenten vollständig verwalten. Die nächsten Epics bauen darauf auf:

- **Epic 3:** Interaktives Agenten-Testing (Sprach-Interface)
- **Epic 4:** Performance-Dashboard mit KPIs
- **Epic 5:** Wissensbasis-Integration (RAG)
- **Epic 6:** Telefonie-Integration
