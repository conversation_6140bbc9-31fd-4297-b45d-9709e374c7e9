# Story 1.1: Projekt-Initialisierung & Supabase-Anbindung

**Status:** Completed ✅

**Story**
* **Al<PERSON>** <PERSON><PERSON><PERSON><PERSON>,
* **möchte ich** das Next.js-Projekt initialisieren und es mit einem neuen Supabase-Projekt verbinden,
* **sodass** die grundlegende technische Infrastruktur vorhanden ist.

**Acceptance Criteria**
1.  Ein neues Next.js-Projekt ist mit `pnpm create next-app` erstellt.
2.  Ein neues Supabase-Projekt ist im Supabase-Dashboard erstellt.
3.  Die Umgebungsvariablen für die Supabase-URL und den `anon key` sind in `.env.local` hinterlegt.
4.  Ein Supabase-Client (`lib/supabase.ts`) ist erstellt und konfiguriert.

**Tasks / Subtasks**
* [x] **Task 1: Next.js-Projekt aufsetzen** (AC: #1)
    * [x] Führe `pnpm create next-app` mit den folgenden Einstellungen aus: TypeScript, ESLint, Tailwind CSS, App Router.
* [x] **Task 2: Supabase-Projekt erstellen** (AC: #2)
    * [x] Erstelle ein neues Projekt im Supabase-Dashboard.
    * [x] Notiere die Projekt-URL und den `anon` key aus den API-Einstellungen.
* [x] **Task 3: Umgebungsvariablen konfigurieren** (AC: #3)
    * [x] Erstelle eine `.env.local`-Datei im Stammverzeichnis des Projekts.
    * [x] Füge `NEXT_PUBLIC_SUPABASE_URL` und `NEXT_PUBLIC_SUPABASE_ANON_KEY` mit den Werten aus Task 2 hinzu.
* [x] **Task 4: Supabase-Client implementieren** (AC: #4)
    * [x] Installiere das `@supabase/supabase-js`-Paket mit `pnpm add @supabase/supabase-js`.
    * [x] Erstelle eine neue Datei unter `lib/supabase.ts`.
    * [x] Implementiere den Supabase-Client, der die Umgebungsvariablen verwendet, um eine Singleton-Instanz zu erstellen.

**Dev Notes**
* **Architektur-Kontext:** Dieses Projekt ist eine Full-Stack Next.js-Anwendung, die auf Vercel gehostet wird. Das Backend wird durch Supabase (PostgreSQL, Auth, Storage) bereitgestellt.
* **Repository:** Die Anwendung wird in einer Monorepo-Struktur mit pnpm Workspaces verwaltet.
* **Wichtig:** Die `.env.local`-Datei darf **niemals** in Git eingecheckt werden. Stelle sicher, dass `.env.local` in der `.gitignore`-Datei enthalten ist.
