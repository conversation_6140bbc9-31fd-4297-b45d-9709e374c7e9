# Story 1.2: Benutzer-Registrierung

**Status:** Completed ✅

**Story**
* **Als** neuer <PERSON>,
* **möchte ich** mich mit meiner E-Mail-Adresse und einem Passwort registrieren können,
* **sodass** ich einen Account auf der Plattform erstellen kann.

**Acceptance Criteria**
1. ✅ Ein Registrierungsformular ist unter `/signup` erreichbar.
2. ✅ Das Formular ruft bei der Übermittlung die `signUp`-Methode von Supabase Auth auf.
3. ✅ Ein neuer Benutzer wird in der `auth.users`-Tabelle in Supabase angelegt.
4. ✅ Nach erfolgreicher Registrierung wird eine Nachricht angezeigt, die den Benutzer auffordert, seine E-Mail zu bestätigen.

**Tasks / Subtasks**
* [x] **Task 1: Registrierungsseite erstellen** (AC: #1)
    * [x] E<PERSON>elle `app/signup/page.tsx` mit Registrierungsformular
    * [x] Implementiere Formular-Validierung (E-Mail, Passwort min. 6 Zeichen)
    * [x] Verwende Shadcn/UI Komponenten für konsistentes Design
* [x] **Task 2: Supabase Auth Integration** (AC: #2, #3)
    * [x] Implementiere `handleSignUp` Funktion mit `supabase.auth.signUp()`
    * [x] Fehlerbehandlung für ungültige Eingaben
    * [x] Loading-States während der Registrierung
* [x] **Task 3: Benutzer-Feedback** (AC: #4)
    * [x] Erfolgs-Nachricht nach Registrierung
    * [x] Link zur Login-Seite für bestehende Benutzer
    * [x] Fehler-Anzeige bei Problemen

**Implementation Details**
* **Datei:** `app/signup/page.tsx`
* **Komponenten:** Card, Input, Button, Label aus Shadcn/UI
* **Validierung:** Client-seitige Validierung + Supabase Server-Validierung
* **UX:** Loading-States, Erfolgs-/Fehlermeldungen, Navigation zu Login

**Dev Notes**
* Die Registrierung erfordert E-Mail-Bestätigung (Supabase Standard)
* Passwort-Mindestlänge: 6 Zeichen
* Automatische Weiterleitung nach Bestätigung erfolgt über Supabase Auth
* Responsive Design für alle Geräte
