# Story 1.4: Anwendungs-Layout mit Auth

**Status:** Completed ✅

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** ein konsistentes Layout mit Navigation und Benutzerinformationen sehen,
* **sodass** ich mich einfach in der Anwendung zurechtfinde und meine Sitzung verwalten kann.

**Acceptance Criteria**
1. ✅ Ein Auth-Context verwaltet den globalen Authentifizierungsstatus.
2. ✅ Geschützte Routen sind nur für authentifizierte Benutzer zugänglich.
3. ✅ Die Navigation zeigt Benutzerinformationen (E-Mail) an.
4. ✅ Nicht-authentifizierte Benutzer werden automatisch zur Login-Seite weitergeleitet.

**Tasks / Subtasks**
* [x] **Task 1: Auth-Context erstellen** (AC: #1)
    * [x] Erstelle `lib/auth-context.tsx` mit AuthProvider
    * [x] Implementiere `useAuth` Hook für Zugriff auf Auth-State
    * [x] Überwache Auth-State-Änderungen mit Supabase `onAuthStateChange`
    * [x] Integriere AuthProvider in Root Layout (`app/layout.tsx`)
* [x] **Task 2: Route Protection** (AC: #2, #4)
    * [x] Erstelle `components/auth-guard.tsx` für geschützte Routen
    * [x] Implementiere automatische Weiterleitung für nicht-authentifizierte Benutzer
    * [x] Loading-States während Auth-Überprüfung
    * [x] Integriere AuthGuard in Dashboard-Seite
* [x] **Task 3: Navigation aktualisieren** (AC: #3)
    * [x] Aktualisiere `components/nav-user.tsx` für echte Benutzerdaten
    * [x] Zeige E-Mail-Adresse und Initialen an
    * [x] Entferne Mock-Daten aus AppSidebar
* [x] **Task 4: Hauptseite anpassen**
    * [x] Aktualisiere `app/page.tsx` für Auth-basierte Weiterleitung
    * [x] Leite authentifizierte Benutzer zum Dashboard weiter
    * [x] Leite nicht-authentifizierte Benutzer zum Login weiter

**Implementation Details**
* **Auth Context:** `lib/auth-context.tsx` - Globales State Management
* **Auth Guard:** `components/auth-guard.tsx` - Route Protection
* **Navigation:** `components/nav-user.tsx` - Benutzer-UI mit echten Daten
* **Layout:** `app/layout.tsx` - AuthProvider Integration
* **Routing:** `app/page.tsx` - Auth-basierte Navigation

**Dev Notes**
* Auth-State wird automatisch mit Supabase synchronisiert
* Loading-States verhindern Flash of Unauthenticated Content (FOUC)
* Benutzer-Initialen werden aus E-Mail-Adresse generiert
* Responsive Design für alle Auth-Komponenten
* Konsistente Fehlerbehandlung und UX
