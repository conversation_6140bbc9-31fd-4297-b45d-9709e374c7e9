# Story 1.5: Benutzer-Logout

**Status:** Completed ✅

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** mich sicher abmelden können,
* **sodass** meine Sitzung beendet wird und andere Personen keinen Zugang zu meinem Account haben.

**Acceptance Criteria**
1. ✅ Ein Logout-Button ist in der Benutzer-Navigation verfügbar.
2. ✅ Beim <PERSON> auf Logout wird die `signOut`-Methode von Supabase Auth aufgerufen.
3. ✅ Nach erfolgreichem Logout wird der Benutzer zur Login-Seite weitergeleitet.
4. ✅ Der Auth-State wird global aktualisiert und alle geschützten Bereiche werden unzugänglich.

**Tasks / Subtasks**
* [x] **Task 1: Logout-Funktionalität implementieren** (AC: #1, #2)
    * [x] Erweitere `lib/auth-context.tsx` um `signOut` Funktion
    * [x] Implementiere `signOut` mit `supabase.auth.signOut()`
    * [x] Stelle `signOut` über useAuth Hook zur Verfügung
* [x] **Task 2: UI Integration** (AC: #1)
    * [x] Aktualisiere `components/nav-user.tsx` mit Logout-Button
    * [x] Implementiere `handleSignOut` Funktion in NavUser
    * [x] Deutsche Beschriftung "Abmelden" statt "Log out"
* [x] **Task 3: Automatische Weiterleitung** (AC: #3, #4)
    * [x] Auth-Context überwacht Auth-State-Änderungen automatisch
    * [x] AuthGuard leitet automatisch zur Login-Seite weiter
    * [x] Alle geschützten Routen werden unzugänglich

**Implementation Details**
* **Auth Context:** `signOut` Funktion in `lib/auth-context.tsx`
* **UI:** Logout-Button in Dropdown-Menü der Navigation
* **Routing:** Automatische Weiterleitung durch bestehende AuthGuard-Logik
* **State Management:** Globale Auth-State-Aktualisierung

**Dev Notes**
* Logout ist sofort wirksam - keine Verzögerung
* Auth-State wird automatisch über alle Komponenten synchronisiert
* Keine zusätzliche Bestätigung erforderlich (Standard UX-Pattern)
* Konsistent mit bestehender Auth-Architektur
* Sicher: Alle Tokens werden von Supabase invalidiert
