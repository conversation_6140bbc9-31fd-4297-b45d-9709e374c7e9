# Story 2.1: Agenten-Liste anzeigen

**Status:** Completed ✅

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** eine Übersicht aller meiner erstellten KI-Agenten sehen,
* **sodass** ich einen schnellen Überblick über meine Agenten erhalte und diese verwalten kann.

**Acceptance Criteria**
1. [x] Eine Agenten-Übersichtsseite ist unter `/agents` erreichbar.
2. [x] Die Seite zeigt alle Agenten des aktuell angemeldeten Benutzers in einer Liste/Tabelle an.
3. [x] Für jeden Agenten werden mindestens Name, Status, Erstellungsdatum und Beschreibung angezeigt.
4. [x] Die Liste ist nach Erstellungsdatum sortiert (neueste zuerst).
5. [x] Bei leerer Agenten-Liste wird ein "Leerer Zustand" mit Hinweis zum Erstellen des ersten Agenten angezeigt.
6. [x] Die Seite ist über die Hauptnavigation erreichbar.

**Tasks / Subtasks**
* [x] **Task 1: TypeScript Interfaces definieren** (AC: #2, #3)
    * [x] Erstelle `types/agent.ts` mit Agent Interface
    * [x] Definiere API Response Types
    * [x] Exportiere Types für Wiederverwendung
* [x] **Task 2: Stimmen-Konstanten erstellen** (AC: #2)
    * [x] Erstelle `lib/constants/voices.ts` mit verfügbaren Stimmen
    * [x] Implementiere Helper-Funktionen für Stimmen-Auswahl
* [x] **Task 3: API Route vorbereiten** (AC: #2, #4)
    * [x] Erstelle `app/api/agents/route.ts` Grundstruktur
    * [x] Implementiere GET und POST Handler-Skelett
    * [x] Vorbereitung für Authentifizierung und Validierung
* [x] **Task 4: Agenten-Übersichtsseite erstellen** (AC: #1, #3, #5)
    * [x] Erstelle `app/dashboard/agents/page.tsx`
    * [x] Implementiere Mock-Daten für Entwicklung
    * [x] Erstelle responsive Agenten-Liste mit Cards
    * [x] Implementiere "Leerer Zustand" für keine Agenten
* [x] **Task 5: Navigation erweitern** (AC: #6)
    * [x] Erweitere `components/app-sidebar.tsx` um Agenten-Link
    * [x] Aktualisiere Sidebar-Navigation auf Deutsch
    * [x] Teste Navigation und Routing

**Implementation Details**
* **Datenbankschema:** `agents` Tabelle mit RLS
* **API:** `/api/agents` GET-Endpoint
* **UI:** `/agents` Seite mit Tabelle/Liste (flache URL-Struktur)
* **Navigation:** Sidebar-Link zu Agenten-Bereich
* **State Management:** React Query für Server-State

**Dev Notes**
* Mock-Daten für Entwicklung implementiert - echte API-Integration folgt in nächster Phase
* Responsive Card-Layout für optimale mobile Darstellung
* Loading-States mit Skeleton-UI für bessere UX
* AuthGuard für Routenschutz implementiert
* Navigation erfolgreich erweitert und lokalisiert

**Implementierte Dateien:**
* `types/agent.ts` - TypeScript Interfaces für Agent-Datenstrukturen
* `lib/constants/voices.ts` - Verfügbare Stimmen mit Helper-Funktionen
* `app/api/agents/route.ts` - API-Route Grundstruktur (GET/POST)
* `app/agents/page.tsx` - Agenten-Übersichtsseite mit Mock-Daten
* `app/agents/new/page.tsx` - Formular zum Erstellen neuer Agenten
* `app/agents/[id]/page.tsx` - Agenten-Detailansicht
* `app/agents/[id]/edit/page.tsx` - Agenten-Bearbeitungsformular
* `components/app-sidebar.tsx` - Überarbeitete Navigation mit flacher URL-Struktur

**Architektur-Verbesserung:**
* URL-Struktur von `/dashboard/agents` zu `/agents` geändert für bessere Konzeption
* Flache Navigation: Dashboard und Agenten-Verwaltung sind separate Hauptbereiche
* Alle internen Links und Navigation entsprechend aktualisiert

**Nächste Schritte:**
* Supabase-Datenbankschema erstellen und RLS konfigurieren
* Echte API-Integration mit Authentifizierung implementieren
* Lösch-Dialog für sichere Agent-Entfernung
