# Story 2.2: Neuen Agenten erstellen

**Status:** Not Started

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** einen neuen KI-Agenten mit allen erforderlichen Konfigurationen erstellen können,
* **sodass** ich personalisierte Sprachassistenten für meine Anwendungsfälle entwickeln kann.

**Acceptance Criteria**
1. [ ] Ein "Neuen Agenten erstellen" Button ist auf der Agenten-Übersichtsseite verfügbar.
2. [ ] Ein Formular unter `/dashboard/agents/new` ermöglicht die Eingabe aller Agent-Eigenschaften.
3. [ ] Pflichtfelder: Name, System Prompt, Stimme, Sprache - alle müssen ausgefüllt werden.
4. [ ] Optionale Felder: Beschreibung kann leer gelassen werden.
5. [ ] Nach erfolgreichem Erstellen wird der Benutzer zur Agenten-Liste weitergeleitet.
6. [ ] Der neue Agent erscheint sofort in der Agenten-Liste.

**Tasks / Subtasks**
* [ ] **Task 1: API Route für POST implementieren** (AC: #5, #6)
    * [ ] Erweitere `app/api/agents/route.ts` um POST-Handler
    * [ ] Implementiere Validierung aller Eingabefelder
    * [ ] Automatische Generierung von ID und Timestamps
    * [ ] Benutzer-ID aus Auth-Session zuweisen
* [ ] **Task 2: Formular-Komponente erstellen** (AC: #2, #3, #4)
    * [ ] Erstelle `components/agents/agent-form.tsx`
    * [ ] Implementiere alle Eingabefelder mit Validierung
    * [ ] Dropdown für Stimmen-Auswahl
    * [ ] Radio-Buttons für Sprach-Auswahl (de-DE/en-US)
    * [ ] Textarea für System Prompt mit Zeichenzähler
* [ ] **Task 3: Erstellen-Seite implementieren** (AC: #2)
    * [ ] Erstelle `app/dashboard/agents/new/page.tsx`
    * [ ] Integriere Agent-Form-Komponente
    * [ ] Implementiere Form-Submission mit API-Call
    * [ ] Loading-States und Fehlerbehandlung
* [ ] **Task 4: Navigation und UX** (AC: #1, #5)
    * [ ] "Neuen Agenten erstellen" Button auf Übersichtsseite
    * [ ] Weiterleitung nach erfolgreichem Erstellen
    * [ ] Success-Toast/Notification
    * [ ] Breadcrumb-Navigation
* [ ] **Task 5: Validierung und Fehlerbehandlung** (AC: #3)
    * [ ] Client-seitige Formular-Validierung
    * [ ] Server-seitige Validierung in API
    * [ ] Benutzerfreundliche Fehlermeldungen
    * [ ] Validierung für System Prompt Länge

**Implementation Details**
* **API:** POST `/api/agents` mit Validierung
* **Form:** React Hook Form mit Zod-Schema
* **UI:** `/dashboard/agents/new` mit Agent-Form
* **Validation:** Client + Server-seitige Validierung
* **UX:** Loading-States, Success-Feedback, Error-Handling

**Dev Notes**
* System Prompt sollte mindestens 50 Zeichen haben
* Stimmen-Liste aus Konstanten-Datei laden
* Standard-Status: 'inactive' für neue Agenten
* Form-Reset nach erfolgreichem Erstellen
* Responsive Design für alle Formular-Elemente
