# Story 2.3: Agenten-Details anzeigen

**Status:** Not Started

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** die vollständigen Details eines spezifischen Agenten einsehen können,
* **sodass** ich alle Konfigurationen und Eigenschaften des Agenten überprüfen kann.

**Acceptance Criteria**
1. [ ] Eine Detailseite ist unter `/dashboard/agents/[id]` für jeden Agenten erreichbar.
2. [ ] Die Seite zeigt alle Agent-Eigenschaften in einer übersichtlichen Form an.
3. [ ] Links von der Agenten-Liste führen zur jeweiligen Detailseite.
4. [ ] Bei ungültiger Agent-ID wird eine 404-Seite angezeigt.
5. [ ] Nur der Besitzer des Agenten kann dessen Details einsehen.
6. [ ] Aktions-Buttons für "Bearbeiten" und "Löschen" sind verfügbar.

**Tasks / Subtasks**
* [ ] **Task 1: API Route für einzelnen Agenten** (AC: #1, #4, #5)
    * [ ] Erstelle `app/api/agents/[id]/route.ts` für GET-Requests
    * [ ] Implementiere ID-Validierung und Benutzer-Berechtigung
    * [ ] 404-Handling für nicht existierende Agenten
    * [ ] RLS-Policy für einzelne Agent-Abfragen
* [ ] **Task 2: Detailseite erstellen** (AC: #1, #2)
    * [ ] Erstelle `app/dashboard/agents/[id]/page.tsx`
    * [ ] Implementiere Daten-Fetching für spezifischen Agenten
    * [ ] Übersichtliche Darstellung aller Agent-Eigenschaften
    * [ ] Loading-State während Daten-Laden
* [ ] **Task 3: Detail-Komponente implementieren** (AC: #2, #6)
    * [ ] Erstelle `components/agents/agent-details.tsx`
    * [ ] Strukturierte Anzeige aller Felder
    * [ ] Status-Badge für aktiv/inaktiv
    * [ ] Formatierung von Timestamps
    * [ ] Aktions-Buttons für Bearbeiten/Löschen
* [ ] **Task 4: Navigation von Liste zu Details** (AC: #3)
    * [ ] Links in Agenten-Liste zu Detailseiten
    * [ ] Breadcrumb-Navigation auf Detailseite
    * [ ] "Zurück zur Liste" Button
* [ ] **Task 5: Fehlerbehandlung und Sicherheit** (AC: #4, #5)
    * [ ] 404-Seite für ungültige IDs
    * [ ] Unauthorized-Handling für fremde Agenten
    * [ ] Error-Boundaries für unerwartete Fehler
    * [ ] Loading-States und Skeleton-UI

**Implementation Details**
* **API:** GET `/api/agents/[id]` mit Berechtigungsprüfung
* **UI:** `/dashboard/agents/[id]` Detailseite
* **Komponenten:** Agent-Details-Komponente mit strukturierter Anzeige
* **Navigation:** Links von Liste, Breadcrumbs, Zurück-Button
* **Sicherheit:** RLS + zusätzliche Berechtigungsprüfung

**Dev Notes**
* Dynamic Route mit Next.js App Router
* Server-seitige Berechtigungsprüfung in API
* Responsive Design für Detail-Ansicht
* Consistent Styling mit anderen Seiten
* Vorbereitung für Edit/Delete-Aktionen
