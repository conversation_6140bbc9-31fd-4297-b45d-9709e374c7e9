# Story 2.4: Agenten bearbeiten

**Status:** Not Started

**Story**
* **Al<PERSON>** authentifizier<PERSON>,
* **möchte ich** die Konfiguration meiner bestehenden Agenten ändern können,
* **sodass** ich sie an neue Anforderungen anpassen und optimieren kann.

**Acceptance Criteria**
1. [ ] Ein "Bearbeiten" Button ist auf der Agenten-Detailseite verfügbar.
2. [ ] Eine Bearbeitungsseite unter `/dashboard/agents/[id]/edit` ermöglicht die Änderung aller Agent-Eigenschaften.
3. [ ] Das Formular ist mit den aktuellen Werten des Agenten vorausgefüllt.
4. [ ] Alle Validierungsregeln vom Erstellen gelten auch beim Bearbeiten.
5. [ ] Nach erfolgreichem Speichern wird der Benutzer zur Detailseite weitergeleitet.
6. [ ] Änderungen werden sofort in der Agenten-Liste und Detailansicht reflektiert.

**Tasks / Subtasks**
* [ ] **Task 1: API Route für PUT/PATCH implementieren** (AC: #5, #6)
    * [ ] Erweitere `app/api/agents/[id]/route.ts` um PUT-Handler
    * [ ] Implementiere Validierung und Berechtigungsprüfung
    * [ ] Optimistic Updates für bessere UX
    * [ ] Timestamp-Update für `updated_at` Feld
* [ ] **Task 2: Bearbeitungsseite erstellen** (AC: #2, #3)
    * [ ] Erstelle `app/dashboard/agents/[id]/edit/page.tsx`
    * [ ] Lade aktuelle Agent-Daten für Formular
    * [ ] Wiederverwendung der Agent-Form-Komponente
    * [ ] Pre-Population aller Formular-Felder
* [ ] **Task 3: Agent-Form für Bearbeitung erweitern** (AC: #3, #4)
    * [ ] Erweitere `components/agents/agent-form.tsx` für Edit-Modus
    * [ ] Props für initiale Werte und Edit/Create-Modus
    * [ ] Unterschiedliche Submit-Buttons (Erstellen/Aktualisieren)
    * [ ] Validierung bleibt identisch zu Erstellen
* [ ] **Task 4: Navigation und UX** (AC: #1, #5)
    * [ ] "Bearbeiten" Button auf Detailseite
    * [ ] Weiterleitung zur Detailseite nach Speichern
    * [ ] "Abbrechen" Button zurück zur Detailseite
    * [ ] Success-Feedback nach Aktualisierung
* [ ] **Task 5: Datenbankschema erweitern** (AC: #6)
    * [ ] `updated_at` Feld zur agents Tabelle hinzufügen
    * [ ] Automatische Aktualisierung bei Änderungen
    * [ ] Migration für bestehende Daten

**Implementation Details**
* **API:** PUT `/api/agents/[id]` mit Validierung
* **UI:** `/dashboard/agents/[id]/edit` mit vorausgefülltem Formular
* **Komponenten:** Erweiterte Agent-Form für Edit-Modus
* **Navigation:** Edit-Button, Weiterleitung, Abbrechen-Option
* **Database:** `updated_at` Feld für Änderungsverfolgung

**Dev Notes**
* Wiederverwendung der Agent-Form-Komponente
* Optimistic Updates für bessere Performance
* Validierung identisch zu Create-Flow
* Breadcrumb-Navigation für bessere UX
* Dirty-State-Tracking für ungespeicherte Änderungen
