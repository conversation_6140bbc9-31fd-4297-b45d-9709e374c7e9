# Story 2.5: Agenten löschen

**Status:** Not Started

**Story**
* **Als** authentifizier<PERSON>,
* **möchte ich** nicht mehr benötigte Agenten dauerhaft entfernen können,
* **sodass** ich meine Agenten-Liste sauber und übersichtlich halte.

**Acceptance Criteria**
1. [ ] Ein "Löschen" Button ist auf der Agenten-Detailseite verfügbar.
2. [ ] Vor dem Löschen wird eine Bestätigungsdialog angezeigt.
3. [ ] Der Dialog warnt vor der permanenten Löschung und zeigt den Agent-Namen.
4. [ ] Nach Bestätigung wird der Agent dauerhaft aus der Datenbank entfernt.
5. [ ] Der Benutzer wird zur Agenten-Liste weitergeleitet.
6. [ ] Der gelöschte Agent erscheint nicht mehr in der Liste.

**Tasks / Subtasks**
* [ ] **Task 1: API Route für DELETE implementieren** (AC: #4, #6)
    * [ ] Erweitere `app/api/agents/[id]/route.ts` um DELETE-Handler
    * [ ] Implementiere Berechtigungsprüfung (nur Besitzer)
    * [ ] Soft-Delete vs. Hard-Delete Entscheidung
    * [ ] Cascade-Löschung für verknüpfte Daten
* [ ] **Task 2: Bestätigungsdialog erstellen** (AC: #2, #3)
    * [ ] Erstelle `components/agents/delete-agent-dialog.tsx`
    * [ ] Modal/Dialog mit Warnung und Agent-Name
    * [ ] "Abbrechen" und "Löschen" Buttons
    * [ ] Gefährliche Aktion durch roten Button kennzeichnen
* [ ] **Task 3: Löschen-Funktionalität integrieren** (AC: #1, #5)
    * [ ] "Löschen" Button auf Detailseite
    * [ ] Dialog-Trigger und State-Management
    * [ ] API-Call nach Bestätigung
    * [ ] Weiterleitung zur Agenten-Liste
* [ ] **Task 4: UX und Feedback** (AC: #5, #6)
    * [ ] Loading-State während Löschvorgang
    * [ ] Success-Toast nach erfolgreichem Löschen
    * [ ] Error-Handling bei Löschfehlern
    * [ ] Optimistic Updates in der Liste
* [ ] **Task 5: Sicherheit und Validierung** (AC: #4)
    * [ ] Doppelte Berechtigungsprüfung (Client + Server)
    * [ ] Validierung der Agent-ID
    * [ ] Schutz vor versehentlichem Löschen
    * [ ] Audit-Log für Löschvorgänge (optional)

**Implementation Details**
* **API:** DELETE `/api/agents/[id]` mit Berechtigungsprüfung
* **UI:** Bestätigungsdialog mit Warnung
* **UX:** Löschen-Button, Weiterleitung, Feedback
* **Sicherheit:** Mehrfache Bestätigung und Berechtigungsprüfung
* **Data:** Hard-Delete oder Soft-Delete Strategie

**Dev Notes**
* Gefährliche Aktion - deutliche visuelle Kennzeichnung
* Bestätigungsdialog verhindert versehentliches Löschen
* Überlegung: Soft-Delete für Wiederherstellung
* Cascade-Löschung für verknüpfte Daten beachten
* Responsive Dialog für alle Geräte
