# 1. **Modellkonfiguration**

Bestimme das Verhalten des virtuellen Assistenten **JASZ**.

* **Anbieter:** z. B. OpenAI, Google, OpenRouter
* **Sprachmodell:** Auswahl des jeweils verfügbaren LLM beim <PERSON>bie<PERSON>
* **Startnachricht-Modus:** Soll JASZ die Unterhaltung eröffnen oder auf den Nutzer warten?
* **Eröffnungsnachricht:** Begrüßungstext zu Beginn des Gesprächs
* **System-Prompt:** Vordefinierter Systemkontext (bereits vorhanden)
* **Temperatur:** Steuerung der Kreativität (Wert zwischen 0 und 1)
* **Maximale Tokenanzahl:** Obergrenze der Tokens pro Sitzung

---

# 2. **Sprachausgabe (Voice)**

Wähle eine Stimme für die Ausgabe oder synchronisiere deine Bibliothek. Falls Fehler auftreten, aktiviere benutzerdefinierte Stimmen und hinterlege eine Voice-ID.
→ Aktuell verwendete Sprache für Spracherkennung: „de“. Wähle eine Stimme, die mit Deutsch kompatibel ist.

* **Stimm-Anbieter:** z. B. ElevenLabs, Cartesia
* **Stimme:** Verfügbare Stimmen des Anbieters
* **TTS/STT-Modell:** Text-to-Speech oder Speech-to-Text Modell
* **Hintergrundklang:** z. B. Bürogeräusche
* **Hintergrund-Sound-URL oder Upload:** Link oder Datei
* **Mindestanzahl an Zeichen:** Eingabelänge vor Start
* **Sprechgeschwindigkeit:** Geschwindigkeit der Sprachausgabe

---

# 3. **Spracherkennung (Transcriber)**

Hier werden die Einstellungen für die Transkription von Gesprächen mit JASZ festgelegt.

* **Anbieter:** z. B. Deepgram
* **Sprache:** z. B. Deutsch, Englisch
* **Erkennungsmodell:** Sprachmodell des Anbieters
* **Hintergrundgeräuschfilter:** Aktivieren, um Störgeräusche auszublenden
* **Vertrauensgrenze (Confidence Score):** Transkriptionen unter diesem Wert werden verworfen (Skala 0–1)
* **Zahlenformat:** Zahlen in Ziffern statt als Worte darstellen

---

# 4. **Werkzeuge (Tools)**

Ermögliche JASZ, während eines Gesprächs Aktionen durchzuführen. Wähle Tools aus der Bibliothek aus oder verbinde eigene Services (z. B. über Make.com oder APIs).

* **Tool-Auswahl:** Verfügbare und konfigurierte Tools der Tools-Seite

### 4.1. **Vordefinierte Funktionen**

Bereits integrierte Funktionen für typische Anwendungsfälle, die aktiviert und angepasst werden können.

* **Anruf beenden erlauben:** JASZ kann das Gespräch eigenständig beenden (empfohlen für GPT-4 oder höher)
* **Tastenfeld-Nutzung:** JASZ kann Ziffern über das Nummernfeld wählen
* **Weiterleitungsnummer:** Rufnummer für Call-Forwarding

---

# 5. **Analysefunktionen**

### 5.1. **Zusammenfassung**

Erstelle automatisch eine Gesprächszusammenfassung. Diese wird in den Gesprächsprotokollen gespeichert.

* **Timeout für Zusammenfassung (in Sekunden)**
* **Zusammenfassungs-Prompt**
* **Mindestanzahl an Nachrichten, um eine Analyse zu starten**

### 5.2. **Erfolgsauswertung**

Bestimme, ob ein Gespräch erfolgreich war – basierend auf Bewertungsrichtlinien und/oder einem Bewertungs-Prompt.

* **Bewertungsprompt:** Textvorlage zur Analyse der Zielerreichung

### 5.3. **Strukturierte Datenerfassung**

Extrahiere gezielt strukturierte Informationen aus dem Gesprächsverlauf – mit oder ohne Datenschema.

* **Prompt zur Datenerfassung**
* **Timeout für strukturierte Datenerfassung (in Sekunden)**

---

# 6. **Erweiterte Einstellungen**

## 6.1. **Datenschutz**

Konfiguriere, wie Gespräche gespeichert oder geschützt werden.

* **HIPAA-Konformität:** Keine Speicherung von Protokollen oder Audioaufnahmen
* **PCI-Konformität:** Nur PCI-zertifizierte Anbieter verwendbar
* **Tonaufzeichnung:** Gespräch mitschneiden
* **Audioformat:** Format für die Gesprächsaufzeichnung wählen
* **Videoaufzeichnung:** Optional bei Webcalls – zeichnet das Nutzer-Video auf

---

## 6.2. **Startzeitpunkt der Sprachausgabe**

Definiere, wann JASZ mit dem Sprechen beginnen darf.

* **Wartezeit (Sekunden)**
* **Intelligente Endpunkterkennung:** Genauere Erkennung von Gesprächsübergängen (LiveKit nur Englisch)
* **Wartezeit nach Satzzeichen:** Verzögerung nach einem Punkt, Komma etc.
* **Wartezeit ohne Satzzeichen:** Verzögerung nach unvollständiger Aussage
* **Wartezeit bei Zahlen:** Verzögerung nach einer Zahlenangabe

---

## 6.3. **Voicemail-Erkennung**

Lege fest, wie JASZ auf Anrufbeantworter reagiert.

* **Anbieter zur Erkennung:** JASZ verwendet moderne KI für Voicemail-Erkennung, alternativ stehen Google, OpenAI oder Twilio zur Verfügung.

---

## 6.4. **Sprechstopp-Regelung**

Bestimme, wann JASZ das Reden unterbricht oder pausiert.

* **Wortanzahl des Nutzers:** Ab wie vielen gesprochenen Wörtern JASZ stoppt
* **Sprechdauer in Sekunden:** Zeitspanne des Nutzers, nach der JASZ aufhört
* **Pause nach Unterbrechung (in Sekunden):** Wartezeit vor Wiederaufnahme des Gesprächs

---

## 6.5. **Timeouts für Anrufende**

Regelt, wann Gespräche automatisch beendet werden.

* **Stille-Timeout:** Dauer ohne Eingabe, bevor das Gespräch automatisch endet
* **Maximale Gesprächsdauer (in Sekunden)**

---

## 6.6. **Zifferneingabe via Tastatur**

Ermöglicht dem Nutzer Eingaben über das Nummernfeld.

* **Zifferneingabe aktivieren**
* **Timeout für Eingabe:** Zeitspanne bis zur Verarbeitung der Eingabe
* **Trennzeichen:** Zeichen zur Abgrenzung der Eingabe (z. B. „#“)

---

