# JASZ-AI – Progressive Research Plans (PRPs)

Dieses Verzeichnis enthält alle Progressive Research Plans (PRPs) für die Entwicklung des JASZ-AI Dashboards – einer webbasierten Plattform zur Erstellung und Verwaltung von AI Voice Assistenten.

---

## 🔍 Ziel

Ein PRP ist eine strukturierte Anweisung, mit der AI-Agenten (z. B. Claude Code) in der Lage sind, ein vollständiges Software-Feature iterativ und eigenständig umzusetzen. Es kombiniert Produktanforderungen, Umsetzungsstrategie und validierbare Tests.

---

## 📁 Struktur

```text
PRPs/
├── dashboard/            → Layout, Navigation & Onboarding
├── assistants/           → Erstellen, Bearbeiten & Verwalten von Voice Assistants
├── integrations/         → API-Verbindungen (z. B. Zapier, CRM)
├── telephony/            → Telefonnummern kaufen & verwalten
├── user/                 → Profileinstellungen & Abrechnung
├── templates/            → Wiederverwendbare PRP-Templates
├── scripts/              → Runner für lokale oder CI-Nutzung
├── ai_docs/              → Claude-Zugriff auf relevante Kontextdokumentation
└── CLAUDE.md             → Kontextualisierung für Claude-Code-Interaktion
