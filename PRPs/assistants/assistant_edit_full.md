name: "[Feature Name] – High-Fidelity PRP"
description: |

## Goal
[Beschreibung des Ziels]

## Why
[War<PERSON> dieses Feature wichtig ist]

## What
[Konkrete Anforderungen und Benutzerverhalten]

## All Needed Context
### Documentation & References
- file: [pfad/zur/datei]
  why: [Warum diese Datei wichtig ist]

### Current Codebase tree
```bash
.
├── app/
│   ├── dashboard/
│   │   ├── data.json
│   │   ├── index.css
│   │   └── page.tsx
├── components/
│   ├── animated-theme-toggle.tsx
│   ├── app-sidebar.tsx
│   ├── dashboard/
│   │   ├── charts/
│   │   │   ├── chart-area-interactive.tsx
│   │   │   ├── circle-charts.tsx
│   │   │   ├── radial-charts.tsx
│   │   │   └── section-cards.tsx
│   │   ├── data-table.tsx
│   │   └── site-header.tsx
│   ├── nav-user.tsx
│   ├── ui/
│   │   ├── badge.tsx
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── drawer.tsx
│   │   ├── input.tsx
│   │   ├── select.tsx
│   │   ├── sheet.tsx
│   │   ├── table.tsx
│   │   └── tooltip.tsx
├── hooks/
│   ├── use-mobile.ts
│   └── use-theme.ts
├── lib/
│   ├── theme-context.tsx
│   └── utils.ts

```

### Desired Codebase tree
```bash
# Beispiel (kontextabhängig anpassbar)
components/
├── assistant/
│   ├── assistant-list.tsx
│   ├── assistant-form.tsx

app/
├── assistants/
│   └── page.tsx
```

### Known Gotchas
```python
# ❗ Tailwind benötigt className-Disziplin und mobile-first
# ❗ Shadcn-Komponenten erwarten konsistente props
# ❗ JSON-Files als Mock-Daten müssen synchron geladen werden
```

## Implementation Blueprint

### Data models
```json
# Beispielhafte JSON-Struktur für einen Assistant
{
  "id": "string",
  "name": "My Assistant",
  "created_at": "2023-01-01T12:00:00Z"
}
```

### Tasks
```yaml
Task 1:
CREATE components/assistant/assistant-form.tsx
  - Formularkomponente mit Name-Feld und Submit-Button

Task 2:
MODIFY app/assistants/page.tsx
  - Importiere assistant-form
  - Integriere mit JSON-Mock

Task 3:
CREATE test/assistant-form.test.tsx
  - Schreibe grundlegende Render- und Interaktionstests
```

### Pseudocode
```typescript
function AssistantForm() {
  const [name, setName] = useState("");

  const handleSubmit = () => {
    // validate input
    if (!name) return alert("Name required");
    // write to local JSON or state for now
    saveAssistant({ name });
  };

  return <input value={name} onChange={e => setName(e.target.value)} />;
}
```

### Integration Points
```yaml
COMPONENTS:
  - Importiere assistant-form in assistants/page.tsx
  - Nutze UI-Komponenten aus shadcn/ui: button, input

MOCKDATA:
  - Lies aus: /app/dashboard/data.json
  - Bei Submit: Update JSON lokal oder speichere im Zustand
```


### Desired Codebase tree
```bash
# Neue Struktur
```

### Known Gotchas
```python
# Besonderheiten im Projekt oder bei Libraries
```

## Implementation Blueprint
### Data models
```python
# Datenmodelle oder Supabase Tabellen
```

### Tasks
```yaml
# Schrittweise Umsetzung
```

### Pseudocode
```python
# Kritische Logik als Pseudocode
```

### Integration Points
```yaml
# DB, Routen, Configs
```

## Validation Loop
### Syntax & Typechecks
### Unit Tests
### Integration Tests
### Creative Validation

## Final validation Checklist
- [ ] Alle Tests bestanden
- [ ] Keine Lint- oder Typfehler
- [ ] Manuelle Tests erfolgreich
