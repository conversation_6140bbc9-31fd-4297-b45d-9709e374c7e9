# JASZ-AI Dashboard

Eine KI-Sprachassistenten-Plattform für die Erstellung und Verwaltung von KI-Agenten.

## Features

- ✅ **Benutzerauthentifizierung** mit Supabase Auth
- ✅ **Responsive Dashboard** mit Charts und Tabellen
- ✅ **Dark/Light Theme** Support
- ✅ **Moderne UI** mit Shadcn/UI Komponenten

## Tech Stack

- **Frontend:** Next.js 15, React 19, TypeScript
- **Styling:** Tailwind CSS, Shadcn/UI
- **Backend:** Supabase (PostgreSQL, Auth, Storage)
- **Deployment:** Vercel

## Setup

1. **Umgebungsvariablen konfigurieren:**
   ```bash
   cp .env.local.example .env.local
   ```
   Füge deine Supabase-Credentials in `.env.local` ein.

2. **Dependencies installieren:**
   ```bash
   pnpm install
   ```

3. **Development Server starten:**

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

   Öffne [http://localhost:3000](http://localhost:3000) in deinem Browser.

## Authentifizierung

Die App verwendet Supabase Auth für die Benutzerauthentifizierung:

- **Registrierung:** `/signup` - Neue Benutzer können sich registrieren
- **Login:** `/login` - Bestehende Benutzer können sich anmelden
- **Dashboard:** `/dashboard` - Geschützter Bereich für authentifizierte Benutzer

## Projektstruktur

```
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard-Seite (geschützt)
│   ├── login/            # Login-Seite
│   ├── signup/           # Registrierungs-Seite
│   └── layout.tsx        # Root Layout mit Auth Provider
├── components/           # React Komponenten
│   ├── ui/              # Shadcn/UI Komponenten
│   ├── auth-guard.tsx   # Authentifizierungs-Schutz
│   └── nav-user.tsx     # Benutzer-Navigation
├── lib/                 # Utilities
│   ├── supabase.ts      # Supabase Client
│   └── auth-context.tsx # Auth Context Provider
└── .bmad-core/          # Projekt-Dokumentation
    ├── prd.md           # Product Requirements Document
    └── storys/          # User Stories
```

## Nächste Schritte (Epic 2+)

- [ ] **Epic 2:** Agenten-Verwaltung (CRUD)
- [ ] **Epic 3:** Interaktives Agenten-Testing
- [ ] **Epic 4:** Performance-Dashboard mit KPIs
- [ ] **Epic 5:** Wissensbasis-Integration (RAG)
- [ ] **Epic 6:** Telefonie-Integration
