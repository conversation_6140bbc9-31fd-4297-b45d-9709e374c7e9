'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { AuthGuard } from '@/components/auth-guard'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { AgentForm } from '@/components/agents/agent-form'
import { AgentTabNavigation } from '@/components/agents/agent-tab-navigation'
import { Agent, CreateAgentRequest } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'

export default function EditAgentPage() {
  const params = useParams()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [agent, setAgent] = useState<Agent | null>(null)

  useEffect(() => {
    // TODO: Implement real API call
    // For now using mock data
    const mockAgent: Agent = {
      id: params.id as string,
      user_id: 'mock-user',
      created_at: '2025-01-29T10:00:00Z',
      updated_at: '2025-01-29T10:00:00Z',
      name: 'Customer Service Agent',
      description: 'Helps customers with general questions and problems',
      system_prompt: 'You are a friendly customer service representative who helps customers with their concerns. Always be polite, helpful, and professional.',
      voice: 'de-DE-KatjaNeural',
      language: 'de-DE',
      status: 'active'
    }

    // Simulate API call
    setTimeout(() => {
      setAgent(mockAgent)
      setInitialLoading(false)
    }, 1000)
  }, [params.id])

  const handleSubmit = async (formData: CreateAgentRequest) => {
    setLoading(true)
    
    try {
      // TODO: Implement real API call
      console.log('Updating agent:', { id: params.id, ...formData })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect to agent details
      router.push(`/agents/${params.id}`)
    } catch (error) {
      console.error('Error updating agent:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader title="Edit Agent" />
            <div className="flex flex-1 flex-col">
              <div className="container mx-auto p-6">
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                  <div className="h-64 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title={`Edit ${agent?.name || 'Agent'}`} />
          <div className="flex flex-1 flex-col">
            {agent && (
              <>
                <AgentTabNavigation
                  activeTab="model"
                  onTabChange={() => {}}
                />
                <AgentForm
                  mode="edit"
                  initialData={agent}
                  onSubmit={handleSubmit}
                  loading={loading}
                />
              </>
            )}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
