'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { AuthGuard } from '@/components/auth-guard'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { AgentDetails } from '@/components/agents/agent-details'
import { Agent } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'

export default function AgentDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [agent, setAgent] = useState<Agent | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // TODO: Implement real API call
    // For now using mock data
    const mockAgent: Agent = {
      id: params.id as string,
      user_id: 'mock-user',
      created_at: '2025-01-29T10:00:00Z',
      updated_at: '2025-01-29T10:00:00Z',
      name: 'Customer Service Agent',
      description: 'Helps customers with general questions and problems',
      system_prompt: 'You are a friendly customer service representative who helps customers with their concerns. Always be polite, helpful, and professional.',
      voice: 'de-DE-KatjaNeural',
      language: 'de-DE',
      status: 'active'
    }

    // Simulate API call
    setTimeout(() => {
      setAgent(mockAgent)
      setLoading(false)
    }, 1000)
  }, [params.id])

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this agent?')) return
    
    try {
      // TODO: Implement real API call
      console.log('Deleting agent:', agent?.id)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect to agents list
      router.push('/agents')
    } catch (error) {
      console.error('Error deleting agent:', error)
    }
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title={agent?.name || 'Agent Details'} />
          <div className="flex flex-1 flex-col">
            {agent && (
              <AgentDetails 
                agent={agent} 
                loading={loading} 
                onDelete={handleDelete} 
              />
            )}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
