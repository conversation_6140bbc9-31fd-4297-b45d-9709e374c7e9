'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthGuard } from '@/components/auth-guard'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { AgentForm } from '@/components/agents/agent-form'
import { AgentTabNavigation } from '@/components/agents/agent-tab-navigation'
import { CreateAgentRequest } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { ModelConfigurationSection } from '@/components/agents/sections/ModelConfigurationSection'
import { VoiceConfigurationSection } from '@/components/agents/sections/VoiceConfigurationSection'
import { TranscriberConfigurationSection } from '@/components/agents/sections/TranscriberConfigurationSection'
import { ToolsConfigurationSection } from '@/components/agents/sections/ToolsConfigurationSection'
import { AnalyticsConfigurationSection } from '@/components/agents/sections/AnalyticsConfigurationSection'
import { AdvancedSettingsSection } from '@/components/agents/sections/AdvancedSettingsSection'
import { getDefaultModelConfig, getDefaultVoiceConfig, getDefaultTranscriberConfig, getDefaultToolsConfig, getDefaultAnalyticsConfig, getDefaultAdvancedSettings } from '@/components/agents/agent-form'
import { ModelConfiguration, VoiceConfiguration, TranscriberConfiguration, ToolsConfiguration, AnalyticsConfiguration, AdvancedSettings } from '@/types/agent'

export default function NewAgentPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('model')
  const [formData, setFormData] = useState({
    modelConfig: getDefaultModelConfig(),
    voiceConfig: getDefaultVoiceConfig(),
    transcriberConfig: getDefaultTranscriberConfig(),
    toolsConfig: getDefaultToolsConfig(),
    analyticsConfig: getDefaultAnalyticsConfig(),
    advancedSettings: getDefaultAdvancedSettings()
  })

  const handleSubmit = async (data: CreateAgentRequest) => {
    setLoading(true)
    
    try {
      // TODO: Implement real API call
      console.log('Creating agent:', data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect to agents list
      router.push('/agents')
    } catch (error) {
      console.error('Error creating agent:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleFormSubmit = async () => {
    const data: CreateAgentRequest = {
      name: '',
      description: '',
      system_prompt: '',
      voice: '',
      language: 'de-DE',
      status: 'active',
      modelConfig: formData.modelConfig,
      voiceConfig: formData.voiceConfig,
      transcriberConfig: formData.transcriberConfig,
      toolsConfig: formData.toolsConfig,
      analyticsConfig: formData.analyticsConfig,
      advancedSettings: formData.advancedSettings
    }
    await handleSubmit(data)
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title="Create New Agent" />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  <AgentTabNavigation
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                  />
                  <div className="mt-6">
                    {activeTab === 'model' && (
                      <ModelConfigurationSection
                        config={formData.modelConfig}
                        onChange={(config) => setFormData(prev => ({ ...prev, modelConfig: config }))}
                        errors={{}}
                      />
                    )}
                    {activeTab === 'voice' && (
                      <VoiceConfigurationSection
                        config={formData.voiceConfig}
                        language="de-DE"
                        onChange={(config) => setFormData(prev => ({ ...prev, voiceConfig: config }))}
                        errors={{}}
                      />
                    )}
                    {activeTab === 'transcriber' && (
                      <TranscriberConfigurationSection
                        config={formData.transcriberConfig}
                        onChange={(config) => setFormData(prev => ({ ...prev, transcriberConfig: config }))}
                        errors={{}}
                      />
                    )}
                    {activeTab === 'tools' && (
                      <ToolsConfigurationSection
                        config={formData.toolsConfig}
                        onChange={(config) => setFormData(prev => ({ ...prev, toolsConfig: config }))}
                        errors={{}}
                      />
                    )}
                    {activeTab === 'analytics' && (
                      <AnalyticsConfigurationSection
                        config={formData.analyticsConfig}
                        onChange={(config) => setFormData(prev => ({ ...prev, analyticsConfig: config }))}
                        errors={{}}
                      />
                    )}
                    {activeTab === 'advanced' && (
                      <AdvancedSettingsSection
                        config={formData.advancedSettings}
                        onChange={(config) => setFormData(prev => ({ ...prev, advancedSettings: config }))}
                        errors={{}}
                      />
                    )}
                  </div>
                  <div className="flex justify-end mt-6">
                    <button
                      type="button"
                      onClick={handleFormSubmit}
                      disabled={loading}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
                    >
                      {loading ? 'Speichern...' : 'Agent erstellen'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
