'use client'

import { useState, useEffect } from 'react'
import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { AgentList } from '@/components/agents/agent-list'
import { SiteHeader } from '@/components/dashboard/site-header'
import { Agent } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'

// Mock data - später durch echte API ersetzen
const mockAgents: Agent[] = [
  {
    id: '1',
    user_id: 'mock-user',
    created_at: '2025-01-29T10:00:00Z',
    updated_at: '2025-01-29T10:00:00Z',
    name: 'Customer Service Agent',
    description: 'Helps customers with general questions and problems',
    system_prompt: 'You are a friendly customer service representative who helps customers with their concerns.',
    voice: 'de-DE-KatjaNeural',
    language: 'de-DE',
    status: 'active'
  },
  {
    id: '2',
    user_id: 'mock-user',
    created_at: '2025-01-28T15:30:00Z',
    updated_at: '2025-01-28T15:30:00Z',
    name: 'Sales Assistant',
    description: 'Supports the sales process and answers product questions',
    system_prompt: 'You are a competent sales consultant who advises customers on product selection.',
    voice: 'de-DE-ConradNeural',
    language: 'de-DE',
    status: 'inactive'
  }
]

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simuliere API-Call
    const timer = setTimeout(() => {
      setAgents(mockAgents)
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="My Agents"
            showCreateButton={true}
            createButtonText="Create New Agent"
            createButtonHref="/agents/new"
            createButtonPosition="left"
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <AgentList agents={agents} loading={loading} />
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}