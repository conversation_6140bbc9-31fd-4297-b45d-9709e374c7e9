// Analytics & Reports Page - Story 4 Phase 3

'use client'

import { useState } from 'react'
import { AppSidebar } from "@/components/app-sidebar"
import { AuthGuard } from "@/components/auth-guard"
import { PeriodicReportDashboard } from "@/components/analytics/PeriodicReportDashboard"
import { SuccessRateAnalysisDashboard } from "@/components/analytics/SuccessRateAnalysisDashboard"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

type TabType = 'periodic-reports' | 'success-rate' | 'conversation-duration' | 'tool-usage'

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('periodic-reports')

  const tabs = [
    { id: 'periodic-reports', label: 'Periodische Reports' },
    { id: 'success-rate', label: 'Erfolgsrate-Analyse' },
    { id: 'conversation-duration', label: 'Gesprächsdauer-Stats' },
    { id: 'tool-usage', label: 'Tool-Nutzung' },
  ] as const

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "19rem",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          {/* Header */}
          <header className="flex h-16 shrink-0 items-center gap-2 px-4 border-b">
            <div className="flex items-center gap-2">
              <h1 className="text-xl font-semibold">Analytics & Reports</h1>
            </div>
          </header>

          {/* Main Content */}
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">

                {/* Navigation Tabs */}
                <div className="px-4 lg:px-6">
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                      {tabs.map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                            activeTab === tab.id
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          {tab.label}
                        </button>
                      ))}
                    </nav>
                  </div>
                </div>

                {/* Tab Content */}
                <div className="px-4 lg:px-6">
                  {activeTab === 'periodic-reports' && <PeriodicReportDashboard />}
                  {activeTab === 'success-rate' && <SuccessRateAnalysisDashboard />}
                  {activeTab === 'conversation-duration' && (
                    <div className="text-center py-12">
                      <p className="text-gray-500">Gesprächsdauer-Statistiken werden implementiert...</p>
                    </div>
                  )}
                  {activeTab === 'tool-usage' && (
                    <div className="text-center py-12">
                      <p className="text-gray-500">Tool-Nutzungs-Analytics werden implementiert...</p>
                    </div>
                  )}
                </div>

              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
