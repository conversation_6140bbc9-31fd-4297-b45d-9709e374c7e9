import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { Agent, CreateAgentRequest, AgentListResponse } from '@/types/agent'

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Fetch agents for the authenticated user
    const { data: agents, error } = await supabase
      .from('agents')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Database Error', message: 'Fehler beim Laden der Agenten' },
        { status: 500 }
      )
    }

    const response: AgentListResponse = {
      agents: agents || [],
      total: agents?.length || 0
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse request body
    const body: CreateAgentRequest = await request.json()

    // Validate required fields
    if (!body.name || !body.system_prompt || !body.voice || !body.language) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Name, System Prompt, Stimme und Sprache sind erforderlich' },
        { status: 400 }
      )
    }

    // Validate language
    if (!['de-DE', 'en-US'].includes(body.language)) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Ungültige Sprache' },
        { status: 400 }
      )
    }

    // Create agent
    const { data: agent, error } = await supabase
      .from('agents')
      .insert({
        user_id: session.user.id,
        name: body.name.trim(),
        description: body.description?.trim() || null,
        system_prompt: body.system_prompt.trim(),
        voice: body.voice,
        language: body.language,
        status: body.status || 'inactive'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Database Error', message: 'Fehler beim Erstellen des Agenten' },
        { status: 500 }
      )
    }

    return NextResponse.json({ agent }, { status: 201 })
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
