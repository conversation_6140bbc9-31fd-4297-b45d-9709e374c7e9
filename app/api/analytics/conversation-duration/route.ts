// API-Endpoint für Gesprächsdauer-Statistiken - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsService } from '@/services/analyticsService'
import { AnalyticsQuery } from '@/types/analytics'

const analyticsService = AnalyticsService.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Query-Parameter extrahieren
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const agentIds = searchParams.get('agentIds')?.split(',').filter(Boolean)
    const minDuration = searchParams.get('minDuration')
    const maxDuration = searchParams.get('maxDuration')

    // Validierung
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate und endDate sind erforderlich' },
        { status: 400 }
      )
    }

    // Query-Objekt erstellen
    const query: AnalyticsQuery = {
      timeRange: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        period: 'custom'
      },
      agentIds,
      filters: {
        minDuration: minDuration ? parseInt(minDuration) : undefined,
        maxDuration: maxDuration ? parseInt(maxDuration) : undefined
      }
    }

    // Validierung der Datumsangaben
    if (isNaN(query.timeRange.startDate.getTime()) || isNaN(query.timeRange.endDate.getTime())) {
      return NextResponse.json(
        { error: 'Ungültige Datumsangaben' },
        { status: 400 }
      )
    }

    if (query.timeRange.startDate >= query.timeRange.endDate) {
      return NextResponse.json(
        { error: 'startDate muss vor endDate liegen' },
        { status: 400 }
      )
    }

    // Validierung der Dauer-Filter
    if (query.filters?.minDuration && query.filters.minDuration < 0) {
      return NextResponse.json(
        { error: 'minDuration muss >= 0 sein' },
        { status: 400 }
      )
    }

    if (query.filters?.maxDuration && query.filters.maxDuration < 0) {
      return NextResponse.json(
        { error: 'maxDuration muss >= 0 sein' },
        { status: 400 }
      )
    }

    if (query.filters?.minDuration && query.filters?.maxDuration && 
        query.filters.minDuration >= query.filters.maxDuration) {
      return NextResponse.json(
        { error: 'minDuration muss kleiner als maxDuration sein' },
        { status: 400 }
      )
    }

    // Gesprächsdauer-Statistiken generieren
    const response = await analyticsService.generateDurationStats(query)

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=1200', // 20 Minuten Cache
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('Fehler bei den Gesprächsdauer-Statistiken:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler bei den Gesprächsdauer-Statistiken',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
