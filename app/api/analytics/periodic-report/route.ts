// API-Endpoint für periodische Reports - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsService } from '@/services/analyticsService'
import { AnalyticsQuery } from '@/types/analytics'

const analyticsService = AnalyticsService.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Query-Parameter extrahieren
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const period = searchParams.get('period') as 'daily' | 'weekly' | 'monthly' | 'custom'
    const agentIds = searchParams.get('agentIds')?.split(',').filter(Boolean)

    // Validierung
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate und endDate sind erforderlich' },
        { status: 400 }
      )
    }

    if (!period || !['daily', 'weekly', 'monthly', 'custom'].includes(period)) {
      return NextResponse.json(
        { error: 'Gültiger period-Parameter ist erforderlich (daily, weekly, monthly, custom)' },
        { status: 400 }
      )
    }

    // Query-Objekt erstellen
    const query: AnalyticsQuery = {
      timeRange: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        period
      },
      agentIds
    }

    // Validierung der Datumsangaben
    if (isNaN(query.timeRange.startDate.getTime()) || isNaN(query.timeRange.endDate.getTime())) {
      return NextResponse.json(
        { error: 'Ungültige Datumsangaben' },
        { status: 400 }
      )
    }

    if (query.timeRange.startDate >= query.timeRange.endDate) {
      return NextResponse.json(
        { error: 'startDate muss vor endDate liegen' },
        { status: 400 }
      )
    }

    // Maximaler Zeitraum prüfen (z.B. 1 Jahr)
    const maxDays = 365
    const daysDiff = Math.ceil((query.timeRange.endDate.getTime() - query.timeRange.startDate.getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > maxDays) {
      return NextResponse.json(
        { error: `Zeitraum darf maximal ${maxDays} Tage betragen` },
        { status: 400 }
      )
    }

    // Report generieren
    const response = await analyticsService.generatePeriodicReport(query)

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=900', // 15 Minuten Cache
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('Fehler beim Generieren des periodischen Reports:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler beim Generieren des Reports',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// OPTIONS für CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
