// API-Endpoint für Tool-Nutzungs-Analytics - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { AnalyticsService } from '@/services/analyticsService'
import { AnalyticsQuery } from '@/types/analytics'

const analyticsService = AnalyticsService.getInstance()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Query-Parameter extrahieren
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const agentIds = searchParams.get('agentIds')?.split(',').filter(Boolean)
    const toolNames = searchParams.get('toolNames')?.split(',').filter(Boolean)

    // Validierung
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate und endDate sind erforderlich' },
        { status: 400 }
      )
    }

    // Query-Objekt erstellen
    const query: AnalyticsQuery = {
      timeRange: {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        period: 'custom'
      },
      agentIds,
      filters: {
        toolNames
      }
    }

    // Validierung der Datumsangaben
    if (isNaN(query.timeRange.startDate.getTime()) || isNaN(query.timeRange.endDate.getTime())) {
      return NextResponse.json(
        { error: 'Ungültige Datumsangaben' },
        { status: 400 }
      )
    }

    if (query.timeRange.startDate >= query.timeRange.endDate) {
      return NextResponse.json(
        { error: 'startDate muss vor endDate liegen' },
        { status: 400 }
      )
    }

    // Tool-Nutzungs-Analytics generieren
    const response = await analyticsService.generateToolUsageAnalytics(query)

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=1800', // 30 Minuten Cache
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('Fehler bei der Tool-Nutzungs-Analyse:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler bei der Tool-Nutzungs-Analyse',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
