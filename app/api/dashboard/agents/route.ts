import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { AgentDashboardData, DashboardAgentsResponse, DashboardSummary } from '@/types/dashboard'

// Mock-Daten für die initiale Implementierung
// TODO: Später durch echte Datenbank-Abfragen ersetzen
function generateMockDashboardData(): AgentDashboardData[] {
  const mockAgents: AgentDashboardData[] = [
    {
      id: '1',
      name: 'Customer Service Agent',
      avatar: undefined,
      status: 'active',
      currentCalls: 2,
      lastActivity: new Date(Date.now() - 5 * 60 * 1000), // 5 Minuten ago
      todayMetrics: {
        totalCalls: 45,
        successfulCalls: 42,
        successRate: 93.3,
        averageDuration: 180, // 3 minutes
        utilization: 75.5
      },
      yesterdayComparison: {
        callsChange: 12.5,
        successRateChange: 2.1,
        durationChange: -8.3
      }
    },
    {
      id: '2',
      name: 'Sales Assistant',
      avatar: undefined,
      status: 'idle',
      currentCalls: 0,
      lastActivity: new Date(Date.now() - 15 * 60 * 1000), // 15 Minuten ago
      todayMetrics: {
        totalCalls: 28,
        successfulCalls: 25,
        successRate: 89.3,
        averageDuration: 240, // 4 minutes
        utilization: 45.2
      },
      yesterdayComparison: {
        callsChange: -5.2,
        successRateChange: 1.8,
        durationChange: 15.7
      }
    },
    {
      id: '3',
      name: 'Technical Support',
      avatar: undefined,
      status: 'error',
      currentCalls: 0,
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 Stunden ago
      todayMetrics: {
        totalCalls: 12,
        successfulCalls: 8,
        successRate: 66.7,
        averageDuration: 420, // 7 minutes
        utilization: 25.0
      },
      yesterdayComparison: {
        callsChange: -25.0,
        successRateChange: -15.5,
        durationChange: 35.2
      }
    },
    {
      id: '4',
      name: 'Order Processing',
      avatar: undefined,
      status: 'inactive',
      currentCalls: 0,
      lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 Stunden ago
      todayMetrics: {
        totalCalls: 0,
        successfulCalls: 0,
        successRate: 0,
        averageDuration: 0,
        utilization: 0
      },
      yesterdayComparison: {
        callsChange: -100,
        successRateChange: 0,
        durationChange: 0
      }
    }
  ]

  return mockAgents
}

function generateMockSummary(agents: AgentDashboardData[]): DashboardSummary {
  const activeAgents = agents.filter(a => a.status === 'active').length
  const totalCalls = agents.reduce((sum, agent) => sum + agent.todayMetrics.totalCalls, 0)
  const totalSuccessful = agents.reduce((sum, agent) => sum + agent.todayMetrics.successfulCalls, 0)
  const averageSuccessRate = totalCalls > 0 ? (totalSuccessful / totalCalls) * 100 : 0
  const totalUtilization = agents.reduce((sum, agent) => sum + agent.todayMetrics.utilization, 0) / agents.length

  return {
    totalAgents: agents.length,
    activeAgents,
    totalCallsToday: totalCalls,
    averageSuccessRate,
    totalUtilization,
    lastUpdated: new Date()
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const sortBy = searchParams.get('sortBy') || 'name'
    const sortOrder = searchParams.get('sortOrder') || 'asc'

    // TODO: Später durch echte Datenbank-Abfragen ersetzen
    // Für jetzt verwenden wir Mock-Daten
    let agents = generateMockDashboardData()

    // Filter anwenden
    if (search) {
      agents = agents.filter(agent => 
        agent.name.toLowerCase().includes(search.toLowerCase()) ||
        agent.id.includes(search)
      )
    }

    if (status !== 'all') {
      agents = agents.filter(agent => agent.status === status)
    }

    // Sortierung anwenden
    agents.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'status':
          aValue = a.status
          bValue = b.status
          break
        case 'activity':
          aValue = a.lastActivity.getTime()
          bValue = b.lastActivity.getTime()
          break
        case 'performance':
          aValue = a.todayMetrics.successRate
          bValue = b.todayMetrics.successRate
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (sortOrder === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }
    })

    const summary = generateMockSummary(agents)

    const response: DashboardAgentsResponse = {
      agents,
      summary,
      total: agents.length
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Dashboard API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}

// POST endpoint für zukünftige Aktionen (z.B. Agent-Status ändern)
export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, agentId, data } = body

    // TODO: Implementiere verschiedene Aktionen
    switch (action) {
      case 'update_status':
        // Placeholder für Status-Update
        return NextResponse.json({ success: true, message: 'Status aktualisiert' })
      
      default:
        return NextResponse.json(
          { error: 'Bad Request', message: 'Unbekannte Aktion' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Dashboard POST API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
