import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { DashboardSummary } from '@/types/dashboard'

// Mock-Daten für die initiale Implementierung
function generateMockSummary(): DashboardSummary {
  return {
    totalAgents: 4,
    activeAgents: 1,
    totalCallsToday: 85,
    averageSuccessRate: 84.7,
    totalUtilization: 36.4,
    lastUpdated: new Date()
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // TODO: Später durch echte Datenbank-Abfragen ersetzen
    // Für jetzt verwenden wir Mock-Daten
    const summary = generateMockSummary()

    return NextResponse.json(summary)
  } catch (error) {
    console.error('Dashboard Summary API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
