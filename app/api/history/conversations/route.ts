import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { 
  ConversationHistoryItem, 
  ConversationHistoryResponse,
  ConversationFilters,
  DEFAULT_FILTERS
} from '@/types/history'

// Mock-Daten für die initiale Implementierung
function generateMockConversations(): ConversationHistoryItem[] {
  const mockConversations: ConversationHistoryItem[] = [
    {
      id: 'conv-hist-001',
      startTime: new Date('2025-01-29T10:30:00Z'),
      endTime: new Date('2025-01-29T10:45:00Z'),
      duration: 900, // 15 Minuten
      agentId: '1',
      agentName: 'Customer Service Agent',
      callerId: 'caller-001',
      callerInfo: {
        name: '<PERSON>',
        phone: '+49 30 12345678',
        email: '<EMAIL>',
        location: 'Berlin, Deutschland'
      },
      status: 'completed',
      rating: 5,
      summary: 'Kunde hatte Fragen zur Bestellung #12345. Problem erfolgreich gelöst.',
      tags: ['support', 'order-inquiry', 'resolved'],
      toolsUsed: ['OrderLookup', 'CustomerDatabase'],
      metadata: {
        department: 'Customer Service',
        priority: 'medium',
        category: 'Order Support'
      }
    },
    {
      id: 'conv-hist-002',
      startTime: new Date('2025-01-29T09:15:00Z'),
      endTime: new Date('2025-01-29T09:18:00Z'),
      duration: 180, // 3 Minuten
      agentId: '2',
      agentName: 'Sales Assistant',
      callerId: 'caller-002',
      callerInfo: {
        name: 'Anna Schmidt',
        phone: '+49 40 98765432',
        location: 'Hamburg, Deutschland'
      },
      status: 'abandoned',
      rating: undefined,
      summary: 'Kunde hat während der Wartezeit aufgelegt.',
      tags: ['sales', 'abandoned'],
      toolsUsed: [],
      metadata: {
        department: 'Sales',
        priority: 'low',
        category: 'Sales Inquiry'
      }
    },
    {
      id: 'conv-hist-003',
      startTime: new Date('2025-01-29T08:45:00Z'),
      endTime: new Date('2025-01-29T09:12:00Z'),
      duration: 1620, // 27 Minuten
      agentId: '3',
      agentName: 'Technical Support',
      callerId: 'caller-003',
      callerInfo: {
        name: 'Thomas Weber',
        phone: '+49 89 55566677',
        email: '<EMAIL>',
        location: 'München, Deutschland'
      },
      status: 'transferred',
      rating: 3,
      summary: 'Technisches Problem konnte nicht gelöst werden. An Spezialist weitergeleitet.',
      tags: ['technical', 'escalated', 'complex'],
      toolsUsed: ['TechnicalDiagnostics', 'RemoteAccess', 'TicketSystem'],
      metadata: {
        department: 'Technical Support',
        priority: 'high',
        category: 'Technical Issue'
      }
    },
    {
      id: 'conv-hist-004',
      startTime: new Date('2025-01-28T16:20:00Z'),
      endTime: new Date('2025-01-28T16:22:00Z'),
      duration: 120, // 2 Minuten
      agentId: '1',
      agentName: 'Customer Service Agent',
      callerId: 'caller-004',
      callerInfo: {
        name: 'Maria Gonzalez',
        phone: '+49 711 123456',
        location: 'Stuttgart, Deutschland'
      },
      status: 'failed',
      rating: undefined,
      summary: 'Verbindungsfehler während des Gesprächs.',
      tags: ['technical-issue', 'connection-failed'],
      toolsUsed: [],
      metadata: {
        department: 'Customer Service',
        priority: 'medium',
        category: 'Technical Issue'
      }
    },
    {
      id: 'conv-hist-005',
      startTime: new Date('2025-01-28T14:10:00Z'),
      endTime: new Date('2025-01-28T14:25:00Z'),
      duration: 900, // 15 Minuten
      agentId: '2',
      agentName: 'Sales Assistant',
      callerId: 'caller-005',
      callerInfo: {
        name: 'Peter Müller',
        phone: '+49 221 987654',
        email: '<EMAIL>',
        location: 'Köln, Deutschland'
      },
      status: 'completed',
      rating: 4,
      summary: 'Erfolgreiche Beratung zu Premium-Paketen. Kunde interessiert.',
      tags: ['sales', 'premium', 'interested'],
      toolsUsed: ['ProductCatalog', 'PricingCalculator'],
      metadata: {
        department: 'Sales',
        priority: 'high',
        category: 'Sales Consultation'
      }
    }
  ]

  return mockConversations
}

function applyFilters(
  conversations: ConversationHistoryItem[], 
  filters: Partial<ConversationFilters>
): ConversationHistoryItem[] {
  let filtered = [...conversations]

  // Date Range Filter
  if (filters.dateRange) {
    filtered = filtered.filter(conv => 
      conv.startTime >= filters.dateRange!.start && 
      conv.startTime <= filters.dateRange!.end
    )
  }

  // Agent Filter
  if (filters.agentIds && filters.agentIds.length > 0) {
    filtered = filtered.filter(conv => 
      filters.agentIds!.includes(conv.agentId)
    )
  }

  // Status Filter
  if (filters.status && filters.status.length > 0) {
    filtered = filtered.filter(conv => 
      filters.status!.includes(conv.status)
    )
  }

  // Duration Range Filter
  if (filters.durationRange) {
    filtered = filtered.filter(conv => 
      conv.duration >= filters.durationRange!.min && 
      conv.duration <= filters.durationRange!.max
    )
  }

  // Search Filter
  if (filters.search && filters.search.trim()) {
    const searchTerm = filters.search.toLowerCase()
    filtered = filtered.filter(conv => 
      conv.agentName.toLowerCase().includes(searchTerm) ||
      conv.callerInfo?.name?.toLowerCase().includes(searchTerm) ||
      conv.callerInfo?.phone?.includes(searchTerm) ||
      conv.summary?.toLowerCase().includes(searchTerm) ||
      conv.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  // Tags Filter
  if (filters.tags && filters.tags.length > 0) {
    filtered = filtered.filter(conv => 
      filters.tags!.some(tag => conv.tags.includes(tag))
    )
  }

  return filtered
}

function applySorting(
  conversations: ConversationHistoryItem[],
  sortBy: ConversationFilters['sortBy'] = 'startTime',
  sortOrder: ConversationFilters['sortOrder'] = 'desc'
): ConversationHistoryItem[] {
  return [...conversations].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'startTime':
        aValue = a.startTime.getTime()
        bValue = b.startTime.getTime()
        break
      case 'duration':
        aValue = a.duration
        bValue = b.duration
        break
      case 'agentName':
        aValue = a.agentName.toLowerCase()
        bValue = b.agentName.toLowerCase()
        break
      case 'rating':
        aValue = a.rating || 0
        bValue = b.rating || 0
        break
      default:
        aValue = a.startTime.getTime()
        bValue = b.startTime.getTime()
    }

    if (sortOrder === 'desc') {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })
}

function applyPagination(
  conversations: ConversationHistoryItem[],
  page: number = 1,
  pageSize: number = 50
): {
  conversations: ConversationHistoryItem[]
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
} {
  const total = conversations.length
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize

  return {
    conversations: conversations.slice(startIndex, endIndex),
    totalPages,
    hasNext: page < totalPages,
    hasPrevious: page > 1
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    
    const filters: Partial<ConversationFilters> = {
      search: searchParams.get('search') || '',
      agentIds: searchParams.get('agentIds')?.split(',').filter(Boolean) || [],
      status: searchParams.get('status')?.split(',').filter(Boolean) as any[] || [],
      sortBy: (searchParams.get('sortBy') as ConversationFilters['sortBy']) || 'startTime',
      sortOrder: (searchParams.get('sortOrder') as ConversationFilters['sortOrder']) || 'desc',
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('pageSize') || '50')
    }

    // Parse date range
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    if (startDate && endDate) {
      filters.dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    }

    // Parse duration range
    const minDuration = searchParams.get('minDuration')
    const maxDuration = searchParams.get('maxDuration')
    if (minDuration || maxDuration) {
      filters.durationRange = {
        min: minDuration ? parseInt(minDuration) : 0,
        max: maxDuration ? parseInt(maxDuration) : Infinity
      }
    }

    // Parse tags
    const tags = searchParams.get('tags')
    if (tags) {
      filters.tags = tags.split(',').filter(Boolean)
    }

    // TODO: Später durch echte Datenbank-Abfragen ersetzen
    let conversations = generateMockConversations()

    // Apply filters
    conversations = applyFilters(conversations, filters)

    // Apply sorting
    conversations = applySorting(conversations, filters.sortBy, filters.sortOrder)

    // Apply pagination
    const paginationResult = applyPagination(conversations, filters.page, filters.pageSize)

    const response: ConversationHistoryResponse = {
      conversations: paginationResult.conversations,
      total: conversations.length,
      totalPages: paginationResult.totalPages,
      currentPage: filters.page || 1,
      hasNext: paginationResult.hasNext,
      hasPrevious: paginationResult.hasPrevious
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('History API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
