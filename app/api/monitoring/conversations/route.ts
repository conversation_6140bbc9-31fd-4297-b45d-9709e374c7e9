import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { LiveConversation, MonitoringApiResponse } from '@/types/monitoring'

// Mock-Daten für die initiale Implementierung
function generateMockConversations(): LiveConversation[] {
  const mockConversations: LiveConversation[] = [
    {
      id: 'conv-001',
      agentId: '1',
      agentName: 'Customer Service Agent',
      agentAvatar: undefined,
      callerId: 'caller-001',
      callerInfo: {
        name: '<PERSON>',
        phone: '+49 30 12345678',
        location: 'Berlin, Deutschland'
      },
      status: 'active',
      startTime: new Date(Date.now() - 5 * 60 * 1000), // 5 Minuten ago
      duration: 300, // 5 Minuten
      lastActivity: new Date(Date.now() - 10 * 1000), // 10 Sekunden ago
      transcript: [
        {
          id: 'msg-001',
          timestamp: new Date(Date.now() - 4 * 60 * 1000),
          speaker: 'agent',
          content: 'Guten Tag! Wie kann ich Ihnen heute helfen?',
          confidence: 0.95,
          sentiment: 'positive',
          eventType: 'message'
        },
        {
          id: 'msg-002',
          timestamp: new Date(Date.now() - 3 * 60 * 1000),
          speaker: 'caller',
          content: 'Hallo, ich habe ein Problem mit meiner Bestellung.',
          confidence: 0.92,
          sentiment: 'neutral',
          eventType: 'message'
        },
        {
          id: 'msg-003',
          timestamp: new Date(Date.now() - 2 * 60 * 1000),
          speaker: 'system',
          content: 'Bestellsystem wurde geöffnet',
          eventType: 'tool_use',
          metadata: {
            toolName: 'OrderLookup'
          }
        },
        {
          id: 'msg-004',
          timestamp: new Date(Date.now() - 1 * 60 * 1000),
          speaker: 'agent',
          content: 'Ich kann Ihre Bestellung #12345 sehen. Was genau ist das Problem?',
          confidence: 0.97,
          sentiment: 'neutral',
          eventType: 'message'
        }
      ],
      qualityMetrics: {
        connectionQuality: 4,
        latency: 120,
        audioLevel: {
          agent: 75,
          caller: 68
        }
      },
      metadata: {
        priority: 'medium',
        tags: ['support', 'order-issue'],
        department: 'Customer Service'
      }
    },
    {
      id: 'conv-002',
      agentId: '2',
      agentName: 'Sales Assistant',
      agentAvatar: undefined,
      callerId: 'caller-002',
      callerInfo: {
        name: 'Anna Schmidt',
        phone: '+49 40 98765432',
        location: 'Hamburg, Deutschland'
      },
      status: 'on-hold',
      startTime: new Date(Date.now() - 8 * 60 * 1000), // 8 Minuten ago
      duration: 480, // 8 Minuten
      lastActivity: new Date(Date.now() - 2 * 60 * 1000), // 2 Minuten ago
      transcript: [
        {
          id: 'msg-005',
          timestamp: new Date(Date.now() - 7 * 60 * 1000),
          speaker: 'agent',
          content: 'Willkommen! Interessieren Sie sich für unsere Produkte?',
          confidence: 0.94,
          sentiment: 'positive',
          eventType: 'message'
        },
        {
          id: 'msg-006',
          timestamp: new Date(Date.now() - 6 * 60 * 1000),
          speaker: 'caller',
          content: 'Ja, ich möchte mehr über Ihre Premium-Pakete erfahren.',
          confidence: 0.91,
          sentiment: 'positive',
          eventType: 'message'
        },
        {
          id: 'msg-007',
          timestamp: new Date(Date.now() - 2 * 60 * 1000),
          speaker: 'system',
          content: 'Gespräch pausiert - Kunde in Warteschleife',
          eventType: 'hold'
        }
      ],
      qualityMetrics: {
        connectionQuality: 5,
        latency: 85,
        audioLevel: {
          agent: 82,
          caller: 79
        }
      },
      metadata: {
        priority: 'high',
        tags: ['sales', 'premium'],
        department: 'Sales'
      }
    },
    {
      id: 'conv-003',
      agentId: '3',
      agentName: 'Technical Support',
      agentAvatar: undefined,
      callerId: 'caller-003',
      callerInfo: {
        name: 'Thomas Weber',
        phone: '+49 89 55566677',
        location: 'München, Deutschland'
      },
      status: 'connecting',
      startTime: new Date(Date.now() - 30 * 1000), // 30 Sekunden ago
      duration: 30,
      lastActivity: new Date(Date.now() - 5 * 1000), // 5 Sekunden ago
      transcript: [
        {
          id: 'msg-008',
          timestamp: new Date(Date.now() - 20 * 1000),
          speaker: 'system',
          content: 'Verbindung wird aufgebaut...',
          eventType: 'system'
        }
      ],
      qualityMetrics: {
        connectionQuality: 3,
        latency: 200,
        audioLevel: {
          agent: 0,
          caller: 45
        }
      },
      metadata: {
        priority: 'medium',
        tags: ['technical', 'support'],
        department: 'Technical Support'
      }
    }
  ]

  return mockConversations
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const agent = searchParams.get('agent') || 'all'
    const priority = searchParams.get('priority') || 'all'
    const sortBy = searchParams.get('sortBy') || 'duration'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // TODO: Später durch echte Datenbank-Abfragen ersetzen
    let conversations = generateMockConversations()

    // Filter anwenden
    if (search) {
      conversations = conversations.filter(conv => 
        conv.agentName.toLowerCase().includes(search.toLowerCase()) ||
        conv.callerInfo?.name?.toLowerCase().includes(search.toLowerCase()) ||
        conv.id.includes(search)
      )
    }

    if (status !== 'all') {
      conversations = conversations.filter(conv => conv.status === status)
    }

    if (agent !== 'all') {
      conversations = conversations.filter(conv => conv.agentId === agent)
    }

    if (priority !== 'all') {
      conversations = conversations.filter(conv => conv.metadata.priority === priority)
    }

    // Sortierung anwenden
    conversations.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'duration':
          aValue = a.duration
          bValue = b.duration
          break
        case 'startTime':
          aValue = a.startTime.getTime()
          bValue = b.startTime.getTime()
          break
        case 'agentName':
          aValue = a.agentName.toLowerCase()
          bValue = b.agentName.toLowerCase()
          break
        case 'priority':
          const priorityOrder = { low: 0, medium: 1, high: 2 }
          aValue = priorityOrder[a.metadata.priority as keyof typeof priorityOrder]
          bValue = priorityOrder[b.metadata.priority as keyof typeof priorityOrder]
          break
        default:
          aValue = a.duration
          bValue = b.duration
      }

      if (sortOrder === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }
    })

    const response: MonitoringApiResponse = {
      conversations,
      total: conversations.length,
      timestamp: new Date()
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Monitoring API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}

// POST endpoint für Aktionen (z.B. Gesprächsübernahme)
export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, conversationId, data } = body

    // TODO: Implementiere verschiedene Aktionen
    switch (action) {
      case 'takeover':
        // Placeholder für Gesprächsübernahme
        return NextResponse.json({ 
          success: true, 
          message: 'Gespräch erfolgreich übernommen',
          conversationId,
          timestamp: new Date()
        })
      
      case 'get_details':
        // Placeholder für Gesprächsdetails
        const mockConversation = generateMockConversations().find(c => c.id === conversationId)
        if (!mockConversation) {
          return NextResponse.json(
            { error: 'Not Found', message: 'Gespräch nicht gefunden' },
            { status: 404 }
          )
        }
        return NextResponse.json({ conversation: mockConversation })
      
      default:
        return NextResponse.json(
          { error: 'Bad Request', message: 'Unbekannte Aktion' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring POST API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
