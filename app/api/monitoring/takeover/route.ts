import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { TakeoverRequest, TakeoverResponse } from '@/types/monitoring'

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse request body
    const takeoverRequest: Omit<TakeoverRequest, 'supervisorId' | 'timestamp'> = await request.json()

    // Validate required fields
    if (!takeoverRequest.conversationId || !takeoverRequest.reason) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Gesprächs-ID und Grund sind erforderlich' },
        { status: 400 }
      )
    }

    // Validate reason
    const validReasons = ['quality_issue', 'escalation', 'training', 'technical_problem', 'other']
    if (!validReasons.includes(takeoverRequest.reason)) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Ungültiger Übernahmegrund' },
        { status: 400 }
      )
    }

    // Create complete takeover request
    const completeTakeoverRequest: TakeoverRequest = {
      ...takeoverRequest,
      supervisorId: user.id,
      timestamp: new Date()
    }

    // TODO: Implementiere echte Übernahme-Logik
    // Für jetzt simulieren wir eine erfolgreiche Übernahme
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Log takeover attempt
    console.log('Takeover request:', completeTakeoverRequest)

    // TODO: Später durch echte Datenbank-Operationen ersetzen
    // - Gespräch in Datenbank aktualisieren
    // - WebSocket-Event an alle Clients senden
    // - Benachrichtigung an ursprünglichen Agent
    // - Audit-Log erstellen

    const response: TakeoverResponse = {
      success: true,
      message: 'Gespräch erfolgreich übernommen',
      conversationId: takeoverRequest.conversationId,
      timestamp: new Date()
    }

    return NextResponse.json(response, { status: 200 })

  } catch (error) {
    console.error('Takeover API error:', error)
    
    // Return appropriate error response
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Ungültiges JSON-Format' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}

// GET endpoint für Übernahme-Historie
export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authorization header fehlt' },
        { status: 401 }
      )
    }

    // Set auth header for supabase client
    supabase.auth.setSession({
      access_token: authorization.replace('Bearer ', ''),
      refresh_token: ''
    })

    // Get user from session
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Benutzer nicht authentifiziert' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const conversationId = searchParams.get('conversationId')
    const supervisorId = searchParams.get('supervisorId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // TODO: Implementiere echte Datenbank-Abfrage
    // Für jetzt Mock-Daten zurückgeben
    const mockTakeovers = [
      {
        id: 'takeover-001',
        conversationId: 'conv-001',
        supervisorId: user.id,
        supervisorName: 'Admin User',
        reason: 'quality_issue',
        note: 'Kunde war unzufrieden mit der Antwort',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 Stunden ago
        originalAgentId: '1',
        originalAgentName: 'Customer Service Agent'
      },
      {
        id: 'takeover-002',
        conversationId: 'conv-002',
        supervisorId: user.id,
        supervisorName: 'Admin User',
        reason: 'escalation',
        note: 'Kunde möchte mit Manager sprechen',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 Stunden ago
        originalAgentId: '2',
        originalAgentName: 'Sales Assistant'
      }
    ]

    // Filter anwenden
    let filteredTakeovers = mockTakeovers

    if (conversationId) {
      filteredTakeovers = filteredTakeovers.filter(t => t.conversationId === conversationId)
    }

    if (supervisorId) {
      filteredTakeovers = filteredTakeovers.filter(t => t.supervisorId === supervisorId)
    }

    // Paginierung anwenden
    const paginatedTakeovers = filteredTakeovers.slice(offset, offset + limit)

    return NextResponse.json({
      takeovers: paginatedTakeovers,
      total: filteredTakeovers.length,
      limit,
      offset,
      hasMore: offset + limit < filteredTakeovers.length
    })

  } catch (error) {
    console.error('Takeover GET API error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Ein unerwarteter Fehler ist aufgetreten' },
      { status: 500 }
    )
  }
}
