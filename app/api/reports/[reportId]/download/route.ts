// API-Endpoint für Report-Download - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { ReportService } from '@/services/reportService'
import { supabase } from '@/lib/supabase'

const reportService = ReportService.getInstance()

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  try {
    const { reportId } = params

    // ReportId validieren
    if (!reportId || typeof reportId !== 'string') {
      return NextResponse.json(
        { error: 'Gültige reportId ist erforderlich' },
        { status: 400 }
      )
    }

    // Report-Status prüfen
    const status = await reportService.getReportStatus(reportId)

    if (!status) {
      return NextResponse.json(
        { error: 'Report nicht gefunden' },
        { status: 404 }
      )
    }

    if (status.status !== 'completed') {
      return NextResponse.json(
        { 
          error: 'Report ist noch nicht fertig',
          status: status.status,
          progress: status.progress
        },
        { status: 202 } // Accepted - noch in Bearbeitung
      )
    }

    if (!status.downloadUrl) {
      return NextResponse.json(
        { error: 'Download-URL nicht verfügbar' },
        { status: 500 }
      )
    }

    // Prüfen ob Report abgelaufen ist
    if (status.expiresAt && new Date() > status.expiresAt) {
      return NextResponse.json(
        { error: 'Report ist abgelaufen' },
        { status: 410 } // Gone
      )
    }

    // Report-Datei aus Supabase Storage abrufen
    const filename = status.downloadUrl.split('/').pop()
    if (!filename) {
      return NextResponse.json(
        { error: 'Ungültige Download-URL' },
        { status: 500 }
      )
    }

    const { data: fileData, error: downloadError } = await supabase.storage
      .from('reports')
      .download(filename)

    if (downloadError) {
      console.error('Fehler beim Download der Report-Datei:', downloadError)
      return NextResponse.json(
        { error: 'Fehler beim Download der Report-Datei' },
        { status: 500 }
      )
    }

    // Content-Type basierend auf Dateiendung bestimmen
    const contentType = getContentType(filename)
    
    // Datei als Response zurückgeben
    return new NextResponse(fileData, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'private, max-age=3600', // 1 Stunde Cache
        'Content-Length': fileData.size.toString()
      }
    })

  } catch (error) {
    console.error('Fehler beim Report-Download:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler beim Report-Download',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// Hilfsfunktion für Content-Type
function getContentType(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase()
  
  switch (extension) {
    case 'pdf':
      return 'application/pdf'
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    case 'csv':
      return 'text/csv; charset=utf-8'
    default:
      return 'application/octet-stream'
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
