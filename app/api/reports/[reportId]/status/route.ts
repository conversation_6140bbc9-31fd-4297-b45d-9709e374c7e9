// API-Endpoint für Report-Status - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { ReportService } from '@/services/reportService'

const reportService = ReportService.getInstance()

export async function GET(
  request: NextRequest,
  { params }: { params: { reportId: string } }
) {
  try {
    const { reportId } = params

    // ReportId validieren
    if (!reportId || typeof reportId !== 'string') {
      return NextResponse.json(
        { error: 'Gültige reportId ist erforderlich' },
        { status: 400 }
      )
    }

    // Report-Status abrufen
    const status = await reportService.getReportStatus(reportId)

    if (!status) {
      return NextResponse.json(
        { error: 'Report nicht gefunden' },
        { status: 404 }
      )
    }

    return NextResponse.json(status, {
      headers: {
        'Content-Type': 'application/json',
        // Kein Cache für Status-Abfragen
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })

  } catch (error) {
    console.error('<PERSON><PERSON> beim Abrufen des Report-Status:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler beim Abrufen des Report-Status',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
