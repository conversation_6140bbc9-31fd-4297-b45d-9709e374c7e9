// API-Endpoint für Report-Generierung - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { ReportService } from '@/services/reportService'
import { ReportGenerationRequest } from '@/types/analytics'

const reportService = ReportService.getInstance()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Request-Validierung
    const validationResult = validateReportRequest(body)
    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: 'Ungültige Request-Daten', details: validationResult.errors },
        { status: 400 }
      )
    }

    const reportRequest: ReportGenerationRequest = body

    // Report-Generierung starten
    const response = await reportService.generateReport(reportRequest)

    return NextResponse.json(response, {
      status: 202, // Accepted - Asynchrone Verarbeitung
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('<PERSON>hler bei der Report-Generierung:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler bei der Report-Generierung',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// Validierungsfunktion
function validateReportRequest(body: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // reportType validieren
  if (!body.reportType) {
    errors.push('reportType ist erforderlich')
  } else if (!['periodic', 'success_rate', 'duration_stats', 'tool_usage'].includes(body.reportType)) {
    errors.push('reportType muss einer von: periodic, success_rate, duration_stats, tool_usage sein')
  }

  // timeRange validieren
  if (!body.timeRange) {
    errors.push('timeRange ist erforderlich')
  } else {
    if (!body.timeRange.startDate) {
      errors.push('timeRange.startDate ist erforderlich')
    } else {
      const startDate = new Date(body.timeRange.startDate)
      if (isNaN(startDate.getTime())) {
        errors.push('timeRange.startDate muss ein gültiges Datum sein')
      }
    }

    if (!body.timeRange.endDate) {
      errors.push('timeRange.endDate ist erforderlich')
    } else {
      const endDate = new Date(body.timeRange.endDate)
      if (isNaN(endDate.getTime())) {
        errors.push('timeRange.endDate muss ein gültiges Datum sein')
      }
    }

    if (!body.timeRange.period) {
      errors.push('timeRange.period ist erforderlich')
    } else if (!['daily', 'weekly', 'monthly', 'custom'].includes(body.timeRange.period)) {
      errors.push('timeRange.period muss einer von: daily, weekly, monthly, custom sein')
    }

    // Datumsbereich validieren
    if (body.timeRange.startDate && body.timeRange.endDate) {
      const startDate = new Date(body.timeRange.startDate)
      const endDate = new Date(body.timeRange.endDate)
      
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        if (startDate >= endDate) {
          errors.push('timeRange.startDate muss vor timeRange.endDate liegen')
        }

        // Maximaler Zeitraum (1 Jahr)
        const maxDays = 365
        const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
        if (daysDiff > maxDays) {
          errors.push(`Zeitraum darf maximal ${maxDays} Tage betragen`)
        }
      }
    }
  }

  // config validieren
  if (!body.config) {
    errors.push('config ist erforderlich')
  } else {
    if (!body.config.format) {
      errors.push('config.format ist erforderlich')
    } else if (!['pdf', 'excel', 'csv'].includes(body.config.format)) {
      errors.push('config.format muss einer von: pdf, excel, csv sein')
    }

    if (!body.config.language) {
      errors.push('config.language ist erforderlich')
    } else if (!['de', 'en'].includes(body.config.language)) {
      errors.push('config.language muss einer von: de, en sein')
    }

    // agentIds validieren (optional)
    if (body.config.agentIds && !Array.isArray(body.config.agentIds)) {
      errors.push('config.agentIds muss ein Array sein')
    }

    // includeCharts validieren (optional)
    if (body.config.includeCharts !== undefined && typeof body.config.includeCharts !== 'boolean') {
      errors.push('config.includeCharts muss ein Boolean sein')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
