// API-Endpoint für geplante Reports - Story 4

import { NextRequest, NextResponse } from 'next/server'
import { ReportService } from '@/services/reportService'
import { ScheduledReport } from '@/types/analytics'

const reportService = ReportService.getInstance()

// GET - Alle geplanten Reports abrufen
export async function GET(request: NextRequest) {
  try {
    const scheduledReports = await reportService.getScheduledReports()

    return NextResponse.json(scheduledReports, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=300' // 5 Minuten Cache
      }
    })

  } catch (error) {
    console.error('Fehler beim Abrufen der geplanten Reports:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler beim Abrufen der geplanten Reports',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// POST - Neuen geplanten Report erstellen
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Request-Validierung
    const validationResult = validateScheduledReportRequest(body)
    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: 'Ungültige Request-Daten', details: validationResult.errors },
        { status: 400 }
      )
    }

    // Geplanten Report erstellen
    const scheduledReport = await reportService.createScheduledReport(body)

    return NextResponse.json(scheduledReport, {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('Fehler beim Erstellen des geplanten Reports:', error)
    
    return NextResponse.json(
      { 
        error: 'Interner Serverfehler beim Erstellen des geplanten Reports',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

// Validierungsfunktion für geplante Reports
function validateScheduledReportRequest(body: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // name validieren
  if (!body.name || typeof body.name !== 'string') {
    errors.push('name ist erforderlich und muss ein String sein')
  } else if (body.name.length < 3 || body.name.length > 100) {
    errors.push('name muss zwischen 3 und 100 Zeichen lang sein')
  }

  // reportType validieren
  if (!body.reportType) {
    errors.push('reportType ist erforderlich')
  } else if (!['periodic', 'success_rate', 'duration_stats', 'tool_usage'].includes(body.reportType)) {
    errors.push('reportType muss einer von: periodic, success_rate, duration_stats, tool_usage sein')
  }

  // schedule validieren
  if (!body.schedule) {
    errors.push('schedule ist erforderlich')
  } else {
    if (!body.schedule.frequency) {
      errors.push('schedule.frequency ist erforderlich')
    } else if (!['daily', 'weekly', 'monthly'].includes(body.schedule.frequency)) {
      errors.push('schedule.frequency muss einer von: daily, weekly, monthly sein')
    }

    if (!body.schedule.time) {
      errors.push('schedule.time ist erforderlich')
    } else {
      // Zeit-Format validieren (HH:MM)
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
      if (!timeRegex.test(body.schedule.time)) {
        errors.push('schedule.time muss im Format HH:MM sein (z.B. 09:30)')
      }
    }

    // Wöchentliche Reports: dayOfWeek validieren
    if (body.schedule.frequency === 'weekly') {
      if (body.schedule.dayOfWeek === undefined) {
        errors.push('schedule.dayOfWeek ist für wöchentliche Reports erforderlich')
      } else if (!Number.isInteger(body.schedule.dayOfWeek) || 
                 body.schedule.dayOfWeek < 0 || body.schedule.dayOfWeek > 6) {
        errors.push('schedule.dayOfWeek muss eine Zahl zwischen 0 (Sonntag) und 6 (Samstag) sein')
      }
    }

    // Monatliche Reports: dayOfMonth validieren
    if (body.schedule.frequency === 'monthly') {
      if (body.schedule.dayOfMonth === undefined) {
        errors.push('schedule.dayOfMonth ist für monatliche Reports erforderlich')
      } else if (!Number.isInteger(body.schedule.dayOfMonth) || 
                 body.schedule.dayOfMonth < 1 || body.schedule.dayOfMonth > 31) {
        errors.push('schedule.dayOfMonth muss eine Zahl zwischen 1 und 31 sein')
      }
    }
  }

  // config validieren
  if (!body.config) {
    errors.push('config ist erforderlich')
  } else {
    if (!body.config.format) {
      errors.push('config.format ist erforderlich')
    } else if (!['pdf', 'excel', 'csv'].includes(body.config.format)) {
      errors.push('config.format muss einer von: pdf, excel, csv sein')
    }

    if (!body.config.language) {
      errors.push('config.language ist erforderlich')
    } else if (!['de', 'en'].includes(body.config.language)) {
      errors.push('config.language muss einer von: de, en sein')
    }

    // agentIds validieren (optional)
    if (body.config.agentIds && !Array.isArray(body.config.agentIds)) {
      errors.push('config.agentIds muss ein Array sein')
    }
  }

  // recipients validieren
  if (!body.recipients || !Array.isArray(body.recipients)) {
    errors.push('recipients ist erforderlich und muss ein Array sein')
  } else if (body.recipients.length === 0) {
    errors.push('Mindestens ein Empfänger ist erforderlich')
  } else {
    body.recipients.forEach((recipient: any, index: number) => {
      if (!recipient.email || typeof recipient.email !== 'string') {
        errors.push(`recipients[${index}].email ist erforderlich und muss ein String sein`)
      } else {
        // E-Mail-Format validieren
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(recipient.email)) {
          errors.push(`recipients[${index}].email hat ein ungültiges Format`)
        }
      }

      if (recipient.name && typeof recipient.name !== 'string') {
        errors.push(`recipients[${index}].name muss ein String sein`)
      }
    })
  }

  // enabled validieren (optional, default: true)
  if (body.enabled !== undefined && typeof body.enabled !== 'boolean') {
    errors.push('enabled muss ein Boolean sein')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
