'use client'

import { AuthGuard } from '@/components/auth-guard'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { AgentPerformanceDashboard } from '@/components/dashboard/AgentPerformanceDashboard'

export default function AgentDashboardPage() {
  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <AgentPerformanceDashboard />
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
