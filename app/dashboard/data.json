[{"id": 1, "header": "CALL-2024-001", "type": "09:15 AM", "status": "00:15:30", "target": "00:12:45", "limit": "None", "reviewer": "Customer inquiry resolved successfully"}, {"id": 2, "header": "CALL-2024-002", "type": "10:22 AM", "status": "00:08:15", "target": "00:08:15", "limit": "Connection timeout", "reviewer": "Technical support completed"}, {"id": 3, "header": "CALL-2024-003", "type": "11:45 AM", "status": "00:22:10", "target": "00:20:30", "limit": "None", "reviewer": "Sales consultation finished"}, {"id": 4, "header": "CALL-2024-004", "type": "02:15 PM", "status": "00:05:45", "target": "00:05:45", "limit": "Audio quality issue", "reviewer": "Quick support call"}, {"id": 5, "header": "CALL-2024-005", "type": "03:30 PM", "status": "00:18:20", "target": "00:15:00", "limit": "None", "reviewer": "Product demo completed"}, {"id": 6, "header": "CALL-2024-006", "type": "04:45 PM", "status": "00:12:35", "target": "00:10:00", "limit": "None", "reviewer": "Billing question answered"}, {"id": 7, "header": "CALL-2024-007", "type": "09:30 AM", "status": "00:25:15", "target": "00:25:15", "limit": "Screen sharing failed", "reviewer": "Training session conducted"}, {"id": 8, "header": "CALL-2024-008", "type": "11:00 AM", "status": "00:07:30", "target": "00:07:30", "limit": "None", "reviewer": "Password reset assistance"}, {"id": 9, "header": "CALL-2024-009", "type": "01:20 PM", "status": "00:19:45", "target": "00:18:00", "limit": "None", "reviewer": "Feature request discussion"}, {"id": 10, "header": "CALL-2024-010", "type": "03:15 PM", "status": "00:14:20", "target": "00:12:00", "limit": "Microphone issue", "reviewer": "Account setup completed"}, {"id": 11, "header": "CALL-2024-011", "type": "10:45 AM", "status": "00:09:10", "target": "00:09:10", "limit": "None", "reviewer": "Bug report submitted"}, {"id": 12, "header": "CALL-2024-012", "type": "02:30 PM", "status": "00:16:55", "target": "00:15:30", "limit": "None", "reviewer": "Integration support provided"}, {"id": 13, "header": "CALL-2024-013", "type": "04:10 PM", "status": "00:11:25", "target": "00:10:00", "limit": "Call dropped", "reviewer": "Refund request processed"}, {"id": 14, "header": "CALL-2024-014", "type": "09:50 AM", "status": "00:21:40", "target": "00:20:00", "limit": "None", "reviewer": "Onboarding session completed"}, {"id": 15, "header": "CALL-2024-015", "type": "12:15 PM", "status": "00:06:30", "target": "00:06:30", "limit": "None", "reviewer": "Quick status update"}, {"id": 16, "header": "CALL-2024-016", "type": "01:45 PM", "status": "00:13:15", "target": "00:12:00", "limit": "Video quality poor", "reviewer": "Security consultation"}, {"id": 17, "header": "CALL-2024-017", "type": "03:55 PM", "status": "00:17:30", "target": "00:15:45", "limit": "None", "reviewer": "Performance optimization discussed"}, {"id": 18, "header": "CALL-2024-018", "type": "10:20 AM", "status": "00:08:45", "target": "00:08:45", "limit": "None", "reviewer": "License renewal processed"}, {"id": 19, "header": "CALL-2024-019", "type": "11:35 AM", "status": "00:24:10", "target": "00:22:30", "limit": "None", "reviewer": "Custom solution planning"}, {"id": 20, "header": "CALL-2024-020", "type": "02:50 PM", "status": "00:10:20", "target": "00:10:00", "limit": "Background noise", "reviewer": "Data migration support"}]