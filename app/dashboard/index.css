:root {
    --background: oklch(1.0000 0 0);
    --foreground: oklch(0.2077 0.0398 265.7549);
    --card: oklch(0.9842 0.0034 247.8575);
    --card-foreground: oklch(0.2077 0.0398 265.7549);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.2077 0.0398 265.7549);
    --primary: oklch(0.2795 0.0368 260.0310);
    --primary-foreground: oklch(0.9842 0.0034 247.8575);
    --secondary: oklch(0.6847 0.1479 237.3225);
    --secondary-foreground: oklch(0.9842 0.0034 247.8575);
    --muted: oklch(0.9683 0.0069 247.8956);
    --muted-foreground: oklch(0.5544 0.0407 257.4166);
    --accent: oklch(0.9683 0.0069 247.8956);
    --accent-foreground: oklch(0.2077 0.0398 265.7549);
    --destructive: oklch(0.5771 0.2152 27.3250);
    --destructive-foreground: oklch(0.9842 0.0034 247.8575);
    --border: oklch(0.9288 0.0126 255.5078);
    --input: oklch(0.9288 0.0126 255.5078);
    --ring: oklch(0.6847 0.1479 237.3225);
    --chart-1: oklch(0.6847 0.1479 237.3225);
    --chart-2: oklch(0.5876 0.1389 241.9661);
    --chart-3: oklch(0.5000 0.1193 242.7490);
    --chart-4: oklch(0.4434 0.1000 240.7897);
    --chart-5: oklch(0.2935 0.0632 243.1571);
    --sidebar: oklch(0.9842 0.0034 247.8575);
    --sidebar-foreground: oklch(0.2077 0.0398 265.7549);
    --sidebar-primary: oklch(0.2795 0.0368 260.0310);
    --sidebar-primary-foreground: oklch(0.9842 0.0034 247.8575);
    --sidebar-accent: oklch(0.9683 0.0069 247.8956);
    --sidebar-accent-foreground: oklch(0.2077 0.0398 265.7549);
    --sidebar-border: oklch(0.9288 0.0126 255.5078);
    --sidebar-ring: oklch(0.6847 0.1479 237.3225);
    --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0rem;
    --spacing: 0.25rem;
  }
  
  .dark {
    --background: oklch(0.1288 0.0406 264.6952);
    --foreground: oklch(0.9288 0.0126 255.5078);
    --card: oklch(0.2077 0.0398 265.7549);
    --card-foreground: oklch(0.9288 0.0126 255.5078);
    --popover: oklch(0.1288 0.0406 264.6952);
    --popover-foreground: oklch(0.9288 0.0126 255.5078);
    --primary: oklch(0.9288 0.0126 255.5078);
    --primary-foreground: oklch(0.1363 0.0364 259.2010);
    --secondary: oklch(0.6847 0.1479 237.3225);
    --secondary-foreground: oklch(0.9842 0.0034 247.8575);
    --muted: oklch(0.2795 0.0368 260.0310);
    --muted-foreground: oklch(0.7107 0.0351 256.7878);
    --accent: oklch(0.2795 0.0368 260.0310);
    --accent-foreground: oklch(0.9842 0.0034 247.8575);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(0.9842 0.0034 247.8575);
    --border: oklch(0.3717 0.0392 257.2870);
    --input: oklch(0.2795 0.0368 260.0310);
    --ring: oklch(0.7535 0.1390 232.6615);
    --chart-1: oklch(0.7535 0.1390 232.6615);
    --chart-2: oklch(0.6847 0.1479 237.3225);
    --chart-3: oklch(0.5876 0.1389 241.9661);
    --chart-4: oklch(0.5000 0.1193 242.7490);
    --chart-5: oklch(0.4434 0.1000 240.7897);
    --sidebar: oklch(0.2077 0.0398 265.7549);
    --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
    --sidebar-primary: oklch(0.9288 0.0126 255.5078);
    --sidebar-primary-foreground: oklch(0.1363 0.0364 259.2010);
    --sidebar-accent: oklch(0.2795 0.0368 260.0310);
    --sidebar-accent-foreground: oklch(0.9842 0.0034 247.8575);
    --sidebar-border: oklch(0.3717 0.0392 257.2870);
    --sidebar-ring: oklch(0.7535 0.1390 232.6615);
    --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --radius: 0.75rem;
    --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.25);
    --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.25);
    --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.50), 0px 1px 2px -1px hsl(0 0% 0% / 0.50);
    --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.50), 0px 1px 2px -1px hsl(0 0% 0% / 0.50);
    --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.50), 0px 2px 4px -1px hsl(0 0% 0% / 0.50);
    --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.50), 0px 4px 6px -1px hsl(0 0% 0% / 0.50);
    --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.50), 0px 8px 10px -1px hsl(0 0% 0% / 0.50);
    --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 1.25);
  }
  
  @theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
  
    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);
  
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
  
    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
  
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  
  body {
    letter-spacing: var(--tracking-normal);
  }