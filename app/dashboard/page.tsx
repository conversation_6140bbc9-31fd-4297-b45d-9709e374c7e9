import { AppSidebar } from "@/components/app-sidebar"
import { AuthGuard } from "@/components/auth-guard"
import "./index.css"

import { ChartAreaInteractive } from "@/components/dashboard/charts/chart-area-interactive"
import { CircleCharts } from "@/components/dashboard/charts/circle-charts"
import { RadialCharts } from "@/components/dashboard/charts/radial-charts"
import { DataTable } from "@/components/dashboard/data-table"
import { SectionCards } from "@/components/dashboard/charts/section-cards"
import { SiteHeader } from "@/components/dashboard/site-header"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

import data from "./data.json"

export default function Page() {
  return (
    <AuthGuard>
      <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <SectionCards />
              <div className="px-4 lg:px-6">
                <div className="grid grid-cols-1 gap-4 mb-6 lg:grid-cols-3">
                  <CircleCharts />
                  <RadialCharts />
                  <div className="lg:col-span-1">
                    <div className="h-full flex items-center justify-center bg-muted/30 rounded-lg border">
                      <p className="text-muted-foreground">Additional Chart</p>
                    </div>
                  </div>
                </div>
                <ChartAreaInteractive />
              </div>
              <DataTable data={data} />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
    </AuthGuard>
  )
}
