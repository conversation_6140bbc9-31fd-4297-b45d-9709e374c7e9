'use client'

import { AuthGuard } from '@/components/auth-guard'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { ConversationHistory } from '@/components/history/ConversationHistory'

export default function HistoryPage() {
  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title="Gesprächshistorie" />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <ConversationHistory />
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
