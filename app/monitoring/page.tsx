'use client'

import { AuthGuard } from '@/components/auth-guard'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { LiveMonitoringDashboard } from '@/components/monitoring/LiveMonitoringDashboard'

export default function MonitoringPage() {
  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title="Echtzeit-Monitoring" />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <LiveMonitoringDashboard />
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
