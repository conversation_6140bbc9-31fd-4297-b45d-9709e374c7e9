'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, <PERSON>t, Edit, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { Agent } from '@/types/agent'
import { AVAILABLE_VOICES } from '@/lib/constants/voices'

interface AgentDetailsProps {
  agent: Agent
  loading?: boolean
  onDelete?: () => void
}

export function AgentDetails({ agent, loading = false, onDelete }: AgentDetailsProps) {
  const voice = AVAILABLE_VOICES.find(v => v.id === agent.voice)

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/agents">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Agents
          </Link>
        </Button>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-6 w-6" />
              <div>
                <CardTitle className="flex items-center gap-2">
                  {agent.name}
                  <Badge variant={agent.status === 'active' ? 'default' : 'secondary'}>
                    {agent.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </CardTitle>
                <CardDescription>{agent.description}</CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/agents/${agent.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
              {onDelete && (
                <Button variant="outline" size="sm" onClick={onDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="font-semibold mb-2">System Prompt</h3>
            <div className="bg-muted p-4 rounded-md">
              <p className="text-sm whitespace-pre-wrap">{agent.system_prompt}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Language</h3>
              <p className="text-sm text-muted-foreground">
                {agent.language === 'de-DE' ? 'German' : 'English'}
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Voice</h3>
              <p className="text-sm text-muted-foreground">
                {voice ? `${voice.name} (${voice.gender})` : 'Unknown'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Created</h3>
              <p className="text-sm text-muted-foreground">
                {new Date(agent.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Last Updated</h3>
              <p className="text-sm text-muted-foreground">
                {new Date(agent.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex gap-4">
              <Button asChild className="flex-1">
                <Link href={`/agents/${agent.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Agent
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/agents">Back to List</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
