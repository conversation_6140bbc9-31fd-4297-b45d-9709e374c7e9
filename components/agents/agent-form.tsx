'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { ArrowLeft, Bot } from 'lucide-react'
import Link from 'next/link'
import { AVAILABLE_VOICES, getVoicesByLanguage, DEFAULT_VOICES } from '@/lib/constants/voices'
import { CreateAgentRequest, Agent, ModelConfiguration, VoiceConfiguration, TranscriberConfiguration, ToolsConfiguration, AnalyticsConfiguration, AdvancedSettings } from '@/types/agent'

// Import section components
import { ModelConfigurationSection } from './sections/ModelConfigurationSection'
import { VoiceConfigurationSection } from './sections/VoiceConfigurationSection'
import { TranscriberConfigurationSection } from './sections/TranscriberConfigurationSection'
import { ToolsConfigurationSection } from './sections/ToolsConfigurationSection'
import { AnalyticsConfigurationSection } from './sections/AnalyticsConfigurationSection'
import { AdvancedSettingsSection } from './sections/AdvancedSettingsSection'

interface AgentFormProps {
  mode: 'create' | 'edit'
  initialData?: Agent
  onSubmit: (data: CreateAgentRequest) => Promise<void>
  loading?: boolean
}

// Default configurations
export const getDefaultModelConfig = (): ModelConfiguration => ({
  provider: 'openai',
  model: 'gpt-4o',
  startMessageMode: 'user_starts',
  openingMessage: 'Hello! I\'m JASZ, your virtual assistant. How can I help you today?',
  temperature: 0.7,
  maxTokens: 4000
})

export const getDefaultVoiceConfig = (): VoiceConfiguration => ({
  voiceProvider: 'elevenlabs',
  voice: '',
  ttsModel: 'eleven_multilingual_v2',
  minCharacters: 10,
  speechSpeed: 1.0
})

export const getDefaultTranscriberConfig = (): TranscriberConfiguration => ({
  provider: 'deepgram',
  recognitionModel: 'nova-2',
  backgroundNoiseFilter: true,
  confidenceThreshold: 0.8,
  numberFormat: 'digits'
})

export const getDefaultToolsConfig = (): ToolsConfiguration => ({
  selectedTools: [],
  allowCallEnd: true,
  allowKeypad: false
})

export const getDefaultAnalyticsConfig = (): AnalyticsConfiguration => ({
  summaryTimeout: 30,
  summaryPrompt: 'Create a concise summary of the conversation with the key points and outcomes.',
  minMessagesForAnalysis: 3,
  evaluationPrompt: 'Evaluate the conversation based on goal achievement, customer satisfaction, and problem resolution.',
  dataExtractionPrompt: 'Extract structured information from the conversation.',
  dataExtractionTimeout: 45
})

export const getDefaultAdvancedSettings = (): AdvancedSettings => ({
  hipaaCompliant: false,
  pciCompliant: false,
  recordAudio: false,
  audioFormat: 'mp3',
  recordVideo: false,
  speechStartDelay: 0.5,
  intelligentEndpointDetection: false,
  punctuationDelay: 0.3,
  noPunctuationDelay: 0.8,
  numberDelay: 1.0,
  voicemailProvider: 'jasz',
  userWordStopCount: 3,
  userSpeechDuration: 1.5,
  interruptionPause: 0.5,
  silenceTimeout: 30,
  maxCallDuration: 1800,
  digitInputEnabled: false,
  digitInputTimeout: 5,
  digitInputSeparator: '#'
})

export function AgentForm({ mode, initialData, onSubmit, loading = false }: AgentFormProps) {
  const [formData, setFormData] = useState<CreateAgentRequest>(() => {
    const language = initialData?.language || 'de-DE';
    const voice = initialData?.voice || DEFAULT_VOICES[language];
    const voiceConfig = initialData?.voiceConfig || { ...getDefaultVoiceConfig(), voice };

    return {
      name: initialData?.name || '',
      description: initialData?.description || '',
      system_prompt: initialData?.system_prompt || '',
      voice: voice,
      language: language,
      status: initialData?.status || 'active',
      modelConfig: initialData?.modelConfig || getDefaultModelConfig(),
      voiceConfig: voiceConfig,
      transcriberConfig: initialData?.transcriberConfig || getDefaultTranscriberConfig(),
      toolsConfig: initialData?.toolsConfig || getDefaultToolsConfig(),
      analyticsConfig: initialData?.analyticsConfig || getDefaultAnalyticsConfig(),
      advancedSettings: initialData?.advancedSettings || getDefaultAdvancedSettings()
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})

    // Basic validation
    const newErrors: Record<string, string> = {}
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }
    if (!formData.description?.trim()) {
      newErrors.description = 'Description is required'
    }
    if (!formData.system_prompt.trim()) {
      newErrors.system_prompt = 'System prompt is required'
    } else if (formData.system_prompt.length < 50) {
      newErrors.system_prompt = 'System prompt must be at least 50 characters'
    }
    if (!formData.voice) {
      newErrors.voice = 'Voice is required'
    }

    // Extended validation
    if (formData.modelConfig && !formData.modelConfig.provider) {
      newErrors['modelConfig.provider'] = 'Provider is required'
    }
    if (formData.modelConfig && !formData.modelConfig.model) {
      newErrors['modelConfig.model'] = 'Model is required'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Error submitting form:', error)
      setErrors({ general: `Error ${mode === 'create' ? 'creating' : 'updating'} agent` })
    }
  }

  const handleLanguageChange = (language: 'de-DE' | 'en-US') => {
    const defaultVoice = DEFAULT_VOICES[language];
    setFormData(prev => ({
      ...prev,
      language,
      voice: defaultVoice,
      voiceConfig: { 
        ...(prev.voiceConfig || getDefaultVoiceConfig()), 
        voice: defaultVoice 
      }
    }))
  }

  const handleModelConfigChange = (config: ModelConfiguration) => {
    setFormData(prev => ({ ...prev, modelConfig: config }))
  }

  const handleVoiceConfigChange = (config: VoiceConfiguration) => {
    setFormData(prev => ({ ...prev, voiceConfig: config, voice: config.voice }))
  }

  const handleTranscriberConfigChange = (config: TranscriberConfiguration) => {
    setFormData(prev => ({ ...prev, transcriberConfig: config }))
  }

  const handleToolsConfigChange = (config: ToolsConfiguration) => {
    setFormData(prev => ({ ...prev, toolsConfig: config }))
  }

  const handleAnalyticsConfigChange = (config: AnalyticsConfiguration) => {
    setFormData(prev => ({ ...prev, analyticsConfig: config }))
  }

  const handleAdvancedSettingsChange = (config: AdvancedSettings) => {
    setFormData(prev => ({ ...prev, advancedSettings: config }))
  }

  const availableVoices = getVoicesByLanguage(formData.language)

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/agents">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Agents
          </Link>
        </Button>
      </div>

      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bot className="h-6 w-6" />
            <div>
              <CardTitle>
                {mode === 'create' ? 'Create New Agent' : 'Edit Agent'}
              </CardTitle>
              <CardDescription>
                {mode === 'create'
                  ? 'Configure your AI voice assistant with comprehensive settings'
                  : 'Update the configuration of your AI voice assistant'
                }
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.general && (
              <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md">
                {errors.general}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="name">Agent Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g. Customer Service Agent"
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of what this agent does..."
                className={errors.description ? 'border-destructive' : ''}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="system_prompt">System Prompt</Label>
              <Textarea
                id="system_prompt"
                value={formData.system_prompt}
                onChange={(e) => setFormData(prev => ({ ...prev, system_prompt: e.target.value }))}
                placeholder="You are a helpful assistant who..."
                rows={4}
                className={errors.system_prompt ? 'border-destructive' : ''}
              />
              <p className="text-sm text-muted-foreground">
                Define the personality and behavior of your AI assistant
              </p>
              {errors.system_prompt && (
                <p className="text-sm text-destructive">{errors.system_prompt}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Language</Label>
              <RadioGroup
                value={formData.language}
                onValueChange={(value: 'de-DE' | 'en-US') => handleLanguageChange(value)}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="de-DE" id="de-DE" />
                  <Label htmlFor="de-DE">Deutsch</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="en-US" id="en-US" />
                  <Label htmlFor="en-US">English</Label>
                </div>
              </RadioGroup>
            </div>

            <ModelConfigurationSection
              config={formData.modelConfig!}
              onChange={handleModelConfigChange}
              errors={errors}
            />

            <VoiceConfigurationSection
              config={formData.voiceConfig!}
              language={formData.language}
              onChange={handleVoiceConfigChange}
              errors={errors}
            />

            <TranscriberConfigurationSection
              config={formData.transcriberConfig!}
              onChange={handleTranscriberConfigChange}
              errors={errors}
            />

            <ToolsConfigurationSection
              config={formData.toolsConfig!}
              onChange={handleToolsConfigChange}
              errors={errors}
            />

            <AnalyticsConfigurationSection
              config={formData.analyticsConfig!}
              onChange={handleAnalyticsConfigChange}
              errors={errors}
            />

            <AdvancedSettingsSection
              config={formData.advancedSettings!}
              onChange={handleAdvancedSettingsChange}
              errors={errors}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : mode === 'create' ? 'Create Agent' : 'Update Agent'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
