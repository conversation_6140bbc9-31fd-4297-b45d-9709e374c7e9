'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, Edit, Eye, Plus } from 'lucide-react'
import Link from 'next/link'
import { Agent } from '@/types/agent'
import { AVAILABLE_VOICES } from '@/lib/constants/voices'

interface AgentListProps {
  agents?: Agent[]
  loading?: boolean
}

export function AgentList({ agents, loading }: AgentListProps) {
  if (loading) {
    return (
      <div className="grid gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-1/3"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-muted rounded w-full mb-2"></div>
              <div className="h-3 bg-muted rounded w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!agents || agents.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No agents yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first AI voice assistant
          </p>
          <Button asChild>
            <Link href="/agents/new">
              <Plus className="mr-2 h-4 w-4" />
              Create First Agent
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  const getVoiceById = (voiceId: string) => {
    return AVAILABLE_VOICES.find(voice => voice.id === voiceId)
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {agents.map((agent) => {
        const voice = getVoiceById(agent.voice)
        
        return (
          <Card key={agent.id} className="hover:shadow-md transition-shadow h-full flex flex-col">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Bot className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <CardDescription className="text-sm line-clamp-2">
                      {agent.description || 'Keine Beschreibung verfügbar'}
                    </CardDescription>
                  </div>
                </div>
                <Badge variant={agent.status === 'active' ? 'success' : 'destructive'} className="text-xs">
                  {agent.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0 flex-1 flex flex-col">
              <div className="grid grid-cols-1 gap-2 text-xs mb-3 flex-1">
                <div>
                  <span className="font-medium">Language:</span>
                  <span className="ml-1">
                    {agent.language === 'de-DE' ? 'German' : 'English'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Voice:</span>
                  <span className="ml-1">
                    {voice ? `${voice.name}` : 'Unknown'}
                  </span>
                </div>
              </div>
              
              <div className="flex gap-2 mt-auto">
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/agents/${agent.id}`}>
                    <Eye className="h-3 w-3 mr-1" />
                    Details
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/agents/${agent.id}/edit`}>
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
