'use client'

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'

interface AgentTabNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
  className?: string
}

export function AgentTabNavigation({ activeTab, onTabChange, className }: AgentTabNavigationProps) {
  const tabs = [
    { id: 'model', label: 'Modell' },
    { id: 'voice', label: 'Voice' },
    { id: 'transcriber', label: 'Transcription' },
    { id: 'tools', label: 'Tools' },
    { id: 'analytics', label: 'Analysis' },
    { id: 'advanced', label: 'Advanced' }
  ]

  return (
    <div className={cn("w-full", className)}>
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-6 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="text-sm font-medium transition-all hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  )
}