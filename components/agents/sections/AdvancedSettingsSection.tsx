'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AdvancedSettings } from '@/types/agent'
import { Settings2 } from 'lucide-react'

interface AdvancedSettingsSectionProps {
  config: AdvancedSettings
  onChange: (config: AdvancedSettings) => void
  errors?: Record<string, string>
}

const AUDIO_FORMATS = [
  { value: 'mp3', label: 'MP3' },
  { value: 'wav', label: 'WAV' },
  { value: 'flac', label: 'FLAC' }
] as const

const VOICEMAIL_PROVIDERS = [
  { value: 'jasz', label: 'JASZ (KI-basiert)' },
  { value: 'google', label: 'Google' },
  { value: 'openai', label: 'OpenAI' },
  { value: 'twilio', label: 'Twilio' }
] as const

export function AdvancedSettingsSection({ config, onChange, errors }: AdvancedSettingsSectionProps) {
  const updateConfig = (updates: Partial<AdvancedSettings>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Settings2 className="h-5 w-5" />
          <div>
            <CardTitle>Advanced Settings</CardTitle>
            <CardDescription>
              Detailed configuration for special use cases
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Datenschutz */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Privacy</h4>
            <p className="text-sm text-muted-foreground">
              Configure how conversations are stored or protected
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="hipaaCompliant">HIPAA Compliance</Label>
                <p className="text-sm text-muted-foreground">
                  No storage of logs or audio recordings
                </p>
              </div>
              <Switch
                id="hipaaCompliant"
                checked={config.hipaaCompliant}
                onCheckedChange={(checked) => updateConfig({ hipaaCompliant: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="pciCompliant">PCI Compliance</Label>
                <p className="text-sm text-muted-foreground">
                  Only PCI-certified providers usable
                </p>
              </div>
              <Switch
                id="pciCompliant"
                checked={config.pciCompliant}
                onCheckedChange={(checked) => updateConfig({ pciCompliant: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="recordAudio">Audio Recording</Label>
                <p className="text-sm text-muted-foreground">
                  Record conversation
                </p>
              </div>
              <Switch
                id="recordAudio"
                checked={config.recordAudio}
                onCheckedChange={(checked) => updateConfig({ recordAudio: checked })}
              />
            </div>

            {config.recordAudio && (
              <div className="space-y-2 ml-4">
                <Label htmlFor="audioFormat">Audio Format</Label>
                <Select
                  value={config.audioFormat}
                  onValueChange={(value: 'mp3' | 'wav' | 'flac') => updateConfig({ audioFormat: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    {AUDIO_FORMATS.map((format) => (
                      <SelectItem key={format.value} value={format.value}>
                        {format.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="recordVideo">Video Recording</Label>
                <p className="text-sm text-muted-foreground">
                  Optional for web calls - records user video
                </p>
              </div>
              <Switch
                id="recordVideo"
                checked={config.recordVideo}
                onCheckedChange={(checked) => updateConfig({ recordVideo: checked })}
              />
            </div>
          </div>
        </div>

        {/* Startzeitpunkt der Sprachausgabe */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Start time of voice output</h4>
            <p className="text-sm text-muted-foreground">
              Define when JASZ may start speaking
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="speechStartDelay">Wait time (seconds)</Label>
              <Input
                id="speechStartDelay"
                type="number"
                value={config.speechStartDelay}
                onChange={(e) => updateConfig({ speechStartDelay: parseFloat(e.target.value) || 0 })}
                placeholder="0.5"
                min="0"
                max="10"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="punctuationDelay">Wait time after punctuation</Label>
              <Input
                id="punctuationDelay"
                type="number"
                value={config.punctuationDelay}
                onChange={(e) => updateConfig({ punctuationDelay: parseFloat(e.target.value) || 0 })}
                placeholder="0.3"
                min="0"
                max="5"
                step="0.1"
              />
              <p className="text-sm text-muted-foreground">
                Delay after a period, comma, etc.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="noPunctuationDelay">Wait time without punctuation</Label>
              <Input
                id="noPunctuationDelay"
                type="number"
                value={config.noPunctuationDelay}
                onChange={(e) => updateConfig({ noPunctuationDelay: parseFloat(e.target.value) || 0 })}
                placeholder="0.8"
                min="0"
                max="5"
                step="0.1"
              />
              <p className="text-sm text-muted-foreground">
                Delay after incomplete statement
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="numberDelay">Wait time for numbers</Label>
              <Input
                id="numberDelay"
                type="number"
                value={config.numberDelay}
                onChange={(e) => updateConfig({ numberDelay: parseFloat(e.target.value) || 0 })}
                placeholder="1.0"
                min="0"
                max="5"
                step="0.1"
              />
              <p className="text-sm text-muted-foreground">
                Delay after a numerical indication
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="intelligentEndpointDetection">Intelligent Endpoint Detection</Label>
              <p className="text-sm text-muted-foreground">
                More accurate detection of conversation transitions (LiveKit English only)
              </p>
            </div>
            <Switch
              id="intelligentEndpointDetection"
              checked={config.intelligentEndpointDetection}
              onCheckedChange={(checked) => updateConfig({ intelligentEndpointDetection: checked })}
            />
          </div>
        </div>

        {/* Voicemail-Erkennung */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Voicemail Detection</h4>
            <p className="text-sm text-muted-foreground">
              Define how JASZ responds to voicemail
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="voicemailProvider">Detection Provider</Label>
            <Select
              value={config.voicemailProvider}
              onValueChange={(value: 'jasz' | 'google' | 'openai' | 'twilio') => 
                updateConfig({ voicemailProvider: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {VOICEMAIL_PROVIDERS.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Sprechstopp-Regelung */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Speech Stop Regulation</h4>
            <p className="text-sm text-muted-foreground">
              Determine when JASZ interrupts or pauses speaking
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="userWordStopCount">User word count</Label>
              <Input
                id="userWordStopCount"
                type="number"
                value={config.userWordStopCount}
                onChange={(e) => updateConfig({ userWordStopCount: parseInt(e.target.value) || 0 })}
                placeholder="3"
                min="1"
                max="20"
              />
              <p className="text-sm text-muted-foreground">
                At how many spoken words JASZ stops
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="userSpeechDuration">Speaking duration (seconds)</Label>
              <Input
                id="userSpeechDuration"
                type="number"
                value={config.userSpeechDuration}
                onChange={(e) => updateConfig({ userSpeechDuration: parseFloat(e.target.value) || 0 })}
                placeholder="1.5"
                min="0.5"
                max="10"
                step="0.1"
              />
              <p className="text-sm text-muted-foreground">
                Time span of user after which JASZ stops
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="interruptionPause">Pause after interruption</Label>
              <Input
                id="interruptionPause"
                type="number"
                value={config.interruptionPause}
                onChange={(e) => updateConfig({ interruptionPause: parseFloat(e.target.value) || 0 })}
                placeholder="0.5"
                min="0"
                max="5"
                step="0.1"
              />
              <p className="text-sm text-muted-foreground">
                Wait time before resuming conversation
              </p>
            </div>
          </div>
        </div>

        {/* Timeouts für Anrufende */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Timeouts for Callers</h4>
            <p className="text-sm text-muted-foreground">
              Controls when conversations are automatically terminated
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="silenceTimeout">Silence Timeout (seconds)</Label>
              <Input
                id="silenceTimeout"
                type="number"
                value={config.silenceTimeout}
                onChange={(e) => updateConfig({ silenceTimeout: parseInt(e.target.value) || 0 })}
                placeholder="30"
                min="5"
                max="300"
              />
              <p className="text-sm text-muted-foreground">
                Duration without input before conversation automatically ends
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxCallDuration">Maximum Conversation Duration (seconds)</Label>
              <Input
                id="maxCallDuration"
                type="number"
                value={config.maxCallDuration}
                onChange={(e) => updateConfig({ maxCallDuration: parseInt(e.target.value) || 0 })}
                placeholder="1800"
                min="60"
                max="7200"
              />
              <p className="text-sm text-muted-foreground">
                Maximum duration of a conversation (30 min = 1800 sec)
              </p>
            </div>
          </div>
        </div>

        {/* Zifferneingabe via Tastatur */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Digit Input via Keypad</h4>
            <p className="text-sm text-muted-foreground">
              Allows user input via the numeric keypad
            </p>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="space-y-0.5">
              <Label htmlFor="digitInputEnabled">Enable Digit Input</Label>
              <p className="text-sm text-muted-foreground">
                Users can enter digits via the keypad
              </p>
            </div>
            <Switch
              id="digitInputEnabled"
              checked={config.digitInputEnabled}
              onCheckedChange={(checked) => updateConfig({ digitInputEnabled: checked })}
            />
          </div>

          {config.digitInputEnabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ml-4">
              <div className="space-y-2">
                <Label htmlFor="digitInputTimeout">Input Timeout (seconds)</Label>
                <Input
                  id="digitInputTimeout"
                  type="number"
                  value={config.digitInputTimeout}
                  onChange={(e) => updateConfig({ digitInputTimeout: parseInt(e.target.value) || 0 })}
                  placeholder="5"
                  min="1"
                  max="30"
                />
                <p className="text-sm text-muted-foreground">
                  Time span until input processing
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="digitInputSeparator">Separator</Label>
                <Input
                  id="digitInputSeparator"
                  value={config.digitInputSeparator}
                  onChange={(e) => updateConfig({ digitInputSeparator: e.target.value })}
                  placeholder="#"
                  maxLength={1}
                />
                <p className="text-sm text-muted-foreground">
                  Character to delimit input (e.g. "#")
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
