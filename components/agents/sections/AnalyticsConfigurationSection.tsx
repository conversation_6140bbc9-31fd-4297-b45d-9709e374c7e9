'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AnalyticsConfiguration } from '@/types/agent'
import { BarChart3 } from 'lucide-react'

interface AnalyticsConfigurationSectionProps {
  config: AnalyticsConfiguration
  onChange: (config: AnalyticsConfiguration) => void
  errors?: Record<string, string>
}

export function AnalyticsConfigurationSection({ config, onChange, errors }: AnalyticsConfigurationSectionProps) {
  const updateConfig = (updates: Partial<AnalyticsConfiguration>) => {
    onChange({ ...config, ...updates })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          <div>
            <CardTitle>Analytics Functions</CardTitle>
            <CardDescription>
              Automatic evaluation and data collection from conversations
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Zusammenfassung */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Summary</h4>
            <p className="text-sm text-muted-foreground">
              Automatically create a conversation summary
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="summaryTimeout">Summary timeout (seconds)</Label>
              <Input
                id="summaryTimeout"
                type="number"
                value={config.summaryTimeout}
                onChange={(e) => updateConfig({ summaryTimeout: parseInt(e.target.value) || 0 })}
                placeholder="30"
                min="5"
                max="300"
                className={errors?.summaryTimeout ? 'border-destructive' : ''}
              />
              {errors?.summaryTimeout && (
                <p className="text-sm text-destructive">{errors.summaryTimeout}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="minMessagesForAnalysis">Minimum number of messages</Label>
              <Input
                id="minMessagesForAnalysis"
                type="number"
                value={config.minMessagesForAnalysis}
                onChange={(e) => updateConfig({ minMessagesForAnalysis: parseInt(e.target.value) || 0 })}
                placeholder="3"
                min="1"
                max="50"
                className={errors?.minMessagesForAnalysis ? 'border-destructive' : ''}
              />
              <p className="text-sm text-muted-foreground">
                Minimum number of messages to start an analysis
              </p>
              {errors?.minMessagesForAnalysis && (
                <p className="text-sm text-destructive">{errors.minMessagesForAnalysis}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="summaryPrompt">Summary Prompt</Label>
            <Textarea
              id="summaryPrompt"
              value={config.summaryPrompt}
              onChange={(e) => updateConfig({ summaryPrompt: e.target.value })}
              placeholder="Create a concise summary of the conversation with the key points..."
              rows={3}
              className={errors?.summaryPrompt ? 'border-destructive' : ''}
            />
            {errors?.summaryPrompt && (
              <p className="text-sm text-destructive">{errors.summaryPrompt}</p>
            )}
          </div>
        </div>

        {/* Erfolgsauswertung */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Success Evaluation</h4>
            <p className="text-sm text-muted-foreground">
              Determine if a conversation was successful
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="evaluationPrompt">Evaluation Prompt</Label>
            <Textarea
              id="evaluationPrompt"
              value={config.evaluationPrompt}
              onChange={(e) => updateConfig({ evaluationPrompt: e.target.value })}
              placeholder="Evaluate the conversation based on the following criteria: goal achievement, customer satisfaction, problem resolution..."
              rows={4}
              className={errors?.evaluationPrompt ? 'border-destructive' : ''}
            />
            <p className="text-sm text-muted-foreground">
              Text template for analyzing goal achievement
            </p>
            {errors?.evaluationPrompt && (
              <p className="text-sm text-destructive">{errors.evaluationPrompt}</p>
            )}
          </div>
        </div>

        {/* Strukturierte Datenerfassung */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h4 className="text-sm font-medium">Structured Data Collection</h4>
            <p className="text-sm text-muted-foreground">
              Extract targeted structured information from the conversation history
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="dataExtractionTimeout">Data collection timeout (seconds)</Label>
            <Input
              id="dataExtractionTimeout"
              type="number"
              value={config.dataExtractionTimeout}
              onChange={(e) => updateConfig({ dataExtractionTimeout: parseInt(e.target.value) || 0 })}
              placeholder="45"
              min="10"
              max="300"
              className={errors?.dataExtractionTimeout ? 'border-destructive' : ''}
            />
            {errors?.dataExtractionTimeout && (
              <p className="text-sm text-destructive">{errors.dataExtractionTimeout}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="dataExtractionPrompt">Data Collection Prompt</Label>
            <Textarea
              id="dataExtractionPrompt"
              value={config.dataExtractionPrompt}
              onChange={(e) => updateConfig({ dataExtractionPrompt: e.target.value })}
              placeholder="Extract the following information from the conversation: name, email, phone number, concern, priority..."
              rows={4}
              className={errors?.dataExtractionPrompt ? 'border-destructive' : ''}
            />
            {errors?.dataExtractionPrompt && (
              <p className="text-sm text-destructive">{errors.dataExtractionPrompt}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
