'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Textarea } from '@/components/ui/textarea'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ModelConfiguration } from '@/types/agent'
import { Settings } from 'lucide-react'

interface ModelConfigurationSectionProps {
  config: ModelConfiguration
  onChange: (config: ModelConfiguration) => void
  errors?: Record<string, string>
}

const MODEL_PROVIDERS = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'google', label: 'Google' },
  { value: 'openrouter', label: 'OpenRouter' }
] as const

const MODELS_BY_PROVIDER = {
  openai: [
    { value: 'gpt-4o', label: 'GPT-4o' },
    { value: 'gpt-4o-mini', label: 'GPT-4o Mini' },
    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
  ],
  google: [
    { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
    { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' },
    { value: 'gemini-pro', label: 'Gemini Pro' }
  ],
  openrouter: [
    { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet' },
    { value: 'anthropic/claude-3-haiku', label: 'Claude 3 Haiku' },
    { value: 'meta-llama/llama-3.1-405b-instruct', label: 'Llama 3.1 405B' }
  ]
}

export function ModelConfigurationSection({ config, onChange, errors }: ModelConfigurationSectionProps) {
  const updateConfig = (updates: Partial<ModelConfiguration>) => {
    onChange({ ...config, ...updates })
  }

  const availableModels = MODELS_BY_PROVIDER[config.provider] || []

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          <div>
            <CardTitle>Model Configuration</CardTitle>
            <CardDescription>
              Determine the behavior of the virtual assistant JASZ
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="provider">Provider</Label>
            <Select
              value={config.provider}
              onValueChange={(value: 'openai' | 'google' | 'openrouter') => {
                updateConfig({ 
                  provider: value,
                  model: '' // Reset model when provider changes
                })
              }}
            >
              <SelectTrigger className={errors?.provider ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {MODEL_PROVIDERS.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.provider && (
              <p className="text-sm text-destructive">{errors.provider}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="model">Language Model</Label>
            <Select
              value={config.model}
              onValueChange={(value) => updateConfig({ model: value })}
              disabled={!config.provider}
            >
              <SelectTrigger className={errors?.model ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.model && (
              <p className="text-sm text-destructive">{errors.model}</p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <Label>Opening Message Mode</Label>
          <RadioGroup
            value={config.startMessageMode}
            onValueChange={(value: 'agent_starts' | 'user_starts') => 
              updateConfig({ startMessageMode: value })
            }
            className="flex gap-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="agent_starts" id="agent_starts" />
              <Label htmlFor="agent_starts">JASZ opens the conversation</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="user_starts" id="user_starts" />
              <Label htmlFor="user_starts">Wait for user</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2">
          <Label htmlFor="openingMessage">Opening Message</Label>
          <Textarea
            id="openingMessage"
            value={config.openingMessage}
            onChange={(e) => updateConfig({ openingMessage: e.target.value })}
            placeholder="Hello! I'm JASZ, your virtual assistant. How can I help you today?"
            rows={3}
            className={errors?.openingMessage ? 'border-destructive' : ''}
          />
          {errors?.openingMessage && (
            <p className="text-sm text-destructive">{errors.openingMessage}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Label>Temperatur: {config.temperature}</Label>
            <div className="px-2">
              <input
                type="range"
                value={config.temperature}
                onChange={(e) => updateConfig({ temperature: parseFloat(e.target.value) })}
                max={1}
                min={0}
                step={0.1}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Creativity control (0 = conservative, 1 = creative)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxTokens">Maximum Token Count</Label>
            <Input
              id="maxTokens"
              type="number"
              value={config.maxTokens}
              onChange={(e) => updateConfig({ maxTokens: parseInt(e.target.value) || 0 })}
              placeholder="4000"
              min="100"
              max="32000"
              className={errors?.maxTokens ? 'border-destructive' : ''}
            />
            {errors?.maxTokens && (
              <p className="text-sm text-destructive">{errors.maxTokens}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
