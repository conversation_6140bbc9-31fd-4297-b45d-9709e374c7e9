'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ToolsConfiguration } from '@/types/agent'
import { Wrench } from 'lucide-react'

interface ToolsConfigurationSectionProps {
  config: ToolsConfiguration
  onChange: (config: ToolsConfiguration) => void
  errors?: Record<string, string>
}

// Mock available tools - in real app this would come from API
const AVAILABLE_TOOLS = [
  { id: 'calendar', name: '<PERSON><PERSON><PERSON>', description: 'Te<PERSON>ine verwalten und planen' },
  { id: 'email', name: 'E-Mail', description: 'E-Mails senden und empfangen' },
  { id: 'crm', name: 'CRM', description: 'Kundendaten verwalten' },
  { id: 'weather', name: '<PERSON><PERSON>', description: 'Wetterinformationen abrufen' },
  { id: 'calculator', name: '<PERSON><PERSON><PERSON>', description: 'Mathematische Berechnungen' },
  { id: 'translator', name: 'Übersetzer', description: 'Text übersetzen' },
  { id: 'search', name: 'Web-Suche', description: 'Im Internet suchen' },
  { id: 'database', name: 'Datenbank', description: 'Datenbankabfragen durchführen' }
]

export function ToolsConfigurationSection({ config, onChange, errors }: ToolsConfigurationSectionProps) {
  const updateConfig = (updates: Partial<ToolsConfiguration>) => {
    onChange({ ...config, ...updates })
  }

  const handleToolToggle = (toolId: string, checked: boolean) => {
    const updatedTools = checked
      ? [...config.selectedTools, toolId]
      : config.selectedTools.filter(id => id !== toolId)
    
    updateConfig({ selectedTools: updatedTools })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          <div>
            <CardTitle>Tools</CardTitle>
            <CardDescription>
              Enable JASZ to perform actions during a conversation
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Label>Tool Selection</Label>
          <p className="text-sm text-muted-foreground">
            Select tools from the library or connect your own services
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {AVAILABLE_TOOLS.map((tool) => (
              <div key={tool.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                <Checkbox
                  id={tool.id}
                  checked={config.selectedTools.includes(tool.id)}
                  onCheckedChange={(checked) => handleToolToggle(tool.id, checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor={tool.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {tool.name}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {tool.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="border-t pt-6">
          <h4 className="text-sm font-medium mb-4">Predefined Functions</h4>
          <p className="text-sm text-muted-foreground mb-4">
            Already integrated functions for typical use cases
          </p>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="allowCallEnd">Allow call termination</Label>
                <p className="text-sm text-muted-foreground">
                  JASZ can terminate the call independently (recommended for GPT-4 or higher)
                </p>
              </div>
              <Switch
                id="allowCallEnd"
                checked={config.allowCallEnd}
                onCheckedChange={(checked) => updateConfig({ allowCallEnd: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="allowKeypad">Keypad usage</Label>
                <p className="text-sm text-muted-foreground">
                  JASZ can select digits via the keypad
                </p>
              </div>
              <Switch
                id="allowKeypad"
                checked={config.allowKeypad}
                onCheckedChange={(checked) => updateConfig({ allowKeypad: checked })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="forwardingNumber">Forwarding number</Label>
              <Input
                id="forwardingNumber"
                value={config.forwardingNumber || ''}
                onChange={(e) => updateConfig({ forwardingNumber: e.target.value })}
                placeholder="+49 123 456789"
                type="tel"
                className={errors?.forwardingNumber ? 'border-destructive' : ''}
              />
              <p className="text-sm text-muted-foreground">
                Phone number for call forwarding
              </p>
              {errors?.forwardingNumber && (
                <p className="text-sm text-destructive">{errors.forwardingNumber}</p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
