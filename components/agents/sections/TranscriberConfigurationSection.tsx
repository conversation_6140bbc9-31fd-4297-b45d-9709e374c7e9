'use client'

import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TranscriberConfiguration } from '@/types/agent'
import { Mic } from 'lucide-react'

interface TranscriberConfigurationSectionProps {
  config: TranscriberConfiguration
  onChange: (config: TranscriberConfiguration) => void
  errors?: Record<string, string>
}

const TRANSCRIBER_PROVIDERS = [
  { value: 'deepgram', label: 'Deepgram' }
] as const

const RECOGNITION_MODELS = {
  deepgram: [
    { value: 'nova-2', label: 'Nova-2 (Neuestes Modell)' },
    { value: 'nova', label: 'Nova (Standard)' },
    { value: 'enhanced', label: 'Enhanced (Verbessert)' },
    { value: 'base', label: 'Base (Basis)' }
  ]
}

export function TranscriberConfigurationSection({ config, onChange, errors }: TranscriberConfigurationSectionProps) {
  const updateConfig = (updates: Partial<TranscriberConfiguration>) => {
    onChange({ ...config, ...updates })
  }

  const availableModels = RECOGNITION_MODELS[config.provider] || []

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Mic className="h-5 w-5" />
          <div>
            <CardTitle>Speech Recognition (Transcriber)</CardTitle>
            <CardDescription>
              Settings for transcribing conversations with JASZ
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="provider">Provider</Label>
            <Select
              value={config.provider}
              onValueChange={(value: 'deepgram') => {
                updateConfig({ 
                  provider: value,
                  recognitionModel: '' // Reset model when provider changes
                })
              }}
            >
              <SelectTrigger className={errors?.provider ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {TRANSCRIBER_PROVIDERS.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.provider && (
              <p className="text-sm text-destructive">{errors.provider}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="recognitionModel">Recognition Model</Label>
            <Select
              value={config.recognitionModel}
              onValueChange={(value) => updateConfig({ recognitionModel: value })}
              disabled={!config.provider}
            >
              <SelectTrigger className={errors?.recognitionModel ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    {model.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.recognitionModel && (
              <p className="text-sm text-destructive">{errors.recognitionModel}</p>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="backgroundNoiseFilter">Background Noise Filter</Label>
            <p className="text-sm text-muted-foreground">
              Activate to filter out background noise
            </p>
          </div>
          <Switch
            id="backgroundNoiseFilter"
            checked={config.backgroundNoiseFilter}
            onCheckedChange={(checked) => updateConfig({ backgroundNoiseFilter: checked })}
          />
        </div>

        <div className="space-y-4">
          <Label>Confidence Threshold (Confidence Score): {config.confidenceThreshold}</Label>
          <div className="px-2">
            <input
              type="range"
              value={config.confidenceThreshold}
              onChange={(e) => updateConfig({ confidenceThreshold: parseFloat(e.target.value) })}
              max={1}
              min={0}
              step={0.05}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Transcriptions below this value are discarded (scale 0-1)
          </p>
        </div>

        <div className="space-y-4">
          <Label>Number Format</Label>
          <RadioGroup
            value={config.numberFormat}
            onValueChange={(value: 'digits' | 'words') => updateConfig({ numberFormat: value })}
            className="flex gap-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="digits" id="digits" />
              <Label htmlFor="digits">Digits (123)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="words" id="words" />
              <Label htmlFor="words">Words (one hundred twenty-three)</Label>
            </div>
          </RadioGroup>
          <p className="text-sm text-muted-foreground">
            Display numbers as digits instead of words
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
