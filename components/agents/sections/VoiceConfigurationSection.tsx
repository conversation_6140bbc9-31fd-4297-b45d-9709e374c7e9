'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { VoiceConfiguration } from '@/types/agent'
import { Volume2 } from 'lucide-react'
import { AVAILABLE_VOICES, getVoicesByLanguage, DEFAULT_VOICES } from '@/lib/constants/voices'

interface VoiceConfigurationSectionProps {
  config: VoiceConfiguration
  language: 'de-DE' | 'en-US'
  onChange: (config: VoiceConfiguration) => void
  errors?: Record<string, string>
}

const VOICE_PROVIDERS = [
  { value: 'JASZ AI', label: 'JASZ AI' },
  { value: 'elevenlabs', label: 'ElevenLabs' },
  { value: 'cartesia', label: 'Cartes<PERSON>' }
] as const

const TTS_MODELS = {
  elevenlabs: [
    { value: 'eleven_multilingual_v2', label: 'Multilingual v2' },
    { value: 'eleven_turbo_v2', label: 'Turbo v2' },
    { value: 'eleven_monolingual_v1', label: 'Monolingual v1' }
  ],
  cartesia: [
    { value: 'sonic-english', label: 'Sonic English' },
    { value: 'sonic-multilingual', label: 'Sonic Multilingual' }
  ]
}

const BACKGROUND_SOUNDS = [
  { value: 'none', label: 'Kein Hintergrundklang' },
  { value: 'office', label: 'Bürogeräusche' },
  { value: 'cafe', label: 'Café-Atmosphäre' },
  { value: 'nature', label: 'Naturgeräusche' },
  { value: 'white_noise', label: 'Weißes Rauschen' },
  { value: 'custom', label: 'Benutzerdefiniert' }
]

export function VoiceConfigurationSection({ config, language, onChange, errors }: VoiceConfigurationSectionProps) {
  const updateConfig = (updates: Partial<VoiceConfiguration>) => {
    onChange({ ...config, ...updates })
  }

  const availableVoices = getVoicesByLanguage(language)
  const availableTTSModels = TTS_MODELS[config.voiceProvider] || []

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          <div>
            <CardTitle>Voice Output (Voice)</CardTitle>
            <CardDescription>
              Select a voice for output or synchronize your library
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="voiceProvider">Voice Provider</Label>
            <Select
              value={config.voiceProvider}
              onValueChange={(value: 'elevenlabs' | 'cartesia') => {
                const defaultModel = TTS_MODELS[value]?.[0]?.value || 'eleven_multilingual_v2';
                updateConfig({ 
                  voiceProvider: value,
                  ttsModel: defaultModel
                })
              }}
            >
              <SelectTrigger className={errors?.voiceProvider ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                {VOICE_PROVIDERS.map((provider) => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.voiceProvider && (
              <p className="text-sm text-destructive">{errors.voiceProvider}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="voice">Voice</Label>
            <Select
              value={config.voice || DEFAULT_VOICES[language]}
              onValueChange={(value) => updateConfig({ voice: value })}
            >
              <SelectTrigger className={errors?.voice ? 'border-destructive' : ''}>
                <SelectValue placeholder="Select voice" />
              </SelectTrigger>
              <SelectContent>
                {availableVoices.map((voice) => (
                  <SelectItem key={voice.id} value={voice.id}>
                    {voice.name} ({voice.gender})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors?.voice && (
              <p className="text-sm text-destructive">{errors.voice}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="ttsModel">TTS/STT Model</Label>
          <Select
            value={config.ttsModel}
            onValueChange={(value) => updateConfig({ ttsModel: value })}
            disabled={!config.voiceProvider}
          >
            <SelectTrigger className={errors?.ttsModel ? 'border-destructive' : ''}>
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {availableTTSModels.map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors?.ttsModel && (
            <p className="text-sm text-destructive">{errors.ttsModel}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="backgroundSound">Background Sound</Label>
          <Select
            value={config.backgroundSound || 'none'}
            onValueChange={(value) => updateConfig({ backgroundSound: value === 'none' ? undefined : value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select background sound" />
            </SelectTrigger>
            <SelectContent>
              {BACKGROUND_SOUNDS.map((sound) => (
                <SelectItem key={sound.value} value={sound.value}>
                  {sound.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {config.backgroundSound === 'custom' && (
          <div className="space-y-2">
            <Label htmlFor="backgroundSoundUrl">Background Sound URL</Label>
            <Input
              id="backgroundSoundUrl"
              value={config.backgroundSoundUrl || ''}
              onChange={(e) => updateConfig({ backgroundSoundUrl: e.target.value })}
              placeholder="https://example.com/background-sound.mp3"
              type="url"
            />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="minCharacters">Minimum number of characters</Label>
            <Input
              id="minCharacters"
              type="number"
              value={config.minCharacters}
              onChange={(e) => updateConfig({ minCharacters: parseInt(e.target.value) || 0 })}
              placeholder="10"
              min="1"
              max="1000"
            />
            <p className="text-sm text-muted-foreground">
              Input length before starting voice output
            </p>
          </div>

          <div className="space-y-4">
            <Label>Speech speed: {config.speechSpeed}x</Label>
            <div className="px-2">
              <input
                type="range"
                value={config.speechSpeed}
                onChange={(e) => updateConfig({ speechSpeed: parseFloat(e.target.value) })}
                max={2}
                min={0.5}
                step={0.1}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Voice output speed (0.5x - 2.0x)
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}