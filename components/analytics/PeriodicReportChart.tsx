// Periodic Report Chart Component - Story 4 Phase 2

'use client'

import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveC<PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import { PeriodicReport, AnalyticsTimeRange } from '@/types/analytics'

interface PeriodicReportChartProps {
  data: PeriodicReport
  timeRange: AnalyticsTimeRange
  className?: string
}

export function PeriodicReportChart({ 
  data, 
  timeRange, 
  className = '' 
}: PeriodicReportChartProps) {
  // Farben für die Charts
  const colors = {
    primary: '#3B82F6',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    secondary: '#6B7280'
  }

  // Peak Hours Chart Data vorbereiten
  const peakHoursData = data.metrics.peakHours
    ?.slice(0, 12) // Top 12 Stunden
    .map(peak => ({
      hour: `${peak.hour}:00`,
      gespräche: peak.count,
      stunde: peak.hour
    })) || []

  // Success Rate Trend (<PERSON><PERSON>-<PERSON><PERSON> für Demo)
  const successTrendData = [
    { tag: 'Mo', erfolgsrate: 87.5, gespräche: 45 },
    { tag: 'Di', erfolgsrate: 92.1, gespräche: 52 },
    { tag: 'Mi', erfolgsrate: 89.3, gespräche: 48 },
    { tag: 'Do', erfolgsrate: 94.2, gespräche: 61 },
    { tag: 'Fr', erfolgsrate: 88.7, gespräche: 43 },
    { tag: 'Sa', erfolgsrate: 85.4, gespräche: 32 },
    { tag: 'So', erfolgsrate: 91.8, gespräche: 38 }
  ]

  // Agent Performance Data (Mock)
  const agentPerformanceData = data.metrics.agentPerformance?.slice(0, 6).map(agent => ({
    name: agent.name || `Agent ${agent.id}`,
    gespräche: agent.totalConversations || 0,
    erfolgsrate: agent.successRate || 0
  })) || [
    { name: 'Agent Alpha', gespräche: 45, erfolgsrate: 92.5 },
    { name: 'Agent Beta', gespräche: 38, erfolgsrate: 88.2 },
    { name: 'Agent Gamma', gespräche: 52, erfolgsrate: 94.1 },
    { name: 'Agent Delta', gespräche: 41, erfolgsrate: 87.8 }
  ]

  // Conversation Distribution Data
  const distributionData = [
    { name: 'Erfolgreich', value: data.metrics.successfulConversations, color: colors.success },
    { name: 'Fehlgeschlagen', value: data.metrics.totalConversations - data.metrics.successfulConversations, color: colors.danger }
  ]

  // Custom Tooltip für bessere UX
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.name.includes('rate') ? '%' : ''}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Peak Hours Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Gespräche nach Tageszeit
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={peakHoursData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="hour" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="gespräche" 
                fill={colors.primary}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Success Rate Trend */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Erfolgsrate-Trend
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={successTrendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="tag" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                domain={[80, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="erfolgsrate" 
                stroke={colors.success}
                strokeWidth={3}
                dot={{ fill: colors.success, strokeWidth: 2, r: 4 }}
                name="Erfolgsrate (%)"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Agent Performance */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Agent-Performance
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={agentPerformanceData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                type="number"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                type="category"
                dataKey="name"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
                width={80}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                dataKey="gespräche" 
                fill={colors.primary}
                name="Gespräche"
                radius={[0, 4, 4, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Conversation Distribution */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Gesprächsverteilung
        </h3>
        <div className="flex items-center">
          <div className="h-64 flex-1">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [value.toLocaleString('de-DE'), 'Gespräche']}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="ml-6 space-y-4">
            {distributionData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-4 h-4 rounded mr-3"
                  style={{ backgroundColor: item.color }}
                />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {item.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {item.value.toLocaleString('de-DE')} Gespräche
                  </p>
                  <p className="text-xs text-gray-400">
                    {((item.value / data.metrics.totalConversations) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-gray-900">
              {data.metrics.totalConversations.toLocaleString('de-DE')}
            </p>
            <p className="text-sm text-gray-600">Gesamtgespräche</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-green-600">
              {data.metrics.successRate.toFixed(1)}%
            </p>
            <p className="text-sm text-gray-600">Erfolgsrate</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-blue-600">
              {Math.round(data.metrics.averageDuration / 60)}m
            </p>
            <p className="text-sm text-gray-600">Ø Dauer</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-purple-600">
              {data.metrics.peakHours?.[0]?.hour || 'N/A'}:00
            </p>
            <p className="text-sm text-gray-600">Peak-Stunde</p>
          </div>
        </div>
      </div>
    </div>
  )
}
