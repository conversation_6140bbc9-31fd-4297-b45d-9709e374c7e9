// Periodische Reports Dashboard - Story 4 Phase 2

'use client'

import { useState } from 'react'
import { 
  CalendarIcon, 
  ChartBarIcon, 
  DocumentArrowDownIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { usePeriodicReport, useAnalyticsQuery } from '@/hooks/useAnalyticsData'
import { useReportGeneration } from '@/hooks/useReportGeneration'
import { ReportConfigurationModal } from './ReportConfigurationModal'
import { ReportExportOptions } from './ReportExportOptions'
import { PeriodicReportChart } from './PeriodicReportChart'
import { PeriodicReportMetrics } from './PeriodicReportMetrics'
import { PeriodicReportTable } from './PeriodicReportTable'

interface PeriodicReportDashboardProps {
  className?: string
}

export function PeriodicReportDashboard({ className = '' }: PeriodicReportDashboardProps) {
  const [showConfigModal, setShowConfigModal] = useState(false)
  const [showExportOptions, setShowExportOptions] = useState(false)
  
  // Analytics Query Management
  const { 
    query, 
    updateTimeRange, 
    updateAgentIds, 
    setPresetRange 
  } = useAnalyticsQuery({
    timeRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 Tage zurück
      endDate: new Date(),
      period: 'monthly'
    }
  })

  // Data Fetching
  const { data, loading, error, refetch } = usePeriodicReport(query)
  
  // Report Generation
  const { generateReport, loading: generatingReport } = useReportGeneration()

  // Event Handlers
  const handleTimeRangeChange = (preset: string) => {
    setPresetRange(preset as any)
  }

  const handleExportReport = async (format: 'pdf' | 'excel' | 'csv') => {
    if (!data) return

    await generateReport({
      reportType: 'periodic',
      timeRange: query.timeRange,
      config: {
        format,
        language: 'de',
        includeCharts: true,
        agentIds: query.agentIds
      }
    })
  }

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Periodische Reports
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Übersicht über Agent-Performance und Gesprächsstatistiken
          </p>
        </div>
        
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowConfigModal(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Zeitraum konfigurieren
          </button>
          
          <button
            onClick={() => setShowExportOptions(true)}
            disabled={!data || loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Report exportieren
          </button>
        </div>
      </div>

      {/* Quick Time Range Selector */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'today', label: 'Heute' },
            { key: 'yesterday', label: 'Gestern' },
            { key: 'last7days', label: 'Letzte 7 Tage' },
            { key: 'last30days', label: 'Letzte 30 Tage' },
            { key: 'last90days', label: 'Letzte 90 Tage' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => handleTimeRangeChange(key)}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-lg shadow p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Lade Report-Daten...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-red-800">{error}</span>
            <button
              onClick={refetch}
              className="ml-auto text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Erneut versuchen
            </button>
          </div>
        </div>
      )}

      {/* Report Content */}
      {data && !loading && (
        <>
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Gesamtgespräche
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.data.metrics.totalConversations.toLocaleString('de-DE')}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Erfolgsrate
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(data.data.metrics.successRate)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Ø Gesprächsdauer
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatDuration(Math.round(data.data.metrics.averageDuration))}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UserGroupIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Erfolgreiche Gespräche
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.data.metrics.successfulConversations.toLocaleString('de-DE')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <PeriodicReportChart 
              data={data.data}
              timeRange={query.timeRange}
            />
            
            <PeriodicReportMetrics 
              metrics={data.data.metrics}
              timeRange={query.timeRange}
            />
          </div>

          {/* Detailed Table */}
          <PeriodicReportTable 
            data={data.data}
            timeRange={query.timeRange}
          />

          {/* Report Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>
                Report generiert am: {data.generatedAt.toLocaleString('de-DE')}
              </span>
              <span>
                Zeitraum: {query.timeRange.startDate.toLocaleDateString('de-DE')} - {query.timeRange.endDate.toLocaleDateString('de-DE')}
              </span>
              <span>
                Datensätze: {data.totalRecords.toLocaleString('de-DE')}
              </span>
            </div>
          </div>
        </>
      )}

      {/* Modals */}
      {showConfigModal && (
        <ReportConfigurationModal
          currentQuery={query}
          onSave={(newQuery) => {
            updateTimeRange(newQuery.timeRange)
            updateAgentIds(newQuery.agentIds || [])
            setShowConfigModal(false)
          }}
          onClose={() => setShowConfigModal(false)}
        />
      )}

      {showExportOptions && (
        <ReportExportOptions
          onExport={handleExportReport}
          onClose={() => setShowExportOptions(false)}
          loading={generatingReport}
        />
      )}
    </div>
  )
}
