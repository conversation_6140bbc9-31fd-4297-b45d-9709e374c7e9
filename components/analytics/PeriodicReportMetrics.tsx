// Periodic Report Metrics Component - Story 4 Phase 2

'use client'

import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { PeriodicReport, AnalyticsTimeRange } from '@/types/analytics'

interface PeriodicReportMetricsProps {
  metrics: PeriodicReport['metrics']
  timeRange: AnalyticsTimeRange
  className?: string
}

export function PeriodicReportMetrics({ 
  metrics, 
  timeRange, 
  className = '' 
}: PeriodicReportMetricsProps) {
  
  // Hilfsfunktionen
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  const getSuccessRateColor = (rate: number): string => {
    if (rate >= 90) return 'text-green-600'
    if (rate >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSuccessRateBgColor = (rate: number): string => {
    if (rate >= 90) return 'bg-green-50 border-green-200'
    if (rate >= 80) return 'bg-yellow-50 border-yellow-200'
    return 'bg-red-50 border-red-200'
  }

  // Mock-Trend-Daten (in echter App würde das aus vorherigen Perioden berechnet)
  const trends = {
    totalConversations: 12.5, // +12.5% vs. vorherige Periode
    successRate: -2.1, // -2.1% vs. vorherige Periode
    averageDuration: 8.3 // +8.3% vs. vorherige Periode
  }

  const TrendIndicator = ({ value, suffix = '' }: { value: number; suffix?: string }) => {
    const isPositive = value > 0
    const Icon = isPositive ? TrendingUpIcon : TrendingDownIcon
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600'
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span className="text-sm font-medium">
          {Math.abs(value).toFixed(1)}{suffix}
        </span>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Hauptmetriken */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Kernmetriken
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Gesamtgespräche */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Gesamtgespräche
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {metrics.totalConversations.toLocaleString('de-DE')}
                  </p>
                </div>
              </div>
              <TrendIndicator value={trends.totalConversations} suffix="%" />
            </div>
          </div>

          {/* Erfolgsrate */}
          <div className={`border rounded-lg p-4 ${getSuccessRateBgColor(metrics.successRate)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircleIcon className={`h-8 w-8 mr-3 ${getSuccessRateColor(metrics.successRate)}`} />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Erfolgsrate
                  </p>
                  <p className={`text-2xl font-bold ${getSuccessRateColor(metrics.successRate)}`}>
                    {formatPercentage(metrics.successRate)}
                  </p>
                </div>
              </div>
              <TrendIndicator value={trends.successRate} suffix="%" />
            </div>
          </div>

          {/* Durchschnittliche Dauer */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-yellow-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Ø Gesprächsdauer
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatDuration(Math.round(metrics.averageDuration))}
                  </p>
                </div>
              </div>
              <TrendIndicator value={trends.averageDuration} suffix="%" />
            </div>
          </div>
        </div>
      </div>

      {/* Peak Hours Analyse */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Peak-Stunden Analyse
        </h3>
        
        {metrics.peakHours && metrics.peakHours.length > 0 ? (
          <div className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {metrics.peakHours.slice(0, 3).map((peak, index) => (
                <div key={peak.hour} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">
                        {index === 0 ? 'Höchste' : index === 1 ? '2. Höchste' : '3. Höchste'} Aktivität
                      </p>
                      <p className="text-lg font-bold text-gray-900">
                        {peak.hour}:00 Uhr
                      </p>
                      <p className="text-sm text-gray-600">
                        {peak.count} Gespräche
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-bold">
                          #{index + 1}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Peak Hours Verteilung */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Stündliche Verteilung (Top 8)
              </h4>
              <div className="space-y-2">
                {metrics.peakHours.slice(0, 8).map((peak) => {
                  const maxCount = Math.max(...metrics.peakHours.map(p => p.count))
                  const percentage = (peak.count / maxCount) * 100
                  
                  return (
                    <div key={peak.hour} className="flex items-center">
                      <div className="w-16 text-sm text-gray-600">
                        {peak.hour}:00
                      </div>
                      <div className="flex-1 mx-3">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                      <div className="w-12 text-sm text-gray-900 font-medium text-right">
                        {peak.count}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">
            Keine Peak-Stunden-Daten verfügbar
          </p>
        )}
      </div>

      {/* Erfolg vs. Fehlschlag Aufschlüsselung */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Gesprächsergebnisse
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Erfolgreiche Gespräche */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-green-800">
                  Erfolgreiche Gespräche
                </p>
                <p className="text-2xl font-bold text-green-900">
                  {metrics.successfulConversations.toLocaleString('de-DE')}
                </p>
                <p className="text-sm text-green-700">
                  {formatPercentage(metrics.successRate)} der Gesamtgespräche
                </p>
              </div>
            </div>
          </div>

          {/* Fehlgeschlagene Gespräche */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <XCircleIcon className="h-8 w-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-red-800">
                  Fehlgeschlagene Gespräche
                </p>
                <p className="text-2xl font-bold text-red-900">
                  {(metrics.totalConversations - metrics.successfulConversations).toLocaleString('de-DE')}
                </p>
                <p className="text-sm text-red-700">
                  {formatPercentage(100 - metrics.successRate)} der Gesamtgespräche
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Agent Performance Summary */}
      {metrics.agentPerformance && metrics.agentPerformance.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Agent-Performance Übersicht
          </h3>
          
          <div className="space-y-3">
            {metrics.agentPerformance.slice(0, 5).map((agent, index) => (
              <div key={agent.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <UserGroupIcon className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {agent.name || `Agent ${agent.id}`}
                    </p>
                    <p className="text-sm text-gray-500">
                      {agent.totalConversations || 0} Gespräche
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-bold ${getSuccessRateColor(agent.successRate || 0)}`}>
                    {formatPercentage(agent.successRate || 0)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Erfolgsrate
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Zeitraum-Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <ClockIcon className="h-5 w-5 text-blue-600 mr-2" />
          <div className="text-sm text-blue-800">
            <span className="font-medium">Berichtszeitraum:</span>
            {' '}
            {timeRange.startDate.toLocaleDateString('de-DE')} - {timeRange.endDate.toLocaleDateString('de-DE')}
            {' '}
            ({timeRange.period === 'daily' ? 'Täglich' : timeRange.period === 'weekly' ? 'Wöchentlich' : 'Monatlich'})
          </div>
        </div>
      </div>
    </div>
  )
}
