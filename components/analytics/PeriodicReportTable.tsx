// Periodic Report Table Component - Story 4 Phase 2

'use client'

import { useState } from 'react'
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { PeriodicReport, AnalyticsTimeRange } from '@/types/analytics'

interface PeriodicReportTableProps {
  data: PeriodicReport
  timeRange: AnalyticsTimeRange
  className?: string
}

type SortField = 'name' | 'conversations' | 'successRate' | 'avgDuration'
type SortDirection = 'asc' | 'desc'

export function PeriodicReportTable({ 
  data, 
  timeRange, 
  className = '' 
}: PeriodicReportTableProps) {
  const [sortField, setSortField] = useState<SortField>('conversations')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterSuccessRate, setFilterSuccessRate] = useState<'all' | 'high' | 'medium' | 'low'>('all')

  // Mock-Daten für detaillierte Agent-Performance (in echter App aus data.metrics.agentPerformance)
  const agentData = data.metrics.agentPerformance?.length > 0 
    ? data.metrics.agentPerformance.map(agent => ({
        id: agent.id,
        name: agent.name || `Agent ${agent.id}`,
        conversations: agent.totalConversations || 0,
        successfulConversations: agent.successfulConversations || 0,
        successRate: agent.successRate || 0,
        avgDuration: agent.averageDuration || 0,
        lastActive: agent.lastActive || new Date()
      }))
    : [
        {
          id: 'agent-1',
          name: 'Agent Alpha',
          conversations: 45,
          successfulConversations: 42,
          successRate: 93.3,
          avgDuration: 285,
          lastActive: new Date('2024-01-15T14:30:00')
        },
        {
          id: 'agent-2', 
          name: 'Agent Beta',
          conversations: 38,
          successfulConversations: 33,
          successRate: 86.8,
          avgDuration: 312,
          lastActive: new Date('2024-01-15T16:45:00')
        },
        {
          id: 'agent-3',
          name: 'Agent Gamma', 
          conversations: 52,
          successfulConversations: 49,
          successRate: 94.2,
          avgDuration: 267,
          lastActive: new Date('2024-01-15T15:20:00')
        },
        {
          id: 'agent-4',
          name: 'Agent Delta',
          conversations: 41,
          successfulConversations: 36,
          successRate: 87.8,
          avgDuration: 298,
          lastActive: new Date('2024-01-15T13:15:00')
        },
        {
          id: 'agent-5',
          name: 'Agent Epsilon',
          conversations: 29,
          successfulConversations: 25,
          successRate: 86.2,
          avgDuration: 334,
          lastActive: new Date('2024-01-15T17:10:00')
        }
      ]

  // Filtering
  const filteredData = agentData.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesFilter = true
    switch (filterSuccessRate) {
      case 'high':
        matchesFilter = agent.successRate >= 90
        break
      case 'medium':
        matchesFilter = agent.successRate >= 80 && agent.successRate < 90
        break
      case 'low':
        matchesFilter = agent.successRate < 80
        break
    }
    
    return matchesSearch && matchesFilter
  })

  // Sorting
  const sortedData = [...filteredData].sort((a, b) => {
    let aValue: any
    let bValue: any
    
    switch (sortField) {
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'conversations':
        aValue = a.conversations
        bValue = b.conversations
        break
      case 'successRate':
        aValue = a.successRate
        bValue = b.successRate
        break
      case 'avgDuration':
        aValue = a.avgDuration
        bValue = b.avgDuration
        break
      default:
        return 0
    }
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <div className="w-4 h-4" />
    }
    return sortDirection === 'asc' 
      ? <ChevronUpIcon className="w-4 h-4" />
      : <ChevronDownIcon className="w-4 h-4" />
  }

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getSuccessRateColor = (rate: number): string => {
    if (rate >= 90) return 'text-green-600 bg-green-50'
    if (rate >= 80) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Detaillierte Agent-Performance
          </h3>
          
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            {/* Suche */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Agent suchen..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {/* Filter */}
            <div className="relative">
              <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={filterSuccessRate}
                onChange={(e) => setFilterSuccessRate(e.target.value as any)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white"
              >
                <option value="all">Alle Erfolgsraten</option>
                <option value="high">Hoch (≥90%)</option>
                <option value="medium">Mittel (80-89%)</option>
                <option value="low">Niedrig (&lt;80%)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-1">
                  <span>Agent</span>
                  <SortIcon field="name" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('conversations')}
              >
                <div className="flex items-center space-x-1">
                  <span>Gespräche</span>
                  <SortIcon field="conversations" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('successRate')}
              >
                <div className="flex items-center space-x-1">
                  <span>Erfolgsrate</span>
                  <SortIcon field="successRate" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('avgDuration')}
              >
                <div className="flex items-center space-x-1">
                  <span>Ø Dauer</span>
                  <SortIcon field="avgDuration" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Letzte Aktivität
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedData.map((agent) => (
              <tr key={agent.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {agent.name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {agent.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {agent.id}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {agent.conversations.toLocaleString('de-DE')}
                  </div>
                  <div className="text-sm text-gray-500">
                    {agent.successfulConversations} erfolgreich
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSuccessRateColor(agent.successRate)}`}>
                    {agent.successRate.toFixed(1)}%
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDuration(agent.avgDuration)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {agent.lastActive.toLocaleDateString('de-DE')}
                  <br />
                  {agent.lastActive.toLocaleTimeString('de-DE', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {sortedData.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">
            Keine Agents gefunden, die den Filterkriterien entsprechen.
          </p>
        </div>
      )}

      {/* Tabellen-Footer mit Zusammenfassung */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-600">
          <div>
            Zeige {sortedData.length} von {agentData.length} Agents
          </div>
          <div className="mt-2 sm:mt-0">
            Zeitraum: {timeRange.startDate.toLocaleDateString('de-DE')} - {timeRange.endDate.toLocaleDateString('de-DE')}
          </div>
        </div>
      </div>
    </div>
  )
}
