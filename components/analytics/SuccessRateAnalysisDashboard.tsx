// Success Rate Analysis Dashboard - Story 4 Phase 3

'use client'

import { useState } from 'react'
import { 
  ChartBarIcon,
  Cog6ToothIcon,
  DocumentArrowDownIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'
import { useSuccessRateAnalysis } from '@/hooks/useAnalyticsData'
import { AnalyticsQuery } from '@/types/analytics'
import { ReportConfigurationModal } from './ReportConfigurationModal'
import { ReportExportOptions } from './ReportExportOptions'
import { SuccessRateChart } from './SuccessRateChart'
import { SuccessRateMetrics } from './SuccessRateMetrics'
import { SuccessRateTable } from './SuccessRateTable'
import { SuccessRateTrendAnalysis } from './SuccessRateTrendAnalysis'

export function SuccessRateAnalysisDashboard() {
  const [query, setQuery] = useState<AnalyticsQuery>({
    timeRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Letzte 30 Tage
      endDate: new Date(),
      period: 'daily'
    }
  })

  const [showConfigModal, setShowConfigModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)

  const { data, isLoading, error } = useSuccessRateAnalysis(query)

  const handleConfigSave = (newQuery: AnalyticsQuery) => {
    setQuery(newQuery)
    setShowConfigModal(false)
  }

  const handleExport = (format: 'pdf' | 'excel' | 'csv', options?: any) => {
    console.log('Exporting success rate analysis:', { format, options, query })
    // TODO: Implement export functionality
    setShowExportModal(false)
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-2">⚠️ Fehler beim Laden</div>
          <p className="text-gray-600">{error.message}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center">
            <ChartBarIcon className="h-8 w-8 text-green-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Erfolgsrate-Analyse
              </h1>
              <p className="text-gray-600">
                Detaillierte Analyse der Gesprächserfolgsraten nach verschiedenen Dimensionen
              </p>
            </div>
          </div>
          
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => setShowConfigModal(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Cog6ToothIcon className="h-4 w-4 mr-2" />
              Konfigurieren
            </button>
            
            <button
              onClick={() => setShowExportModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
              Exportieren
            </button>
          </div>
        </div>

        {/* Zeitraum-Anzeige */}
        <div className="mt-4 flex items-center text-sm text-gray-500">
          <CalendarIcon className="h-4 w-4 mr-2" />
          <span>
            {query.timeRange.startDate.toLocaleDateString('de-DE')} - {query.timeRange.endDate.toLocaleDateString('de-DE')}
            {query.agentIds && query.agentIds.length > 0 && (
              <span className="ml-4">
                • {query.agentIds.length} Agent(s) ausgewählt
              </span>
            )}
          </span>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          {/* Loading Skeletons */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      ) : data ? (
        <>
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Gesamt-Erfolgsrate
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.data.overallSuccessRate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Beste Erfolgsrate
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.max(...data.data.byAgent.map(a => a.successRate)).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Durchschnitt
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(data.data.byAgent.reduce((sum, a) => sum + a.successRate, 0) / data.data.byAgent.length).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Analysierte Gespräche
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.data.totalConversations.toLocaleString('de-DE')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts and Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <SuccessRateChart 
              data={data.data}
              timeRange={query.timeRange}
            />
            
            <SuccessRateMetrics 
              data={data.data}
              timeRange={query.timeRange}
            />
          </div>

          {/* Trend Analysis */}
          <SuccessRateTrendAnalysis 
            data={data.data}
            timeRange={query.timeRange}
          />

          {/* Detailed Table */}
          <SuccessRateTable 
            data={data.data}
            timeRange={query.timeRange}
          />
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">Keine Daten verfügbar</p>
        </div>
      )}

      {/* Modals */}
      {showConfigModal && (
        <ReportConfigurationModal
          currentQuery={query}
          onSave={handleConfigSave}
          onClose={() => setShowConfigModal(false)}
        />
      )}

      {showExportModal && (
        <ReportExportOptions
          onExport={handleExport}
          onClose={() => setShowExportModal(false)}
        />
      )}
    </div>
  )
}
