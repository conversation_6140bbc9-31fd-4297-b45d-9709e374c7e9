// Success Rate Chart Component - Story 4 Phase 3

'use client'

import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart,
  Area,
  AreaChart
} from 'recharts'
import { SuccessRateAnalysis, AnalyticsTimeRange } from '@/types/analytics'

interface SuccessRateChartProps {
  data: SuccessRateAnalysis
  timeRange: AnalyticsTimeRange
  className?: string
}

export function SuccessRateChart({ 
  data, 
  timeRange, 
  className = '' 
}: SuccessRateChartProps) {
  
  // Farben für die Charts
  const colors = {
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    primary: '#3B82F6',
    secondary: '#6B7280'
  }

  // Zeitbasierte Erfolgsrate-Daten vorbereiten
  const timeBasedData = data.byTimeOfDay?.map(item => ({
    stunde: `${item.hour}:00`,
    erfolgsrate: item.successRate,
    gespräche: item.totalConversations,
    erfolgreich: item.successfulConversations
  })) || []

  // Agent-Vergleichsdaten
  const agentComparisonData = data.byAgent?.slice(0, 8).map(agent => ({
    name: agent.name || `Agent ${agent.id}`,
    erfolgsrate: agent.successRate,
    gespräche: agent.totalConversations,
    erfolgreich: agent.successfulConversations
  })) || []

  // Wöchentliche Trend-Daten (Mock für Demo)
  const weeklyTrendData = [
    { woche: 'KW 1', erfolgsrate: 87.2, ziel: 90, gespräche: 245 },
    { woche: 'KW 2', erfolgsrate: 89.1, ziel: 90, gespräche: 267 },
    { woche: 'KW 3', erfolgsrate: 91.5, ziel: 90, gespräche: 289 },
    { woche: 'KW 4', erfolgsrate: 88.7, ziel: 90, gespräche: 234 },
    { woche: 'KW 5', erfolgsrate: 92.3, ziel: 90, gespräche: 312 },
    { woche: 'KW 6', erfolgsrate: 90.8, ziel: 90, gespräche: 298 }
  ]

  // Custom Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.name.includes('rate') ? '%' : ''}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Erfolgsrate nach Tageszeit */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Erfolgsrate nach Tageszeit
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={timeBasedData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="stunde" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="rate"
                orientation="left"
                domain={[70, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="count"
                orientation="right"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                yAxisId="count"
                dataKey="gespräche" 
                fill={colors.secondary}
                opacity={0.3}
                name="Gespräche"
              />
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="erfolgsrate" 
                stroke={colors.success}
                strokeWidth={3}
                dot={{ fill: colors.success, strokeWidth: 2, r: 4 }}
                name="Erfolgsrate (%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Agent-Vergleich */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Erfolgsrate nach Agent
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={agentComparisonData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                type="number"
                domain={[0, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                type="category"
                dataKey="name"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
                width={80}
              />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  name === 'erfolgsrate' ? `${value.toFixed(1)}%` : value,
                  name === 'erfolgsrate' ? 'Erfolgsrate' : 'Gespräche'
                ]}
              />
              <Bar 
                dataKey="erfolgsrate" 
                fill={colors.success}
                radius={[0, 4, 4, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Wöchentlicher Trend mit Ziel */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Wöchentlicher Erfolgsrate-Trend
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={weeklyTrendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="woche" 
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="rate"
                domain={[80, 100]}
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <YAxis 
                yAxisId="count"
                orientation="right"
                tick={{ fontSize: 12 }}
                stroke="#6B7280"
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Zielbereich als Area */}
              <Area
                yAxisId="rate"
                type="monotone"
                dataKey="ziel"
                stroke="none"
                fill={colors.success}
                fillOpacity={0.1}
                name="Zielbereich"
              />
              
              {/* Gespräche als Balken */}
              <Bar 
                yAxisId="count"
                dataKey="gespräche" 
                fill={colors.secondary}
                opacity={0.3}
                name="Gespräche"
              />
              
              {/* Erfolgsrate als Linie */}
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="erfolgsrate" 
                stroke={colors.success}
                strokeWidth={3}
                dot={{ fill: colors.success, strokeWidth: 2, r: 5 }}
                name="Erfolgsrate (%)"
              />
              
              {/* Ziellinie */}
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="ziel" 
                stroke={colors.warning}
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                name="Ziel (90%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Erfolgsrate-Verteilung */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Erfolgsrate-Kategorien
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Hoch (≥90%) */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">
                  Hoch (≥90%)
                </p>
                <p className="text-2xl font-bold text-green-900">
                  {data.byAgent?.filter(a => a.successRate >= 90).length || 0}
                </p>
                <p className="text-sm text-green-700">
                  Agents
                </p>
              </div>
              <div className="text-green-600">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold">
                    {Math.round(((data.byAgent?.filter(a => a.successRate >= 90).length || 0) / (data.byAgent?.length || 1)) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Mittel (80-89%) */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Mittel (80-89%)
                </p>
                <p className="text-2xl font-bold text-yellow-900">
                  {data.byAgent?.filter(a => a.successRate >= 80 && a.successRate < 90).length || 0}
                </p>
                <p className="text-sm text-yellow-700">
                  Agents
                </p>
              </div>
              <div className="text-yellow-600">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold">
                    {Math.round(((data.byAgent?.filter(a => a.successRate >= 80 && a.successRate < 90).length || 0) / (data.byAgent?.length || 1)) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Niedrig (<80%) */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-800">
                  Niedrig (&lt;80%)
                </p>
                <p className="text-2xl font-bold text-red-900">
                  {data.byAgent?.filter(a => a.successRate < 80).length || 0}
                </p>
                <p className="text-sm text-red-700">
                  Agents
                </p>
              </div>
              <div className="text-red-600">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold">
                    {Math.round(((data.byAgent?.filter(a => a.successRate < 80).length || 0) / (data.byAgent?.length || 1)) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
