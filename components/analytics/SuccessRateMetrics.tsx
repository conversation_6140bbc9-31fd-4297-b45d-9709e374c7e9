// Success Rate Metrics Component - Story 4 Phase 3

'use client'

import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { SuccessRateAnalysis, AnalyticsTimeRange } from '@/types/analytics'

interface SuccessRateMetricsProps {
  data: SuccessRateAnalysis
  timeRange: AnalyticsTimeRange
  className?: string
}

export function SuccessRateMetrics({ 
  data, 
  timeRange, 
  className = '' 
}: SuccessRateMetricsProps) {
  
  // Hilfsfunktionen
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`
  }

  const getSuccessRateColor = (rate: number): string => {
    if (rate >= 90) return 'text-green-600'
    if (rate >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSuccessRateBgColor = (rate: number): string => {
    if (rate >= 90) return 'bg-green-50 border-green-200'
    if (rate >= 80) return 'bg-yellow-50 border-yellow-200'
    return 'bg-red-50 border-red-200'
  }

  // Berechnungen
  const bestAgent = data.byAgent?.reduce((best, current) => 
    current.successRate > best.successRate ? current : best
  )
  
  const worstAgent = data.byAgent?.reduce((worst, current) => 
    current.successRate < worst.successRate ? current : worst
  )

  const averageSuccessRate = data.byAgent?.length > 0 
    ? data.byAgent.reduce((sum, agent) => sum + agent.successRate, 0) / data.byAgent.length
    : 0

  const bestTimeSlot = data.byTimeOfDay?.reduce((best, current) => 
    current.successRate > best.successRate ? current : best
  )

  const worstTimeSlot = data.byTimeOfDay?.reduce((worst, current) => 
    current.successRate < worst.successRate ? current : worst
  )

  // Mock-Trend-Daten (in echter App aus historischen Daten)
  const trends = {
    overallChange: 2.3, // +2.3% vs. vorherige Periode
    bestAgentChange: 1.8,
    worstAgentChange: -0.5
  }

  const TrendIndicator = ({ value, suffix = '%' }: { value: number; suffix?: string }) => {
    const isPositive = value > 0
    const Icon = isPositive ? TrendingUpIcon : TrendingDownIcon
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600'
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span className="text-sm font-medium">
          {Math.abs(value).toFixed(1)}{suffix}
        </span>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Gesamt-Erfolgsrate */}
      <div className={`border rounded-lg p-6 ${getSuccessRateBgColor(data.overallSuccessRate)}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CheckCircleIcon className={`h-8 w-8 mr-3 ${getSuccessRateColor(data.overallSuccessRate)}`} />
            <div>
              <p className="text-sm font-medium text-gray-500">
                Gesamt-Erfolgsrate
              </p>
              <p className={`text-3xl font-bold ${getSuccessRateColor(data.overallSuccessRate)}`}>
                {formatPercentage(data.overallSuccessRate)}
              </p>
              <p className="text-sm text-gray-600">
                {data.totalConversations.toLocaleString('de-DE')} Gespräche analysiert
              </p>
            </div>
          </div>
          <TrendIndicator value={trends.overallChange} />
        </div>
      </div>

      {/* Top & Bottom Performer */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Bester Agent */}
        {bestAgent && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <StarIcon className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-green-800">
                    Bester Agent
                  </p>
                  <p className="text-lg font-bold text-green-900">
                    {bestAgent.name || `Agent ${bestAgent.id}`}
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatPercentage(bestAgent.successRate)}
                  </p>
                  <p className="text-sm text-green-700">
                    {bestAgent.totalConversations} Gespräche
                  </p>
                </div>
              </div>
              <TrendIndicator value={trends.bestAgentChange} />
            </div>
          </div>
        )}

        {/* Schlechtester Agent */}
        {worstAgent && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-red-800">
                    Verbesserungsbedarf
                  </p>
                  <p className="text-lg font-bold text-red-900">
                    {worstAgent.name || `Agent ${worstAgent.id}`}
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {formatPercentage(worstAgent.successRate)}
                  </p>
                  <p className="text-sm text-red-700">
                    {worstAgent.totalConversations} Gespräche
                  </p>
                </div>
              </div>
              <TrendIndicator value={trends.worstAgentChange} />
            </div>
          </div>
        )}
      </div>

      {/* Zeitbasierte Analyse */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Zeitbasierte Erfolgsrate-Analyse
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Beste Tageszeit */}
          {bestTimeSlot && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    Beste Tageszeit
                  </p>
                  <p className="text-lg font-bold text-blue-900">
                    {bestTimeSlot.hour}:00 - {bestTimeSlot.hour + 1}:00 Uhr
                  </p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatPercentage(bestTimeSlot.successRate)}
                  </p>
                  <p className="text-sm text-blue-700">
                    {bestTimeSlot.totalConversations} Gespräche
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Schlechteste Tageszeit */}
          {worstTimeSlot && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 text-orange-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-orange-800">
                    Verbesserungspotential
                  </p>
                  <p className="text-lg font-bold text-orange-900">
                    {worstTimeSlot.hour}:00 - {worstTimeSlot.hour + 1}:00 Uhr
                  </p>
                  <p className="text-xl font-bold text-orange-600">
                    {formatPercentage(worstTimeSlot.successRate)}
                  </p>
                  <p className="text-sm text-orange-700">
                    {worstTimeSlot.totalConversations} Gespräche
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Statistische Kennzahlen */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Statistische Kennzahlen
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Durchschnitt */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <UserGroupIcon className="h-6 w-6 text-gray-600 mr-2" />
              <span className="text-sm font-medium text-gray-500">
                Durchschnitt
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatPercentage(averageSuccessRate)}
            </p>
          </div>

          {/* Spannweite */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <span className="text-sm font-medium text-gray-500">
                Spannweite
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {bestAgent && worstAgent 
                ? formatPercentage(bestAgent.successRate - worstAgent.successRate)
                : 'N/A'
              }
            </p>
          </div>

          {/* Standardabweichung (vereinfacht) */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <span className="text-sm font-medium text-gray-500">
                Varianz
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {data.byAgent?.length > 0 
                ? formatPercentage(
                    Math.sqrt(
                      data.byAgent.reduce((sum, agent) => 
                        sum + Math.pow(agent.successRate - averageSuccessRate, 2), 0
                      ) / data.byAgent.length
                    )
                  )
                : 'N/A'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Empfehlungen */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">
          📊 Analyse-Empfehlungen
        </h3>
        
        <div className="space-y-3 text-sm text-blue-800">
          {data.overallSuccessRate < 85 && (
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <p>
                Die Gesamt-Erfolgsrate liegt unter 85%. Überprüfen Sie die Agent-Konfigurationen und Trainingsqualität.
              </p>
            </div>
          )}
          
          {bestAgent && worstAgent && (bestAgent.successRate - worstAgent.successRate) > 20 && (
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <p>
                Große Leistungsunterschiede zwischen Agents ({formatPercentage(bestAgent.successRate - worstAgent.successRate)} Spannweite). 
                Erwägen Sie eine Standardisierung der Agent-Konfigurationen.
              </p>
            </div>
          )}
          
          {bestTimeSlot && worstTimeSlot && (bestTimeSlot.successRate - worstTimeSlot.successRate) > 15 && (
            <div className="flex items-start">
              <ClockIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <p>
                Deutliche zeitbasierte Leistungsunterschiede. Optimieren Sie die Agent-Verfügbarkeit für die Zeiten um {bestTimeSlot.hour}:00 Uhr.
              </p>
            </div>
          )}
          
          {data.overallSuccessRate >= 90 && (
            <div className="flex items-start">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
              <p className="text-green-800">
                Ausgezeichnete Erfolgsrate! Dokumentieren Sie die aktuellen Best Practices für zukünftige Optimierungen.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Zeitraum-Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center text-sm text-gray-600">
          <ClockIcon className="h-4 w-4 mr-2" />
          <span>
            <span className="font-medium">Analysezeitraum:</span>
            {' '}
            {timeRange.startDate.toLocaleDateString('de-DE')} - {timeRange.endDate.toLocaleDateString('de-DE')}
          </span>
        </div>
      </div>
    </div>
  )
}
