"use client"

import { useTheme } from "@/lib/theme-context"
import { DayNightSwitch } from "@/components/shsfui/switch/day-night-switch"

export function AnimatedThemeToggle() {
    const { theme, setTheme } = useTheme()

    const handleThemeToggle = (isDay: boolean) => {
        setTheme(isDay ? "light" : "dark")
    }

    return (
        <DayNightSwitch
            defaultChecked={theme === "light"}
            onToggle={handleThemeToggle}
        />
    )
}