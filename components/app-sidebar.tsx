"use client"

import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
  IconPhone,
  IconPlug,
  IconFrame,
  IconMap,
  IconActivity,
  IconHistory,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: IconDashboard,
    },
    {
      title: "Monitoring",
      url: "/monitoring",
      icon: IconActivity,
    },
    {
      title: "Gesprächshistorie",
      url: "/history",
      icon: IconHistory,
    },
    {
      title: "Agents",
      url: "/agents",
      icon: IconUsers,
      isActive: false,
      items: [
        {
          title: "All Agents",
          url: "/agents",
        },
        {
          title: "Create New Agent",
          url: "/agents/new",
        },
      ],
    },
    {
      title: "RAG Knowledge Base",
      url: "/rag",
      icon: IconDatabase,
      items: [
        {
          title: "Overview",
          url: "/rag/overview",
        },
        {
          title: "Import",
          url: "/rag/import",
        },
        {
          title: "Export",
          url: "/rag/export",
        },
      ],
    },
    {
      title: "Integrations",
      url: "/integrations",
      icon: IconPlug,
      items: [
        {
          title: "Overview",
          url: "/integrations/overview",
        },
        {
          title: "Custom Integrations",
          url: "/integrations/custom",
        },
        {
          title: "API",
          url: "/integrations/api",
        },
        {
          title: "Third-party",
          url: "/integrations/third-party",
        },
      ],
    },
    {
      title: "Phone numbers",
      url: "/phone",
      icon: IconPhone,
      items: [
        {
          title: "Overview",
          url: "/phone/overview",
        },
        {
          title: "Buy Phone numbers",
          url: "/phone/buy",
        },
        {
          title: "Import your Phone number",
          url: "/phone/import",
        },
      ],
    },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "Medical",
      url: "#",
      icon: IconFrame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: IconChartBar,
    },
    {
      name: "Customer Support",
      url: "#",
      icon: IconMap,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <img src="/logo2.png" alt="Logo" className="!size-8" />
                <span className="text-2xl font-semibold">JASZ Lounge</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
