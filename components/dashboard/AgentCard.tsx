'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { AgentDashboardData } from '@/types/dashboard'
import {
  formatDuration,
  formatPercentage,
  formatNumber,
  formatLastActivity,
  getStatusColor,
  getStatusLabel,
  getTrendArrow,
  getTrendColor,
  calculateTrend
} from '@/lib/dashboard-utils'
import { Phone, Clock, TrendingUp, Activity } from 'lucide-react'

interface AgentCardProps {
  agent: AgentDashboardData
  onClick?: (agent: AgentDashboardData) => void
  className?: string
}

export function AgentCard({ agent, onClick, className = '' }: AgentCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(agent)
    }
  }

  const callsTrend = calculateTrend(
    agent.todayMetrics.totalCalls,
    agent.todayMetrics.totalCalls * (1 - agent.yesterdayComparison.callsChange / 100)
  )

  const successRateTrend = calculateTrend(
    agent.todayMetrics.successRate,
    agent.todayMetrics.successRate * (1 - agent.yesterdayComparison.successRateChange / 100)
  )

  const durationTrend = calculateTrend(
    agent.todayMetrics.averageDuration,
    agent.todayMetrics.averageDuration * (1 - agent.yesterdayComparison.durationChange / 100)
  )

  return (
    <Card 
      className={`hover:shadow-lg transition-all duration-200 cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={agent.avatar} alt={agent.name} />
              <AvatarFallback className="bg-blue-100 text-blue-700">
                {agent.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-sm text-gray-900 truncate max-w-32">
                {agent.name}
              </h3>
              <p className="text-xs text-gray-500">
                ID: {agent.id}
              </p>
            </div>
          </div>
          <Badge 
            variant="outline" 
            className={`text-xs ${getStatusColor(agent.status)}`}
          >
            {getStatusLabel(agent.status)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Aktuelle Aktivität */}
        <div className="flex items-center justify-between mb-4 p-2 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <Phone className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium">
              {agent.currentCalls} aktive Gespräche
            </span>
          </div>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <Activity className="h-3 w-3" />
            <span>{formatLastActivity(agent.lastActivity)}</span>
          </div>
        </div>

        {/* Metriken Grid */}
        <div className="grid grid-cols-2 gap-3">
          {/* Anrufe heute */}
          <div className="text-center p-2 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center space-x-1">
              <span className="text-lg font-bold text-blue-700">
                {formatNumber(agent.todayMetrics.totalCalls)}
              </span>
              <span 
                className={`text-xs ${getTrendColor(callsTrend.direction, true)}`}
                title={`${callsTrend.direction === 'up' ? '+' : callsTrend.direction === 'down' ? '-' : ''}${callsTrend.percentage.toFixed(1)}% vs. gestern`}
              >
                {getTrendArrow(callsTrend.direction)}
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">Anrufe heute</p>
          </div>

          {/* Erfolgsrate */}
          <div className="text-center p-2 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center space-x-1">
              <span className="text-lg font-bold text-green-700">
                {formatPercentage(agent.todayMetrics.successRate, 0)}
              </span>
              <span 
                className={`text-xs ${getTrendColor(successRateTrend.direction, true)}`}
                title={`${successRateTrend.direction === 'up' ? '+' : successRateTrend.direction === 'down' ? '-' : ''}${successRateTrend.percentage.toFixed(1)}% vs. gestern`}
              >
                {getTrendArrow(successRateTrend.direction)}
              </span>
            </div>
            <p className="text-xs text-green-600 mt-1">Erfolgsrate</p>
          </div>

          {/* Durchschnittsdauer */}
          <div className="text-center p-2 bg-purple-50 rounded-lg">
            <div className="flex items-center justify-center space-x-1">
              <span className="text-lg font-bold text-purple-700">
                {formatDuration(agent.todayMetrics.averageDuration)}
              </span>
              <span 
                className={`text-xs ${getTrendColor(durationTrend.direction, false)}`}
                title={`${durationTrend.direction === 'up' ? '+' : durationTrend.direction === 'down' ? '-' : ''}${durationTrend.percentage.toFixed(1)}% vs. gestern`}
              >
                {getTrendArrow(durationTrend.direction)}
              </span>
            </div>
            <p className="text-xs text-purple-600 mt-1">Ø Dauer</p>
          </div>

          {/* Auslastung */}
          <div className="text-center p-2 bg-orange-50 rounded-lg">
            <div className="flex items-center justify-center space-x-1">
              <span className="text-lg font-bold text-orange-700">
                {formatPercentage(agent.todayMetrics.utilization, 0)}
              </span>
              <TrendingUp className="h-3 w-3 text-orange-600" />
            </div>
            <p className="text-xs text-orange-600 mt-1">Auslastung</p>
          </div>
        </div>

        {/* Erfolgreiche Anrufe */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Erfolgreich:</span>
            <span className="font-medium text-gray-900">
              {formatNumber(agent.todayMetrics.successfulCalls)} von {formatNumber(agent.todayMetrics.totalCalls)}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${agent.todayMetrics.totalCalls > 0 ? agent.todayMetrics.successRate : 0}%` 
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
