'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AgentCard } from './AgentCard'
import { MetricsOverview } from './MetricsOverview'
import { DashboardFilters } from './DashboardFilters'
import { useAgentMetrics } from '@/hooks/useAgentMetrics'
import { useWebSocketConnection } from '@/hooks/useWebSocketConnection'
import { AgentDashboardData } from '@/types/dashboard'
import { RefreshCw, AlertCircle, Wifi, WifiOff } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface AgentPerformanceDashboardProps {
  className?: string
}

export function AgentPerformanceDashboard({ className = '' }: AgentPerformanceDashboardProps) {
  const router = useRouter()
  const [selectedAgent, setSelectedAgent] = useState<AgentDashboardData | null>(null)
  
  const {
    agents,
    summary,
    loading,
    error,
    filters,
    setFilters,
    refresh,
    total
  } = useAgentMetrics({
    refreshInterval: 30000, // 30 Sekunden
    autoRefresh: true
  })

  const {
    connectionStatus,
    subscribe
  } = useWebSocketConnection({
    enabled: true,
    maxReconnectAttempts: 5
  })

  // WebSocket Event-Handler
  useState(() => {
    const unsubscribeStatusChanged = subscribe('agent:status_changed', (data) => {
      console.log('Agent status changed:', data)
      // Refresh data when agent status changes
      refresh()
    })

    const unsubscribeMetricsUpdated = subscribe('agent:metrics_updated', (data) => {
      console.log('Agent metrics updated:', data)
      // Refresh data when metrics are updated
      refresh()
    })

    return () => {
      unsubscribeStatusChanged()
      unsubscribeMetricsUpdated()
    }
  })

  const handleAgentClick = useCallback((agent: AgentDashboardData) => {
    setSelectedAgent(agent)
    // Navigate to agent detail page
    router.push(`/agents/${agent.id}`)
  }, [router])

  const handleRefresh = useCallback(async () => {
    await refresh()
  }, [refresh])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Agent Performance Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Überwachen Sie die Leistung und Aktivität Ihrer AI-Agenten in Echtzeit
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* WebSocket Connection Status */}
          <div className="flex items-center space-x-2">
            {connectionStatus.connected ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600">Live</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600">Offline</span>
              </>
            )}
          </div>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Aktualisieren</span>
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Metrics Overview */}
      <MetricsOverview 
        summary={summary} 
        loading={loading}
        className="mb-6"
      />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filter und Suche</CardTitle>
        </CardHeader>
        <CardContent>
          <DashboardFilters
            filters={filters}
            onFiltersChange={setFilters}
            totalResults={total}
          />
        </CardContent>
      </Card>

      {/* Agent Cards Grid */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Agenten ({total})
          </h2>
          
          {!connectionStatus.connected && (
            <div className="text-sm text-amber-600 bg-amber-50 px-3 py-1 rounded-full border border-amber-200">
              Daten werden möglicherweise nicht in Echtzeit aktualisiert
            </div>
          )}
        </div>

        {loading && agents.length === 0 ? (
          // Loading skeleton
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-24"></div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-12 bg-gray-200 rounded"></div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="h-16 bg-gray-200 rounded"></div>
                      <div className="h-16 bg-gray-200 rounded"></div>
                      <div className="h-16 bg-gray-200 rounded"></div>
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : agents.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {agents.map((agent) => (
              <AgentCard
                key={agent.id}
                agent={agent}
                onClick={handleAgentClick}
                className="h-full"
              />
            ))}
          </div>
        ) : (
          // Empty state
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-400 mb-4">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Keine Agenten gefunden
              </h3>
              <p className="text-gray-500 mb-4">
                {filters.search || filters.status !== 'all'
                  ? 'Keine Agenten entsprechen den aktuellen Filterkriterien.'
                  : 'Sie haben noch keine Agenten erstellt.'}
              </p>
              {(!filters.search && filters.status === 'all') && (
                <Button
                  onClick={() => router.push('/agents/new')}
                  className="mt-2"
                >
                  Ersten Agent erstellen
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
