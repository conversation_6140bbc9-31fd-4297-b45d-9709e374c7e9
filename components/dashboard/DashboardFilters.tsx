'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DashboardFilters } from '@/types/dashboard'
import { Search, Filter, X, ArrowUpDown } from 'lucide-react'

interface DashboardFiltersProps {
  filters: DashboardFilters
  onFiltersChange: (filters: Partial<DashboardFilters>) => void
  totalResults: number
  className?: string
}

const STATUS_OPTIONS = [
  { value: 'all', label: 'Alle Status', color: 'bg-gray-100 text-gray-800' },
  { value: 'active', label: 'Aktiv', color: 'bg-green-100 text-green-800' },
  { value: 'idle', label: 'Bereit', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'error', label: '<PERSON><PERSON>', color: 'bg-red-100 text-red-800' },
  { value: 'inactive', label: 'Inaktiv', color: 'bg-gray-100 text-gray-800' }
]

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'status', label: 'Status' },
  { value: 'activity', label: 'Letzte Aktivität' },
  { value: 'performance', label: 'Performance' }
]

export function DashboardFilters({ 
  filters, 
  onFiltersChange, 
  totalResults,
  className = '' 
}: DashboardFiltersProps) {
  const [searchInput, setSearchInput] = useState(filters.search)

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onFiltersChange({ search: searchInput.trim() })
  }

  const handleSearchClear = () => {
    setSearchInput('')
    onFiltersChange({ search: '' })
  }

  const handleStatusChange = (status: string) => {
    onFiltersChange({ status: status as DashboardFilters['status'] })
  }

  const handleSortChange = (sortBy: string) => {
    onFiltersChange({ sortBy: sortBy as DashboardFilters['sortBy'] })
  }

  const handleSortOrderToggle = () => {
    onFiltersChange({ 
      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
    })
  }

  const hasActiveFilters = filters.search || filters.status !== 'all'
  const selectedStatusOption = STATUS_OPTIONS.find(opt => opt.value === filters.status)
  const selectedSortOption = SORT_OPTIONS.find(opt => opt.value === filters.sortBy)

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Suchleiste */}
      <form onSubmit={handleSearchSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Agenten suchen (Name oder ID)..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchInput && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleSearchClear}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </form>

      {/* Filter- und Sortieroptionen */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Status-Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <Select value={filters.status} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {STATUS_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${option.color.split(' ')[0]}`} />
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Sortierung */}
        <div className="flex items-center space-x-2">
          <ArrowUpDown className="h-4 w-4 text-gray-500" />
          <Select value={filters.sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleSortOrderToggle}
            className="px-3"
            title={`Sortierung: ${filters.sortOrder === 'asc' ? 'Aufsteigend' : 'Absteigend'}`}
          >
            {filters.sortOrder === 'asc' ? '↑' : '↓'}
          </Button>
        </div>

        {/* Ergebnisse */}
        <div className="ml-auto text-sm text-gray-600">
          {totalResults} Agent{totalResults !== 1 ? 'en' : ''} gefunden
        </div>
      </div>

      {/* Aktive Filter anzeigen */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-gray-600">Aktive Filter:</span>
          
          {filters.search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Suche: "{filters.search}"
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFiltersChange({ search: '' })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.status !== 'all' && selectedStatusOption && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {selectedStatusOption.label}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFiltersChange({ status: 'all' })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSearchInput('')
              onFiltersChange({ search: '', status: 'all' })
            }}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Alle Filter zurücksetzen
          </Button>
        </div>
      )}

      {/* Sortierung anzeigen */}
      {selectedSortOption && (
        <div className="text-xs text-gray-500">
          Sortiert nach: {selectedSortOption.label} ({filters.sortOrder === 'asc' ? 'aufsteigend' : 'absteigend'})
        </div>
      )}
    </div>
  )
}
