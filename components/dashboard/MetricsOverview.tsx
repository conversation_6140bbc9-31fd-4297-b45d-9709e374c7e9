'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DashboardSummary } from '@/types/dashboard'
import { formatNumber, formatPercentage, formatLastActivity } from '@/lib/dashboard-utils'
import { Users, Phone, TrendingUp, Activity, Clock } from 'lucide-react'

interface MetricsOverviewProps {
  summary: DashboardSummary | null
  loading?: boolean
  className?: string
}

export function MetricsOverview({ summary, loading = false, className = '' }: MetricsOverviewProps) {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!summary) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>Keine Metriken verfügbar</p>
      </div>
    )
  }

  const metrics = [
    {
      title: 'Agenten gesamt',
      value: formatNumber(summary.totalAgents),
      subtitle: `${formatNumber(summary.activeAgents)} aktiv`,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Anrufe heute',
      value: formatNumber(summary.totalCallsToday),
      subtitle: 'Alle Agenten',
      icon: Phone,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'Erfolgsrate',
      value: formatPercentage(summary.averageSuccessRate, 1),
      subtitle: 'Durchschnitt',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Auslastung',
      value: formatPercentage(summary.totalUtilization, 1),
      subtitle: 'Durchschnitt',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ]

  return (
    <div className={className}>
      {/* Hauptmetriken */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric, index) => {
          const Icon = metric.icon
          return (
            <Card key={index} className={`border ${metric.borderColor} hover:shadow-md transition-shadow`}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                  <Icon className={`h-4 w-4 mr-2 ${metric.color}`} />
                  {metric.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${metric.color} mb-1`}>
                  {metric.value}
                </div>
                <p className="text-xs text-gray-500">
                  {metric.subtitle}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Zusätzliche Informationen */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              <span>Letzte Aktualisierung:</span>
              <span className="font-medium">
                {formatLastActivity(summary.lastUpdated)}
              </span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm">
              {/* Aktive Agenten Indikator */}
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-gray-600">
                  {summary.activeAgents} von {summary.totalAgents} Agenten aktiv
                </span>
              </div>
            </div>
          </div>

          {/* Progress Bar für aktive Agenten */}
          <div className="mt-4">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Agent-Aktivität</span>
              <span>
                {formatPercentage((summary.activeAgents / summary.totalAgents) * 100, 0)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-500"
                style={{ 
                  width: `${summary.totalAgents > 0 ? (summary.activeAgents / summary.totalAgents) * 100 : 0}%` 
                }}
              />
            </div>
          </div>

          {/* Schnelle Statistiken */}
          <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {summary.totalCallsToday > 0 
                  ? formatNumber(Math.round(summary.totalCallsToday / Math.max(summary.activeAgents, 1)))
                  : '0'
                }
              </div>
              <div className="text-xs text-gray-500">Anrufe/Agent</div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {summary.totalAgents - summary.activeAgents}
              </div>
              <div className="text-xs text-gray-500">Inaktive Agenten</div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatPercentage(
                  summary.totalAgents > 0 
                    ? ((summary.totalAgents - summary.activeAgents) / summary.totalAgents) * 100 
                    : 0, 
                  0
                )}
              </div>
              <div className="text-xs text-gray-500">Inaktivitätsrate</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
