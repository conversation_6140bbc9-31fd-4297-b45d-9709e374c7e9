"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import { useIsFail } from "@/hooks/use-fail"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group"

export const description = "An interactive area chart"

const chartData = [
  { date: "2024-04-01", success: 222, fail: 150 },
  { date: "2024-04-02", success: 97, fail: 180 },
  { date: "2024-04-03", success: 167, fail: 120 },
  { date: "2024-04-04", success: 242, fail: 260 },
  { date: "2024-04-05", success: 373, fail: 290 },
  { date: "2024-04-06", success: 301, fail: 340 },
  { date: "2024-04-07", success: 245, fail: 180 },
  { date: "2024-04-08", success: 409, fail: 320 },
  { date: "2024-04-09", success: 59, fail: 110 },
  { date: "2024-04-10", success: 261, fail: 190 },
  { date: "2024-04-11", success: 327, fail: 350 },
  { date: "2024-04-12", success: 292, fail: 210 },
  { date: "2024-04-13", success: 342, fail: 380 },
  { date: "2024-04-14", success: 137, fail: 220 },
  { date: "2024-04-15", success: 120, fail: 170 },
  { date: "2024-04-16", success: 138, fail: 190 },
  { date: "2024-04-17", success: 446, fail: 360 },
  { date: "2024-04-18", success: 364, fail: 410 },
  { date: "2024-04-19", success: 243, fail: 180 },
  { date: "2024-04-20", success: 89, fail: 150 },
  { date: "2024-04-21", success: 137, fail: 200 },
  { date: "2024-04-22", success: 224, fail: 170 },
  { date: "2024-04-23", success: 138, fail: 230 },
  { date: "2024-04-24", success: 387, fail: 290 },
  { date: "2024-04-25", success: 215, fail: 250 },
  { date: "2024-04-26", success: 75, fail: 130 },
  { date: "2024-04-27", success: 383, fail: 420 },
  { date: "2024-04-28", success: 122, fail: 180 },
  { date: "2024-04-29", success: 315, fail: 240 },
  { date: "2024-04-30", success: 454, fail: 380 },
  { date: "2024-05-01", success: 165, fail: 220 },
  { date: "2024-05-02", success: 293, fail: 310 },
  { date: "2024-05-03", success: 247, fail: 190 },
  { date: "2024-05-04", success: 385, fail: 420 },
  { date: "2024-05-05", success: 481, fail: 390 },
  { date: "2024-05-06", success: 498, fail: 520 },
  { date: "2024-05-07", success: 388, fail: 300 },
  { date: "2024-05-08", success: 149, fail: 210 },
  { date: "2024-05-09", success: 227, fail: 180 },
  { date: "2024-05-10", success: 293, fail: 330 },
  { date: "2024-05-11", success: 335, fail: 270 },
  { date: "2024-05-12", success: 197, fail: 240 },
  { date: "2024-05-13", success: 197, fail: 160 },
  { date: "2024-05-14", success: 448, fail: 490 },
  { date: "2024-05-15", success: 473, fail: 380 },
  { date: "2024-05-16", success: 338, fail: 400 },
  { date: "2024-05-17", success: 499, fail: 420 },
  { date: "2024-05-18", success: 315, fail: 350 },
  { date: "2024-05-19", success: 235, fail: 180 },
  { date: "2024-05-20", success: 177, fail: 230 },
  { date: "2024-05-21", success: 82, fail: 140 },
  { date: "2024-05-22", success: 81, fail: 120 },
  { date: "2024-05-23", success: 252, fail: 290 },
  { date: "2024-05-24", success: 294, fail: 220 },
  { date: "2024-05-25", success: 201, fail: 250 },
  { date: "2024-05-26", success: 213, fail: 170 },
  { date: "2024-05-27", success: 420, fail: 460 },
  { date: "2024-05-28", success: 233, fail: 190 },
  { date: "2024-05-29", success: 78, fail: 130 },
  { date: "2024-05-30", success: 340, fail: 280 },
  { date: "2024-05-31", success: 178, fail: 230 },
  { date: "2024-06-01", success: 178, fail: 200 },
  { date: "2024-06-02", success: 470, fail: 410 },
  { date: "2024-06-03", success: 103, fail: 160 },
  { date: "2024-06-04", success: 439, fail: 380 },
  { date: "2024-06-05", success: 88, fail: 140 },
  { date: "2024-06-06", success: 294, fail: 250 },
  { date: "2024-06-07", success: 323, fail: 370 },
  { date: "2024-06-08", success: 385, fail: 320 },
  { date: "2024-06-09", success: 438, fail: 480 },
  { date: "2024-06-10", success: 155, fail: 200 },
  { date: "2024-06-11", success: 92, fail: 150 },
  { date: "2024-06-12", success: 492, fail: 420 },
  { date: "2024-06-13", success: 81, fail: 130 },
  { date: "2024-06-14", success: 426, fail: 380 },
  { date: "2024-06-15", success: 307, fail: 350 },
  { date: "2024-06-16", success: 371, fail: 310 },
  { date: "2024-06-17", success: 475, fail: 520 },
  { date: "2024-06-18", success: 107, fail: 170 },
  { date: "2024-06-19", success: 341, fail: 290 },
  { date: "2024-06-20", success: 408, fail: 450 },
  { date: "2024-06-21", success: 169, fail: 210 },
  { date: "2024-06-22", success: 317, fail: 270 },
  { date: "2024-06-23", success: 480, fail: 530 },
  { date: "2024-06-24", success: 132, fail: 180 },
  { date: "2024-06-25", success: 141, fail: 190 },
  { date: "2024-06-26", success: 434, fail: 380 },
  { date: "2024-06-27", success: 448, fail: 490 },
  { date: "2024-06-28", success: 149, fail: 200 },
  { date: "2024-06-29", success: 103, fail: 160 },
  { date: "2024-06-30", success: 446, fail: 400 },
]

const chartConfig = {
  callrate: {
    label: "Call Success Rate",
  },
  success: {
    label: "Call Success",
    color: "var(--chart-1)",
  },
  fail: {
    label: "Call Failure",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig

export function ChartAreaInteractive() {
  const isfail = useIsFail()
  const [timeRange, setTimeRange] = React.useState("90d")

  React.useEffect(() => {
    if (isfail) {
      setTimeRange("7d")
    }
  }, [isfail])

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date)
    const referenceDate = new Date("2024-06-30")
    let daysToSubtract = 90
    if (timeRange === "30d") {
      daysToSubtract = 30
    } else if (timeRange === "7d") {
      daysToSubtract = 7
    }
    const startDate = new Date(referenceDate)
    startDate.setDate(startDate.getDate() - daysToSubtract)
    return date >= startDate
  })

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Call Success Rate</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">
            Total for the last 3 months
          </span>
          <span className="@[540px]/card:hidden">Last 3 months</span>
        </CardDescription>
        <CardAction>
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
          >
            <ToggleGroupItem value="90d">Last 3 months</ToggleGroupItem>
            <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
            <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
          </ToggleGroup>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
              size="sm"
              aria-label="Select a value"
            >
              <SelectValue placeholder="Last 3 months" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="90d" className="rounded-lg">
                Last 3 months
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillsuccess" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-success)"
                  stopOpacity={1.0}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-success)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillfail" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-fail)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-fail)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isfail ? -1 : 10}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="fail"
              type="natural"
              fill="url(#fillfail)"
              stroke="var(--color-mobile)"
              stackId="a"
            />
            <Area
              dataKey="success"
              type="natural"
              fill="url(#fillsuccess)"
              stroke="var(--color-success)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
