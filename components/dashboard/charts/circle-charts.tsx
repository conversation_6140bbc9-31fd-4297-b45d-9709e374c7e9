"use client"

import * as React from "react"
import { TrendingUp } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ra<PERSON><PERSON>ar<PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

// Basis-Daten für die Radial-Chart mit den Standard-Chart-Farben
const baseChartData = [
  { analysis: "Undefined", visitors: 275, fill: "var(--chart-1)" },
  { analysis: "Human Pick Up Cut Off", visitors: 200, fill: "var(--chart-2)" },
  { analysis: "Human Goodbye", visitors: 187, fill: "var(--chart-3)" },
  { analysis: "Agent Goodbye", visitors: 173, fill: "var(--chart-4)" },
]

// Chart-Konfiguration mit Standard-Chart-Farben
const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  undefined: {
    label: "Undefined",
    color: "var(--chart-1)",
  },
  humanPickUpCutOff: {
    label: "Human Pick Up Cut Off",
    color: "var(--chart-2)",
  },
  humanGoodbye: {
    label: "Human Goodbye",
    color: "var(--chart-3)",
  },
  agentGoodbye: {
    label: "Agent Goodbye",
    color: "var(--chart-4)",
  },
} satisfies ChartConfig

// Zeitraum-Typ für die Komponente
type TimeRange = "today" | "lastWeek" | "lastMonth";

// Props-Interface für die Komponente
interface RadialChartProps {
  timeRange?: TimeRange;
  agentId?: string;
}

export function CircleCharts({ timeRange = "today", agentId = "all" }: RadialChartProps) {
  // Titel basierend auf dem ausgewählten Zeitraum anpassen
  const timeRangeTitle = React.useMemo(() => {
    switch (timeRange) {
      case "today":
        return "Today";
      case "lastWeek":
        return "Last Week";
      case "lastMonth":
        return "Last Month";
      default:
        return "Today";
    }
  }, [timeRange])
  
  // Daten basierend auf dem ausgewählten Agent anpassen
  const chartData = React.useMemo(() => {
    // Verschiedene Werte für verschiedene Agents
    const multiplier = 
      agentId === "all" ? 1 :
      agentId === "customer-support" ? 0.85 :
      agentId === "sales" ? 1.25 :
      agentId === "technical" ? 0.7 : 1;
    
    return baseChartData.map(item => ({
      ...item,
      visitors: Math.round(item.visitors * multiplier)
    }));
  }, [agentId])
  
  // Trend-Text basierend auf dem ausgewählten Agent
  const trendText = React.useMemo(() => {
    switch (agentId) {
      case "all":
        return "****%";
      case "customer-support":
        return "+3.8%";
      case "sales":
        return "+7.3%";
      case "technical":
        return "-1.5%";
      default:
        return "****%";
    }
  }, [agentId])

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>
          {agentId === "all" ? "Analysis - All Agents" : 
           agentId === "customer-support" ? "Analysis - Customer Support" :
           agentId === "sales" ? "Analysis - Sales" :
           agentId === "technical" ? "Analysis - Technical Support" : "Analysis"}
        </CardTitle>
        <CardDescription>{timeRangeTitle}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <RadialBarChart
            data={chartData}
            startAngle={-90}
            endAngle={380}
            innerRadius={30}
            outerRadius={110}
          >
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel nameKey="analysis" />}
            />
            <RadialBar dataKey="visitors" background>
              <LabelList
                position="insideStart"
                dataKey="analysis"
                className="fill-white capitalize mix-blend-luminosity"
                fontSize={11}
              />
            </RadialBar>
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Trend compare to last month: {trendText} <TrendingUp className={`h-4 w-4 ${trendText.startsWith('-') ? 'text-red-500 rotate-180' : 'text-green-500'}`} />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing analysis for selected agent and time range
        </div>
      </CardFooter>
    </Card>
  )
}