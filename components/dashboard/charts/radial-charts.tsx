"use client"

import * as React from "react"
import { TrendingUp } from "lucide-react"
import {
  Label,
  PolarGrid,
  PolarRadiusAxis,
  RadialBar,
  RadialBarChart,
} from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ChartConfig, ChartContainer } from "@/components/ui/chart"

// Daten mit Standard-Chart-Farben
const baseChartData = [
  { browser: "safari", visitors: 200, fill: "var(--chart-1)" },
]

// Chart-Konfiguration mit Standard-Chart-Farben
const chartConfig = {
  visitors: {
    label: "Besucher",
    color: "var(--chart-1)",
  },
  safari: {
    label: "Browser",
    color: "var(--chart-1)",
  },
} satisfies ChartConfig

// Zeitraum-Typ für die Komponente
type TimeRange = "today" | "lastWeek" | "lastMonth";

// Props-Interface für die Komponente
interface RadialChartProps {
  timeRange?: TimeRange;
  agentId?: string;
}

export function RadialCharts({ timeRange = "today", agentId = "all" }: RadialChartProps) {
  // Titel basierend auf dem ausgewählten Zeitraum anpassen
  const timeRangeTitle = React.useMemo(() => {
    switch (timeRange) {
      case "today":
        return "Today";
      case "lastWeek":
        return "Last Week";
      case "lastMonth":
        return "Last Month";
      default:
        return "Today";
    }
  }, [timeRange])
  
  // Daten basierend auf dem ausgewählten Agent anpassen
  const chartData = React.useMemo(() => {
    // Verschiedene Werte für verschiedene Agents
    const multiplier = 
      agentId === "all" ? 1 :
      agentId === "customer-support" ? 0.85 :
      agentId === "sales" ? 1.25 :
      agentId === "technical" ? 0.7 : 1;
    
    return baseChartData.map(item => ({
      ...item,
      visitors: Math.round(item.visitors * multiplier)
    }));
  }, [agentId])
  
  // Trend-Text basierend auf dem ausgewählten Agent
  const trendText = React.useMemo(() => {
    switch (agentId) {
      case "all":
        return "****%";
      case "customer-support":
        return "+3.8%";
      case "sales":
        return "+7.3%";
      case "technical":
        return "-1.5%";
      default:
        return "****%";
    }
  }, [agentId])
  
  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>
          {agentId === "all" ? "Minutes Used - All Agents" : 
           agentId === "customer-support" ? "Minutes Used - Customer Support" :
           agentId === "sales" ? "Minutes Used - Sales" :
           agentId === "technical" ? "Minutes Used - Technical Support" : "Minutes Used"}
        </CardTitle>
        <CardDescription>{timeRangeTitle}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <RadialBarChart
            data={chartData}
            startAngle={0}
            endAngle={250}
            innerRadius={80}
            outerRadius={110}
          >
            <PolarGrid
              gridType="circle"
              radialLines={false}
              stroke="none"
              className="first:fill-muted last:fill-background"
              polarRadius={[86, 74]}
            />
            <RadialBar dataKey="visitors" background cornerRadius={10} />
            <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-4xl font-bold"
                        >
                          {chartData[0].visitors.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground"
                        >
                          Minutes
                        </tspan>
                      </text>
                    )
                  }
                }}
              />
            </PolarRadiusAxis>
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Trend compare to last month: {trendText} <TrendingUp className={`h-4 w-4 ${trendText.startsWith('-') ? 'text-red-500 rotate-180' : 'text-green-500'}`} />
        </div>
        <div className="leading-none text-muted-foreground">
          Show total minutes used for selected agent and time range
        </div>
      </CardFooter>
    </Card>
  )
}