import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { AnimatedThemeToggle } from "@/components/animated-theme-toggle"
import { Plus } from "lucide-react"
import Link from "next/link"

interface SiteHeaderProps {
  title?: string
  showCreateButton?: boolean
  createButtonText?: string
  createButtonHref?: string
  createButtonPosition?: "left" | "right" // Neue Prop für die Position des Buttons
}

export function SiteHeader({
  title = "Dashboard",
  showCreateButton = false,
  createButtonText = "Create",
  createButtonHref = "#",
  createButtonPosition = "right" // Standardmäßig rechts
}: SiteHeaderProps) {
  const renderCreateButton = showCreateButton ? (
    <Button asChild size="sm">
      <Link href={createButtonHref}>
        <Plus className="mr-2 h-4 w-4" />
        {createButtonText}
      </Link>
    </Button>
  ) : null;

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        {createButtonPosition === "left" && renderCreateButton}
        <h1 className="text-base font-medium">{title}</h1>
        <div className="ml-auto flex items-center gap-2">
          {createButtonPosition === "right" && renderCreateButton}
          <AnimatedThemeToggle />
          <Button variant="ghost" asChild size="sm" className="hidden sm:flex">
            <a
              href="www.jasz-ai.com"
              rel="noopener noreferrer"
              target="_blank"
              className="dark:text-foreground"
            >
              Homepage
            </a>
          </Button>
        </div>
      </div>
    </header>
  )
}
