'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  MapPin,
  Star,
  Download,
  Play,
  Pause,
  Volume2,
  AlertTriangle,
  MessageSquare,
  Settings,
  FileText,
  Tag
} from 'lucide-react'

import { ConversationDetails as ConversationDetailsType } from '@/types/history'
import { 
  formatDateTime, 
  formatDuration, 
  getStatusLabel, 
  getStatusColor,
  getPriorityColor,
  getSentimentEmoji,
  getSentimentColor,
  getSystemEventIcon,
  getSystemEventLabel,
  getInitials,
  generateColorFromString,
  formatPhoneNumber,
  formatFileSize,
  getResolutionStatusLabel,
  getResolutionStatusColor
} from '@/lib/history-utils'
import { TranscriptViewer } from './TranscriptViewer'

interface ConversationDetailsProps {
  conversation: ConversationDetailsType
  loading: boolean
  error: string | null
  onBack: () => void
}

export function ConversationDetails({
  conversation,
  loading,
  error,
  onBack
}: ConversationDetailsProps) {
  const [activeTab, setActiveTab] = useState('transcript')
  const [audioPlaying, setAudioPlaying] = useState(false)

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </CardContent>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const handleAudioToggle = () => {
    setAudioPlaying(!audioPlaying)
    // TODO: Implement actual audio playback
  }

  const handleAudioDownload = () => {
    if (conversation.audioRecording) {
      // TODO: Implement audio download
      window.open(conversation.audioRecording.url, '_blank')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to List
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Conversation Details</h1>
            <p className="text-gray-600">ID: {conversation.id}</p>
          </div>
        </div>
        
        {conversation.audioRecording && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAudioToggle}
              className="flex items-center gap-2"
            >
              {audioPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {audioPlaying ? 'Pause' : 'Play'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleAudioDownload}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
          </div>
        )}
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Status</p>
                <Badge className={getStatusColor(conversation.status)}>
                  {getStatusLabel(conversation.status)}
                </Badge>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <MessageSquare className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Duration</p>
                <p className="text-lg font-bold text-gray-900">
                  {formatDuration(conversation.duration)}
                </p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rating</p>
                <div className="flex items-center gap-1">
                  {conversation.rating ? (
                    <>
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-lg font-bold text-gray-900">{conversation.rating}</span>
                    </>
                  ) : (
                    <span className="text-gray-400">Not rated</span>
                  )}
                </div>
              </div>
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Priority</p>
                <Badge className={getPriorityColor(conversation.metadata.priority!)}>
                  {conversation.metadata.priority}
                </Badge>
              </div>
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Metadata */}
        <div className="space-y-6">
          {/* Agent Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                Agent
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarFallback 
                    className="text-sm font-medium"
                    style={{ backgroundColor: generateColorFromString(conversation.agentName) }}
                  >
                    {getInitials(conversation.agentName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{conversation.agentName}</div>
                  <div className="text-sm text-gray-500">ID: {conversation.agentId}</div>
                  <div className="text-sm text-gray-500">{conversation.metadata.department}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Caller Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Caller
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {conversation.callerInfo ? (
                <>
                  {conversation.callerInfo.name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{conversation.callerInfo.name}</span>
                    </div>
                  )}
                  {conversation.callerInfo.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{formatPhoneNumber(conversation.callerInfo.phone)}</span>
                    </div>
                  )}
                  {conversation.callerInfo.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{conversation.callerInfo.email}</span>
                    </div>
                  )}
                  {conversation.callerInfo.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>{conversation.callerInfo.location}</span>
                    </div>
                  )}
                </>
              ) : (
                <p className="text-gray-500">No caller information available</p>
              )}
            </CardContent>
          </Card>

          {/* Audio Recording */}
          {conversation.audioRecording && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Volume2 className="h-5 w-5" />
                  Recording
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600">
                  <div>Format: {conversation.audioRecording.format.toUpperCase()}</div>
                  <div>Size: {formatFileSize(conversation.audioRecording.size)}</div>
                  <div>Duration: {formatDuration(conversation.audioRecording.duration)}</div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAudioToggle}
                    className="flex items-center gap-2"
                  >
                    {audioPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    {audioPlaying ? 'Pause' : 'Play'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAudioDownload}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quality Metrics */}
          {conversation.qualityMetrics && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Quality Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg. Response Time:</span>
                  <span className="text-sm font-medium">{conversation.qualityMetrics.averageResponseTime}s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Sentiment Score:</span>
                  <span className="text-sm font-medium">{(conversation.qualityMetrics.sentimentScore * 100).toFixed(0)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Resolution:</span>
                  <Badge className={getResolutionStatusColor(conversation.qualityMetrics.resolutionStatus)}>
                    {getResolutionStatusLabel(conversation.qualityMetrics.resolutionStatus)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Tabs */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="transcript">Transcript</TabsTrigger>
              <TabsTrigger value="events">Events</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="tags">Tags & Tools</TabsTrigger>
            </TabsList>

            <TabsContent value="transcript">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Conversation Transcript</CardTitle>
                </CardHeader>
                <CardContent>
                  <TranscriptViewer transcript={conversation.transcript} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">System Events</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {conversation.systemEvents.map((event) => (
                      <div key={event.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg">{getSystemEventIcon(event.type)}</div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{getSystemEventLabel(event.type)}</span>
                            <span className="text-xs text-gray-500">
                              {formatDateTime(event.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="summary">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Conversation Summary</h4>
                    <p className="text-gray-700">{conversation.summary}</p>
                  </div>
                  
                  {conversation.agentNotes && (
                    <div>
                      <h4 className="font-medium mb-2">Agent Notes</h4>
                      <p className="text-gray-700">{conversation.agentNotes}</p>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium mb-2">Conversation Details</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Start Time:</span>
                        <div className="font-medium">{formatDateTime(conversation.startTime)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">End Time:</span>
                        <div className="font-medium">{formatDateTime(conversation.endTime)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Category:</span>
                        <div className="font-medium">{conversation.metadata.category}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Department:</span>
                        <div className="font-medium">{conversation.metadata.department}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tags">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tags & Used Tools</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Tags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {conversation.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Used Tools
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {conversation.toolsUsed.map((tool) => (
                        <Badge key={tool} variant="outline">
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
