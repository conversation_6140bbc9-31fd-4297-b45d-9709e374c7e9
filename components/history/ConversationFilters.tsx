'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover'
import { 
  Search, 
  X, 
  Calendar as CalendarIcon,
  RotateCcw,
  Filter
} from 'lucide-react'
import { format } from 'date-fns'
import { de } from 'date-fns/locale'

import { 
  ConversationFilters as ConversationFiltersType, 
  ConversationHistoryItem,
  DATE_RANGE_PRESETS,
  DURATION_RANGES,
  STATUS_LABELS
} from '@/types/history'
import { 
  extractUniqueAgents, 
  extractUniqueTags,
  debounce
} from '@/lib/history-utils'

interface ConversationFiltersProps {
  filters: ConversationFiltersType
  onFiltersChange: (filters: Partial<ConversationFiltersType>) => void
  onReset: () => void
  conversations: ConversationHistoryItem[]
}

export function ConversationFilters({
  filters,
  onFiltersChange,
  onReset,
  conversations
}: ConversationFiltersProps) {
  const [searchInput, setSearchInput] = useState(filters.search)
  const [dateRangeOpen, setDateRangeOpen] = useState(false)
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: filters.dateRange?.start,
    to: filters.dateRange?.end
  })

  // Debounced search
  const debouncedSearch = debounce((value: string) => {
    onFiltersChange({ search: value })
  }, 300)

  useEffect(() => {
    debouncedSearch(searchInput)
  }, [searchInput, debouncedSearch])

  const availableAgents = extractUniqueAgents(conversations)
  const availableTags = extractUniqueTags(conversations)

  const handleDateRangePreset = (preset: keyof typeof DATE_RANGE_PRESETS) => {
    const now = new Date()
    let start: Date, end: Date

    switch (preset) {
      case 'today':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
        break
      case 'yesterday':
        const yesterday = new Date(now)
        yesterday.setDate(yesterday.getDate() - 1)
        start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
        end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
        break
      case 'last7days':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        end = now
        break
      case 'last30days':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        end = now
        break
      case 'thisMonth':
        start = new Date(now.getFullYear(), now.getMonth(), 1)
        end = now
        break
      case 'lastMonth':
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        start = lastMonth
        end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)
        break
      default:
        return
    }

    onFiltersChange({
      dateRange: { start, end }
    })
    setCustomDateRange({ from: start, to: end })
  }

  const handleCustomDateRange = () => {
    if (customDateRange.from && customDateRange.to) {
      onFiltersChange({
        dateRange: {
          start: customDateRange.from,
          end: customDateRange.to
        }
      })
    }
    setDateRangeOpen(false)
  }

  const handleDurationRange = (rangeKey: keyof typeof DURATION_RANGES) => {
    const range = DURATION_RANGES[rangeKey]
    onFiltersChange({
      durationRange: {
        min: range.min,
        max: range.max === Infinity ? Infinity : range.max
      }
    })
  }

  const handleAgentToggle = (agentId: string) => {
    const currentAgents = filters.agentIds || []
    const newAgents = currentAgents.includes(agentId)
      ? currentAgents.filter(id => id !== agentId)
      : [...currentAgents, agentId]
    
    onFiltersChange({ agentIds: newAgents })
  }

  const handleStatusToggle = (status: keyof typeof STATUS_LABELS) => {
    const currentStatus = filters.status || []
    const newStatus = currentStatus.includes(status)
      ? currentStatus.filter(s => s !== status)
      : [...currentStatus, status]
    
    onFiltersChange({ status: newStatus })
  }

  const handleTagToggle = (tag: string) => {
    const currentTags = filters.tags || []
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag]
    
    onFiltersChange({ tags: newTags })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.agentIds.length > 0) count++
    if (filters.status.length > 0) count++
    if (filters.tags.length > 0) count++
    if (filters.durationRange.min > 0 || filters.durationRange.max < Infinity) count++
    return count
  }

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="space-y-2">
        <Label htmlFor="search">Suche</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="search"
            placeholder="Nach Agent, Anrufer, Telefonnummer oder Inhalt suchen..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="pl-10"
          />
          {searchInput && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchInput('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Date Range */}
      <div className="space-y-2">
        <Label>Zeitraum</Label>
        <div className="flex flex-wrap gap-2">
          {Object.entries(DATE_RANGE_PRESETS).map(([key, label]) => (
            <Button
              key={key}
              variant="outline"
              size="sm"
              onClick={() => handleDateRangePreset(key as keyof typeof DATE_RANGE_PRESETS)}
              className="text-xs"
            >
              {label}
            </Button>
          ))}
          
          <Popover open={dateRangeOpen} onOpenChange={setDateRangeOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="text-xs">
                <CalendarIcon className="h-3 w-3 mr-1" />
                Benutzerdefiniert
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label>Von</Label>
                  <Calendar
                    mode="single"
                    selected={customDateRange.from}
                    onSelect={(date) => setCustomDateRange(prev => ({ ...prev, from: date }))}
                    locale={de}
                    className="rounded-md border"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Bis</Label>
                  <Calendar
                    mode="single"
                    selected={customDateRange.to}
                    onSelect={(date) => setCustomDateRange(prev => ({ ...prev, to: date }))}
                    locale={de}
                    className="rounded-md border"
                  />
                </div>
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleCustomDateRange}>
                    Anwenden
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setDateRangeOpen(false)}>
                    Abbrechen
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        
        {filters.dateRange && (
          <div className="text-xs text-gray-600">
            Aktiv: {format(filters.dateRange.start, 'dd.MM.yyyy', { locale: de })} - {format(filters.dateRange.end, 'dd.MM.yyyy', { locale: de })}
          </div>
        )}
      </div>

      {/* Duration Range */}
      <div className="space-y-2">
        <Label>Gesprächsdauer</Label>
        <div className="flex flex-wrap gap-2">
          {Object.entries(DURATION_RANGES).map(([key, range]) => (
            <Button
              key={key}
              variant="outline"
              size="sm"
              onClick={() => handleDurationRange(key as keyof typeof DURATION_RANGES)}
              className="text-xs"
            >
              {range.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Agents */}
      {availableAgents.length > 0 && (
        <div className="space-y-2">
          <Label>Agenten</Label>
          <div className="flex flex-wrap gap-2">
            {availableAgents.map((agent) => (
              <Badge
                key={agent.id}
                variant={filters.agentIds.includes(agent.id) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleAgentToggle(agent.id)}
              >
                {agent.name}
                {filters.agentIds.includes(agent.id) && (
                  <X className="h-3 w-3 ml-1" />
                )}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Status */}
      <div className="space-y-2">
        <Label>Status</Label>
        <div className="flex flex-wrap gap-2">
          {Object.entries(STATUS_LABELS).map(([key, label]) => (
            <Badge
              key={key}
              variant={filters.status.includes(key as any) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => handleStatusToggle(key as keyof typeof STATUS_LABELS)}
            >
              {label}
              {filters.status.includes(key as any) && (
                <X className="h-3 w-3 ml-1" />
              )}
            </Badge>
          ))}
        </div>
      </div>

      {/* Tags */}
      {availableTags.length > 0 && (
        <div className="space-y-2">
          <Label>Tags</Label>
          <div className="flex flex-wrap gap-2">
            {availableTags.slice(0, 10).map((tag) => (
              <Badge
                key={tag}
                variant={filters.tags.includes(tag) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
                {filters.tags.includes(tag) && (
                  <X className="h-3 w-3 ml-1" />
                )}
              </Badge>
            ))}
            {availableTags.length > 10 && (
              <Badge variant="outline" className="text-xs">
                +{availableTags.length - 10} weitere
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-sm text-gray-600">
          {getActiveFiltersCount() > 0 && (
            <span>{getActiveFiltersCount()} Filter aktiv</span>
          )}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onReset}
          className="flex items-center gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          Zurücksetzen
        </Button>
      </div>
    </div>
  )
}
