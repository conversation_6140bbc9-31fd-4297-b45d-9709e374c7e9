'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  RefreshCw, 
  Download, 
  Search, 
  Filter,
  AlertTriangle,
  Clock,
  Users,
  TrendingUp,
  FileText
} from 'lucide-react'

import { useConversationHistory } from '@/hooks/useConversationHistory'
import { ConversationTable } from './ConversationTable'
import { ConversationFilters } from './ConversationFilters'
import { ConversationDetails } from './ConversationDetails'
// import { ConversationStats } from './ConversationStats'
import { calculateConversationStats } from '@/lib/history-utils'

interface ConversationHistoryProps {
  className?: string
}

export function ConversationHistory({ className }: ConversationHistoryProps) {
  const [activeTab, setActiveTab] = useState('list')
  const [showFilters, setShowFilters] = useState(false)

  const {
    conversations,
    total,
    totalPages,
    currentPage,
    hasNext,
    hasPrevious,
    loading,
    error,
    filters,
    setFilters,
    resetFilters,
    refresh,
    loadPage,
    selectedConversation,
    loadConversationDetails,
    clearSelectedConversation,
    conversationDetailsLoading,
    conversationDetailsError
  } = useConversationHistory({
    autoRefresh: false,
    refreshInterval: 30000
  })

  const stats = calculateConversationStats(conversations)

  const handleConversationSelect = async (conversationId: string) => {
    await loadConversationDetails(conversationId)
    setActiveTab('details')
  }

  const handleBackToList = () => {
    clearSelectedConversation()
    setActiveTab('list')
  }

  const handleExport = async (format: 'csv' | 'pdf') => {
    try {
      // TODO: Implement export functionality
      console.log(`Exporting ${format} with filters:`, filters)
      // This would call an export API endpoint
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gesprächshistorie</h1>
          <p className="text-gray-600 mt-1">
            Vollständige Historie aller Gespräche mit Such- und Filterfunktionen
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filter
            {(filters.search || filters.agentIds.length > 0 || filters.status.length > 0) && (
              <Badge variant="secondary" className="ml-1">
                {[
                  filters.search && 'Suche',
                  filters.agentIds.length > 0 && 'Agent',
                  filters.status.length > 0 && 'Status'
                ].filter(Boolean).length}
              </Badge>
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('csv')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            CSV
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('pdf')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            PDF
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && !error.includes('authentifiziert') && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Gespräche gesamt</p>
                <p className="text-2xl font-bold text-gray-900">{total}</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Erfolgsrate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.successRate}%</p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ø Dauer</p>
                <p className="text-2xl font-bold text-gray-900">
                  {Math.floor(stats.averageDuration / 60)}m {stats.averageDuration % 60}s
                </p>
              </div>
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ø Bewertung</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : '-'}
                </p>
              </div>
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter & Suche</CardTitle>
          </CardHeader>
          <CardContent>
            <ConversationFilters
              filters={filters}
              onFiltersChange={setFilters}
              onReset={resetFilters}
              conversations={conversations}
            />
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list">Gesprächsliste</TabsTrigger>
          <TabsTrigger value="details" disabled={!selectedConversation}>
            Details
          </TabsTrigger>
          <TabsTrigger value="stats">Statistiken</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <ConversationTable
                conversations={conversations}
                loading={loading}
                total={total}
                currentPage={currentPage}
                totalPages={totalPages}
                hasNext={hasNext}
                hasPrevious={hasPrevious}
                onPageChange={loadPage}
                onConversationSelect={handleConversationSelect}
                filters={filters}
                onFiltersChange={setFilters}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          {selectedConversation ? (
            <ConversationDetails
              conversation={selectedConversation}
              loading={conversationDetailsLoading}
              error={conversationDetailsError}
              onBack={handleBackToList}
            />
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-gray-500">Wählen Sie ein Gespräch aus der Liste aus, um Details anzuzeigen.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-500">Statistiken werden in einer späteren Version implementiert.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
