'use client'

import React from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  Star,
  Clock,
  User,
  Phone
} from 'lucide-react'

import { ConversationHistoryItem, ConversationFilters } from '@/types/history'
import { 
  formatDateTime, 
  formatDuration, 
  getStatusLabel, 
  getStatusColor,
  getPriorityColor,
  getInitials,
  generateColorFromString,
  formatPhoneNumber
} from '@/lib/history-utils'

interface ConversationTableProps {
  conversations: ConversationHistoryItem[]
  loading: boolean
  total: number
  currentPage: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onConversationSelect: (conversationId: string) => void
  filters: ConversationFilters
  onFiltersChange: (filters: Partial<ConversationFilters>) => void
}

export function ConversationTable({
  conversations,
  loading,
  total,
  currentPage,
  totalPages,
  hasNext,
  hasPrevious,
  onPageChange,
  onConversationSelect,
  filters,
  onFiltersChange
}: ConversationTableProps) {
  const handleSort = (field: ConversationFilters['sortBy']) => {
    const newSortOrder = filters.sortBy === field && filters.sortOrder === 'desc' ? 'asc' : 'desc'
    onFiltersChange({
      sortBy: field,
      sortOrder: newSortOrder
    })
  }

  const getSortIcon = (field: ConversationFilters['sortBy']) => {
    if (filters.sortBy !== field) {
      return <ArrowUpDown className="h-4 w-4" />
    }
    return filters.sortOrder === 'desc' ? 
      <ArrowDown className="h-4 w-4" /> : 
      <ArrowUp className="h-4 w-4" />
  }

  const renderRating = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">-</span>
    
    return (
      <div className="flex items-center gap-1">
        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
        <span className="text-sm font-medium">{rating}</span>
      </div>
    )
  }

  const renderCallerInfo = (conversation: ConversationHistoryItem) => {
    const { callerInfo } = conversation
    
    if (!callerInfo) {
      return <span className="text-gray-400">Unknown</span>
    }

    return (
      <div className="space-y-1">
        {callerInfo.name && (
          <div className="flex items-center gap-2">
            <User className="h-3 w-3 text-gray-400" />
            <span className="text-sm font-medium">{callerInfo.name}</span>
          </div>
        )}
        {callerInfo.phone && (
          <div className="flex items-center gap-2">
            <Phone className="h-3 w-3 text-gray-400" />
            <span className="text-xs text-gray-600">{formatPhoneNumber(callerInfo.phone)}</span>
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  if (conversations.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="text-gray-400 mb-4">
          <Clock className="h-12 w-12 mx-auto mb-4" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations found</h3>
        <p className="text-gray-600">
          {filters.search || filters.agentIds.length > 0 || filters.status.length > 0
            ? 'Try changing or resetting your filters.'
            : 'There are no conversations yet.'}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('startTime')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Date/Time
                  {getSortIcon('startTime')}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('agentName')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Agent
                  {getSortIcon('agentName')}
                </Button>
              </TableHead>
              <TableHead>Caller</TableHead>
              <TableHead className="w-[100px]">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('duration')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Duration
                  {getSortIcon('duration')}
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('rating')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Rating
                  {getSortIcon('rating')}
                </Button>
              </TableHead>
              <TableHead>Tags</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {conversations.map((conversation) => (
              <TableRow 
                key={conversation.id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => onConversationSelect(conversation.id)}
              >
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm font-medium">
                      {formatDateTime(conversation.startTime)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {conversation.metadata.department}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback 
                        className="text-xs font-medium"
                        style={{ backgroundColor: generateColorFromString(conversation.agentName) }}
                      >
                        {getInitials(conversation.agentName)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-sm font-medium">{conversation.agentName}</div>
                      <div className="text-xs text-gray-500">ID: {conversation.agentId}</div>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  {renderCallerInfo(conversation)}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium">
                      {formatDuration(conversation.duration)}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-2">
                    <Badge 
                      variant="outline" 
                      className={getStatusColor(conversation.status)}
                    >
                      {getStatusLabel(conversation.status)}
                    </Badge>
                    {conversation.metadata.priority && (
                      <Badge 
                        variant="outline" 
                        className={`${getPriorityColor(conversation.metadata.priority)} text-xs`}
                      >
                        {conversation.metadata.priority}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  {renderRating(conversation.rating)}
                </TableCell>
                
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {conversation.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {conversation.tags.length > 2 && (
                      <Badge variant="secondary" className="text-xs">
                        +{conversation.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onConversationSelect(conversation.id)
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4 py-3 border-t">
        <div className="text-sm text-gray-700">
          Showing {((currentPage - 1) * filters.pageSize) + 1} to{' '}
          {Math.min(currentPage * filters.pageSize, total)} of {total} conversations
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            disabled={!hasPrevious}
            className="h-8 w-8 p-0"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!hasPrevious}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex items-center gap-1">
            <span className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!hasNext}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            disabled={!hasNext}
            className="h-8 w-8 p-0"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}