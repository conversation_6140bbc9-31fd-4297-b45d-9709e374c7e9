'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  Search, 
  X, 
  ChevronUp, 
  ChevronDown,
  User,
  Bot,
  Settings,
  Clock
} from 'lucide-react'

import { TranscriptEntry } from '@/types/history'
import { 
  formatTime, 
  getSentimentEmoji, 
  getSentimentColor,
  getInitials,
  generateColorFromString
} from '@/lib/history-utils'

interface TranscriptViewerProps {
  transcript: TranscriptEntry[]
  className?: string
}

export function TranscriptViewer({ transcript, className }: TranscriptViewerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [currentMatch, setCurrentMatch] = useState(0)
  const [matches, setMatches] = useState<number[]>([])
  const transcriptRef = useRef<HTMLDivElement>(null)
  const messageRefs = useRef<(HTMLDivElement | null)[]>([])

  // Search functionality
  useEffect(() => {
    if (!searchTerm.trim()) {
      setMatches([])
      setCurrentMatch(0)
      return
    }

    const newMatches: number[] = []
    transcript.forEach((entry, index) => {
      if (entry.content.toLowerCase().includes(searchTerm.toLowerCase())) {
        newMatches.push(index)
      }
    })

    setMatches(newMatches)
    setCurrentMatch(0)
  }, [searchTerm, transcript])

  // Auto-scroll to current match
  useEffect(() => {
    if (matches.length > 0 && currentMatch < matches.length) {
      const matchIndex = matches[currentMatch]
      const element = messageRefs.current[matchIndex]
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }
  }, [currentMatch, matches])

  const handlePreviousMatch = () => {
    if (matches.length > 0) {
      setCurrentMatch((prev) => (prev > 0 ? prev - 1 : matches.length - 1))
    }
  }

  const handleNextMatch = () => {
    if (matches.length > 0) {
      setCurrentMatch((prev) => (prev < matches.length - 1 ? prev + 1 : 0))
    }
  }

  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\\]/g, '\\$&')})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    )
  }

  const getSpeakerIcon = (speaker: TranscriptEntry['speaker']) => {
    switch (speaker) {
      case 'agent':
        return <Bot className="h-4 w-4" />
      case 'caller':
        return <User className="h-4 w-4" />
      case 'system':
        return <Settings className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getSpeakerLabel = (speaker: TranscriptEntry['speaker']) => {
    switch (speaker) {
      case 'agent':
        return 'Agent'
      case 'caller':
        return 'Caller'
      case 'system':
        return 'System'
      default:
        return 'Unknown'
    }
  }

  const getSpeakerColor = (speaker: TranscriptEntry['speaker']) => {
    switch (speaker) {
      case 'agent':
        return 'bg-blue-500'
      case 'caller':
        return 'bg-gray-500'
      case 'system':
        return 'bg-yellow-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getMessageAlignment = (speaker: TranscriptEntry['speaker']) => {
    switch (speaker) {
      case 'agent':
        return 'justify-end'
      case 'caller':
        return 'justify-start'
      case 'system':
        return 'justify-center'
      default:
        return 'justify-start'
    }
  }

  const getMessageStyle = (speaker: TranscriptEntry['speaker']) => {
    switch (speaker) {
      case 'agent':
        return 'bg-blue-100 text-blue-900 ml-12'
      case 'caller':
        return 'bg-gray-100 text-gray-900 mr-12'
      case 'system':
        return 'bg-yellow-50 text-yellow-800 mx-8 text-center'
      default:
        return 'bg-gray-100 text-gray-900 mr-12'
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search in transcript..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchTerm('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {matches.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {currentMatch + 1} of {matches.length}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousMatch}
              className="h-8 w-8 p-0"
            >
              <ChevronUp className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextMatch}
              className="h-8 w-8 p-0"
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Transcript */}
      <div 
        ref={transcriptRef}
        className="max-h-96 overflow-y-auto space-y-4 p-4 border rounded-lg bg-gray-50"
      >
        {transcript.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-8 w-8 mx-auto mb-2" />
            <p>No transcript available</p>
          </div>
        ) : (
          transcript.map((entry, index) => (
            <div
              key={entry.id}
              ref={(el) => (messageRefs.current[index] = el)}
              className={`flex ${getMessageAlignment(entry.speaker)}`}
            >
              <div className={`max-w-[80%] ${getMessageStyle(entry.speaker)} rounded-lg p-3 shadow-sm`}>
                {/* Message Header */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Avatar className={`h-6 w-6 ${getSpeakerColor(entry.speaker)}`}>
                      <AvatarFallback className="text-white text-xs">
                        {getSpeakerIcon(entry.speaker)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs font-medium">
                      {getSpeakerLabel(entry.speaker)}
                    </span>
                    {entry.eventType === 'tool_use' && entry.metadata?.toolName && (
                      <Badge variant="outline" className="text-xs">
                        {entry.metadata.toolName}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {entry.sentiment && (
                      <span className="text-sm">
                        {getSentimentEmoji(entry.sentiment)}
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {formatTime(entry.timestamp)}
                    </span>
                  </div>
                </div>

                {/* Message Content */}
                <div className="text-sm leading-relaxed">
                  {highlightText(entry.content, searchTerm)}
                </div>

                {/* Message Footer */}
                <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-200">
                  <div className="flex items-center gap-2">
                    {entry.confidence && (
                      <span className="text-xs text-gray-500">
                        Confidence: {Math.round(entry.confidence * 100)}%
                      </span>
                    )}
                    {entry.eventType !== 'message' && (
                      <Badge variant="secondary" className="text-xs">
                        {entry.eventType}
                      </Badge>
                    )}
                  </div>
                  
                  {entry.sentiment && (
                    <span className={`text-xs ${getSentimentColor(entry.sentiment)}`}>
                      {entry.sentiment}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Transcript Stats */}
      <div className="flex items-center justify-between text-sm text-gray-600 pt-2 border-t">
        <div>
          {transcript.length} Messages
        </div>
        <div className="flex items-center gap-4">
          <span>
            Agent: {transcript.filter(t => t.speaker === 'agent').length}
          </span>
          <span>
            Caller: {transcript.filter(t => t.speaker === 'caller').length}
          </span>
          <span>
            System: {transcript.filter(t => t.speaker === 'system').length}
          </span>
        </div>
      </div>
    </div>
  )
}