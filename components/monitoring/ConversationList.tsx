'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { LiveConversation } from '@/types/monitoring'
import {
  formatConversationDuration,
  formatLastActivity,
  getConversationStatusColor,
  getConversationStatusLabel,
  getPriorityColor,
  getPriorityLabel,
  formatConnectionQuality,
  shouldShowSilenceWarning,
  shouldShowQualityWarning,
  getAgentColor,
  getInitials
} from '@/lib/monitoring-utils'
import {
  Phone,
  Clock,
  Signal,
  AlertTriangle,
  User,
  MapPin
} from 'lucide-react'

interface ConversationListProps {
  conversations: LiveConversation[]
  selectedConversation: LiveConversation | null
  onSelectConversation: (conversation: LiveConversation) => void
  loading?: boolean
  className?: string
}

export function ConversationList({ 
  conversations, 
  selectedConversation, 
  onSelectConversation,
  loading = false,
  className = '' 
}: ConversationListProps) {
  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (conversations.length === 0) {
    return (
      <Card className={`text-center py-8 ${className}`}>
        <CardContent>
          <Phone className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No active conversations
          </h3>
          <p className="text-gray-500">
            There are currently no active conversations.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {conversations.map((conversation) => {
        const isSelected = selectedConversation?.id === conversation.id
        const showSilenceWarning = shouldShowSilenceWarning(conversation)
        const showQualityWarning = shouldShowQualityWarning(conversation)
        const hasWarnings = showSilenceWarning || showQualityWarning

        return (
          <Card 
            key={conversation.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              isSelected 
                ? 'ring-2 ring-blue-500 shadow-md' 
                : hasWarnings 
                  ? 'border-orange-300 bg-orange-50' 
                  : ''
            }`}
            onClick={() => onSelectConversation(conversation)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={conversation.agentAvatar} alt={conversation.agentName} />
                    <AvatarFallback className={`${getAgentColor(conversation.agentName)} text-white`}>
                      {getInitials(conversation.agentName)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-sm text-gray-900 truncate">
                      {conversation.agentName}
                    </h3>
                    <p className="text-xs text-gray-500 truncate">
                      {conversation.metadata.department || 'General'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Priority */}
                  <Badge
                    variant="outline"
                    className={`text-xs ${getPriorityColor(conversation.metadata.priority)}`}
                  >
                    {getPriorityLabel(conversation.metadata.priority)}
                  </Badge>
                  
                  {/* Status */}
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getConversationStatusColor(conversation.status)}`}
                  >
                    {getConversationStatusLabel(conversation.status)}
                  </Badge>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Caller Information */}
              {conversation.callerInfo && (
                <div className="flex items-center space-x-2 mb-3 p-2 bg-gray-50 rounded-lg">
                  <User className="h-4 w-4 text-gray-500" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {conversation.callerInfo.name || 'Unknown Caller'}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      {conversation.callerInfo.phone && (
                        <span>{conversation.callerInfo.phone}</span>
                      )}
                      {conversation.callerInfo.location && (
                        <>
                          <span>•</span>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{conversation.callerInfo.location}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Conversation Metrics */}
              <div className="grid grid-cols-2 gap-3 mb-3">
                {/* Duration */}
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {formatConversationDuration(conversation.duration)}
                    </p>
                    <p className="text-xs text-gray-500">Dauer</p>
                  </div>
                </div>

                {/* Connection Quality */}
                <div className="flex items-center space-x-2">
                  <Signal className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {formatConnectionQuality(conversation.qualityMetrics.connectionQuality)}
                    </p>
                    <p className="text-xs text-gray-500">Qualität</p>
                  </div>
                </div>
              </div>

              {/* Audio Level */}
              <div className="space-y-2 mb-3">
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <span>Audio-Level</span>
                  <span>{conversation.qualityMetrics.latency}ms</span>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {/* Agent Audio */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Agent</span>
                      <span className="text-gray-700">{conversation.qualityMetrics.audioLevel.agent}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${conversation.qualityMetrics.audioLevel.agent}%` }}
                      />
                    </div>
                  </div>

                  {/* Caller Audio */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Anrufer</span>
                      <span className="text-gray-700">{conversation.qualityMetrics.audioLevel.caller}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${conversation.qualityMetrics.audioLevel.caller}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Warnings */}
              {hasWarnings && (
                <div className="flex items-center space-x-2 p-2 bg-orange-100 border border-orange-200 rounded-lg mb-3">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <div className="text-xs text-orange-700">
                    {showSilenceWarning && <p>Lange Stille erkannt</p>}
                    {showQualityWarning && <p>Schlechte Verbindungsqualität</p>}
                  </div>
                </div>
              )}

              {/* Last Activity */}
              <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
                <span>Letzte Aktivität:</span>
                <span>{formatLastActivity(conversation.lastActivity)}</span>
              </div>

              {/* Tags */}
              {conversation.metadata.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {conversation.metadata.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {conversation.metadata.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{conversation.metadata.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
