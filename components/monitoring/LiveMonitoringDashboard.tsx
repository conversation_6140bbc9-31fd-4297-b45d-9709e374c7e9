'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { LiveConversation } from '@/types/monitoring'
import { useLiveConversations } from '@/hooks/useLiveConversations'
import { useWebSocketConnection } from '@/hooks/useWebSocketConnection'
import { ConversationList } from './ConversationList'
import { LiveTranscript } from './LiveTranscript'
import { TakeoverModal } from './TakeoverModal'
import { MonitoringFilters } from './MonitoringFilters'
import { calculateConversationStats } from '@/lib/monitoring-utils'
import { 
  Phone, 
  Users, 
  Clock, 
  AlertTriangle, 
  Activity,
  Wifi,
  WifiOff,
  <PERSON><PERSON><PERSON>,
  PhoneCall
} from 'lucide-react'

interface LiveMonitoringDashboardProps {
  className?: string
}

export function LiveMonitoringDashboard({ className = '' }: LiveMonitoringDashboardProps) {
  const [showTakeoverModal, setShowTakeoverModal] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const {
    conversations,
    loading,
    error,
    filters,
    setFilters,
    refresh,
    total,
    selectedConversation,
    setSelectedConversation
  } = useLiveConversations({
    refreshInterval: 5000, // 5 Sekunden - weniger aggressiv
    autoRefresh: false // Deaktiviert Auto-Refresh um Flackern zu vermeiden
  })

  const {
    isConnected,
    connectionStatus,
    lastConnected
  } = useWebSocketConnection({
    url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001',
    enabled: false // Disable WebSocket for now to prevent connection errors
  })

  const stats = calculateConversationStats(conversations)

  const handleTakeoverClick = () => {
    if (selectedConversation) {
      setShowTakeoverModal(true)
    }
  }

  const handleTakeoverSuccess = () => {
    // Refresh conversations after successful takeover
    refresh()
    setSelectedConversation(null)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Connection Status */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Echtzeit-Gesprächsmonitoring
          </h1>
          <p className="text-gray-600 mt-1">
            Live-Überwachung aller aktiven Gespräche
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">Verbunden</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600 font-medium">Getrennt</span>
              </>
            )}
          </div>

          {/* Live Indicator */}
          {isConnected && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-red-600 font-medium">LIVE</span>
            </div>
          )}
        </div>
      </div>

      {/* Error Alert - Only show persistent errors, not auth loading states */}
      {error && !error.includes('authentifiziert') && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Connection Error Alert */}
      {!isConnected && (
        <Alert>
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            Verbindung zum Live-Monitoring unterbrochen. 
            Daten werden möglicherweise nicht in Echtzeit aktualisiert.
            {lastConnected && (
              <span className="block mt-1 text-sm">
                Letzte Verbindung: {lastConnected.toLocaleTimeString('de-DE')}
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Phone className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                <p className="text-sm text-gray-600">Gesamt</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                <p className="text-sm text-gray-600">Aktiv</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold text-yellow-600">{stats.onHold}</p>
                <p className="text-sm text-gray-600">Warteschleife</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <PhoneCall className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold text-blue-600">{stats.connecting}</p>
                <p className="text-sm text-gray-600">Verbindung</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(stats.averageQuality * 10) / 10}
                </p>
                <p className="text-sm text-gray-600">Ø Qualität</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Filters and Conversation List */}
        <div className="lg:col-span-1 space-y-4">
          <MonitoringFilters
            filters={filters}
            onFiltersChange={setFilters}
            onRefresh={refresh}
            loading={loading}
            totalConversations={total}
          />

          <ConversationList
            conversations={conversations}
            selectedConversation={selectedConversation}
            onSelectConversation={setSelectedConversation}
            loading={loading}
          />
        </div>

        {/* Right Column - Conversation Details */}
        <div className="lg:col-span-2">
          {selectedConversation ? (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
              <div className="flex items-center justify-between mb-4">
                <TabsList>
                  <TabsTrigger value="overview">Übersicht</TabsTrigger>
                  <TabsTrigger value="transcript">Live-Transkript</TabsTrigger>
                  <TabsTrigger value="details">Details</TabsTrigger>
                </TabsList>

                {/* Takeover Button */}
                <Button
                  onClick={handleTakeoverClick}
                  variant="outline"
                  className="text-orange-600 border-orange-600 hover:bg-orange-50"
                >
                  <PhoneCall className="h-4 w-4 mr-2" />
                  Gespräch übernehmen
                </Button>
              </div>

              <TabsContent value="overview" className="mt-0">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle>Gesprächsübersicht</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Conversation Overview Content */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Agent</h4>
                          <p className="text-sm text-gray-600">{selectedConversation.agentName}</p>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Status</h4>
                          <Badge variant="outline">
                            {selectedConversation.status}
                          </Badge>
                        </div>
                      </div>
                      {/* Add more overview content here */}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="transcript" className="mt-0">
                <LiveTranscript 
                  conversation={selectedConversation}
                  className="h-[600px]"
                />
              </TabsContent>

              <TabsContent value="details" className="mt-0">
                <Card className="h-[600px]">
                  <CardHeader>
                    <CardTitle>Technische Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Technical details content */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Verbindungsqualität</h4>
                        <p className="text-sm text-gray-600">
                          {selectedConversation.qualityMetrics.connectionQuality}/5 Sterne
                        </p>
                      </div>
                      {/* Add more technical details here */}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="h-[600px] flex items-center justify-center">
              <CardContent className="text-center">
                <Settings className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Kein Gespräch ausgewählt
                </h3>
                <p className="text-gray-500">
                  Wählen Sie ein Gespräch aus der Liste aus, um Details zu sehen
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Takeover Modal */}
      <TakeoverModal
        conversation={selectedConversation}
        isOpen={showTakeoverModal}
        onClose={() => setShowTakeoverModal(false)}
        onSuccess={handleTakeoverSuccess}
      />
    </div>
  )
}
