'use client'

import { useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { LiveConversation, TranscriptEntry } from '@/types/monitoring'
import {
  formatTimestamp,
  getSentimentEmoji,
  getSentimentColor,
  getEventTypeIcon,
  getAgentColor,
  getInitials
} from '@/lib/monitoring-utils'
import { Bo<PERSON>, User, Settings, Clock } from 'lucide-react'

interface LiveTranscriptProps {
  conversation: LiveConversation | null
  className?: string
}

export function LiveTranscript({ conversation, className = '' }: LiveTranscriptProps) {
  const scrollRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [conversation?.transcript])

  if (!conversation) {
    return (
      <Card className={`h-full ${className}`}>
        <CardHeader>
          <CardTitle className="text-lg">Live Transcript</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-gray-500">
            <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Select a conversation to see the live transcript</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderTranscriptEntry = (entry: TranscriptEntry) => {
    const isAgent = entry.speaker === 'agent'
    const isCaller = entry.speaker === 'caller'
    const isSystem = entry.speaker === 'system'

    if (isSystem) {
      return (
        <div key={entry.id} className="flex justify-center my-4">
          <div className="flex items-center space-x-2 px-3 py-2 bg-yellow-100 border border-yellow-200 rounded-full">
            <span className="text-lg">{getEventTypeIcon(entry.eventType)}</span>
            <span className="text-sm text-yellow-800 font-medium">
              {entry.content}
            </span>
            <span className="text-xs text-yellow-600">
              {formatTimestamp(entry.timestamp)}
            </span>
          </div>
        </div>
      )
    }

    return (
      <div key={entry.id} className={`flex mb-4 ${isAgent ? 'justify-end' : 'justify-start'}`}>
        <div className={`flex max-w-[80%] ${isAgent ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Avatar */}
          <div className={`flex-shrink-0 ${isAgent ? 'ml-3' : 'mr-3'}`}>
            <Avatar className="h-8 w-8">
              {isAgent ? (
                <>
                  <AvatarImage src={conversation.agentAvatar} alt={conversation.agentName} />
                  <AvatarFallback className={`${getAgentColor(conversation.agentName)} text-white text-xs`}>
                    {getInitials(conversation.agentName)}
                  </AvatarFallback>
                </>
              ) : (
                <AvatarFallback className="bg-gray-100 text-gray-600">
                  <User className="h-4 w-4" />
                </AvatarFallback>
              )}
            </Avatar>
          </div>

          {/* Message Bubble */}
          <div className={`flex flex-col ${isAgent ? 'items-end' : 'items-start'}`}>
            {/* Speaker Name and Timestamp */}
            <div className={`flex items-center space-x-2 mb-1 ${isAgent ? 'flex-row-reverse' : 'flex-row'}`}>
              <span className="text-xs font-medium text-gray-600">
                {isAgent ? conversation.agentName : (conversation.callerInfo?.name || 'Caller')}
              </span>
              <span className="text-xs text-gray-400">
                {formatTimestamp(entry.timestamp)}
              </span>
              {entry.confidence && (
                <span className="text-xs text-gray-400">
                  ({Math.round(entry.confidence * 100)}%)
                </span>
              )}
            </div>

            {/* Message Content */}
            <div className={`relative px-4 py-2 rounded-lg max-w-full ${ 
              isAgent 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-900'
            }`}>
              <p className="text-sm whitespace-pre-wrap break-words">
                {entry.content}
              </p>

              {/* Sentiment Indicator */}
              {entry.sentiment && (
                <div className={`absolute -bottom-1 ${isAgent ? '-left-1' : '-right-1'}`}>
                  <span className="text-lg" title={`Sentiment: ${entry.sentiment}`}>
                    {getSentimentEmoji(entry.sentiment)}
                  </span>
                </div>
              )}

              {/* Tool Usage Indicator */}
              {entry.eventType === 'tool_use' && entry.metadata?.toolName && (
                <div className="mt-2 pt-2 border-t border-blue-400 text-xs opacity-90">
                  <div className="flex items-center space-x-1">
                    <span>🔧</span>
                    <span>Tool used: {entry.metadata.toolName}</span>
                  </div>
                </div>
              )}

              {/* Transfer Indicator */}
              {entry.eventType === 'transfer' && entry.metadata?.transferTarget && (
                <div className="mt-2 pt-2 border-t border-blue-400 text-xs opacity-90">
                  <div className="flex items-center space-x-1">
                    <span>📞</span>
                    <span>Transferred to: {entry.metadata.transferTarget}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card className={`h-full flex flex-col ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Live Transcript</CardTitle>
          <div className="flex items-center space-x-2">
            {/* Live Indicator */}
            {conversation.status === 'active' && (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-red-600 font-medium">LIVE</span>
              </div>
            )}
            
            {/* Message Count */}
            <Badge variant="secondary" className="text-xs">
              {conversation.transcript.length} Messages
            </Badge>
          </div>
        </div>

        {/* Conversation Info */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Started: {formatTimestamp(conversation.startTime)}</span>
          </div>
          
          {conversation.callerInfo?.name && (
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>{conversation.callerInfo.name}</span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <div 
          ref={scrollRef}
          className="h-full overflow-y-auto pr-2 space-y-1"
          style={{ scrollBehavior: 'smooth' }}
        >
          {conversation.transcript.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <Bot className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No messages yet</p>
                <p className="text-sm">The transcript will be displayed here in real-time</p>
              </div>
            </div>
          ) : (
            conversation.transcript.map(renderTranscriptEntry)
          )}

          {/* Typing Indicator (if agent is typing) */}
          {conversation.status === 'active' && (
            <div className="flex justify-end mb-4">
              <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-xs text-blue-600">Agent is typing...</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}