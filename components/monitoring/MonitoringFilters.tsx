'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MonitoringFilters } from '@/types/monitoring'
import { 
  Search, 
  Filter, 
  X, 
  RefreshCw,
  SortAsc,
  SortDesc
} from 'lucide-react'

interface MonitoringFiltersProps {
  filters: MonitoringFilters
  onFiltersChange: (filters: Partial<MonitoringFilters>) => void
  onRefresh?: () => void
  loading?: boolean
  totalConversations?: number
  className?: string
}

export function MonitoringFilters({
  filters,
  onFiltersChange,
  onRefresh,
  loading = false,
  totalConversations = 0,
  className = ''
}: MonitoringFiltersProps) {
  const hasActiveFilters = 
    filters.search !== '' ||
    filters.status !== 'all' ||
    filters.agent !== 'all' ||
    filters.priority !== 'all'

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      status: 'all',
      agent: 'all',
      priority: 'all',
      sortBy: 'duration',
      sortOrder: 'desc'
    })
  }

  const toggleSortOrder = () => {
    onFiltersChange({
      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc'
    })
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters & Search</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* Results Count */}
            <Badge variant="secondary" className="text-xs">
              {totalConversations} Conversations
            </Badge>
            
            {/* Refresh Button */}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search" className="text-sm font-medium">
            Search
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Agent, Caller or Conversation ID..."
              value={filters.search}
              onChange={(e) => onFiltersChange({ search: e.target.value })}
              className="pl-10"
            />
          </div>
        </div>

        {/* Filter Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Status</Label>
            <Select
              value={filters.status}
              onValueChange={(value) => onFiltersChange({ status: value as MonitoringFilters['status'] })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="connecting">Connecting</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="on-hold">On Hold</SelectItem>
                <SelectItem value="ending">Ending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Agent Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Agent</Label>
            <Select
              value={filters.agent}
              onValueChange={(value) => onFiltersChange({ agent: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Agents</SelectItem>
                <SelectItem value="1">Customer Service Agent</SelectItem>
                <SelectItem value="2">Sales Assistant</SelectItem>
                <SelectItem value="3">Technical Support</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Priority Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Priority</Label>
            <Select
              value={filters.priority}
              onValueChange={(value) => onFiltersChange({ priority: value as MonitoringFilters['priority'] })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Sort Options */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex items-center space-x-4">
            <Label className="text-sm font-medium">Sort by:</Label>
            <Select
              value={filters.sortBy}
              onValueChange={(value) => onFiltersChange({ sortBy: value as MonitoringFilters['sortBy'] })}
            >
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="startTime">Start Time</SelectItem>
                <SelectItem value="agentName">Agent Name</SelectItem>
                <SelectItem value="priority">Priority</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleSortOrder}
              className="h-8 w-8 p-0"
            >
              {filters.sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-gray-600 hover:text-gray-900"
            >
              <X className="h-4 w-4 mr-1" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-600">Active Filters:</span>
            
            {filters.search && (
              <Badge variant="secondary" className="text-xs">
                Search: "{filters.search}"
                <button
                  onClick={() => onFiltersChange({ search: '' })}
                  className="ml-1 hover:text-gray-700"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            
            {filters.status !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                Status: {filters.status}
                <button
                  onClick={() => onFiltersChange({ status: 'all' })}
                  className="ml-1 hover:text-gray-700"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            
            {filters.agent !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                Agent: {filters.agent}
                <button
                  onClick={() => onFiltersChange({ agent: 'all' })}
                  className="ml-1 hover:text-gray-700"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            
            {filters.priority !== 'all' && (
              <Badge variant="secondary" className="text-xs">
                Priority: {filters.priority}
                <button
                  onClick={() => onFiltersChange({ priority: 'all' })}
                  className="ml-1 hover:text-gray-700"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}