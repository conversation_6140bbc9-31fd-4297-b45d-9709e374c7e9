'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { LiveConversation, TakeoverRequest } from '@/types/monitoring'
import { useTakeover } from '@/hooks/useTakeover'
import { 
  getTakeoverReasonLabel,
  formatConversationDuration,
  getAgentColor,
  getInitials
} from '@/lib/monitoring-utils'
import { 
  AlertTriangle, 
  Phone, 
  Clock, 
  User, 
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react'

interface TakeoverModalProps {
  conversation: LiveConversation | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

const TAKEOVER_REASONS = [
  { value: 'quality_issue', label: 'Qualitätsproblem', description: 'Schlechte Verbindung oder Audio-Qualität' },
  { value: 'escalation', label: 'Eskalation', description: 'Kunde möchte mit Vorgesetztem sprechen' },
  { value: 'training', label: 'Schulung/Training', description: 'Agent benötigt Unterstützung oder Training' },
  { value: 'technical_problem', label: 'Technisches Problem', description: 'System- oder Softwareprobleme' },
  { value: 'other', label: 'Sonstiges', description: 'Andere Gründe (bitte spezifizieren)' }
] as const

export function TakeoverModal({ 
  conversation, 
  isOpen, 
  onClose, 
  onSuccess 
}: TakeoverModalProps) {
  const [selectedReason, setSelectedReason] = useState<string>('')
  const [note, setNote] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)

  const { takeover, loading, error } = useTakeover({
    onSuccess: (response) => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        if (onSuccess) onSuccess()
        // Reset form
        setSelectedReason('')
        setNote('')
      }, 2000)
    }
  })

  const handleTakeover = async () => {
    if (!conversation || !selectedReason) return

    try {
      await takeover({
        conversationId: conversation.id,
        reason: selectedReason as TakeoverRequest['reason'],
        note: note.trim() || undefined
      })
    } catch (err) {
      // Error is handled by the hook
      console.error('Takeover failed:', err)
    }
  }

  const handleClose = () => {
    if (loading) return // Prevent closing during takeover
    onClose()
    // Reset form after a short delay to avoid visual glitch
    setTimeout(() => {
      setSelectedReason('')
      setNote('')
      setShowSuccess(false)
    }, 300)
  }

  if (!conversation) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        {showSuccess ? (
          // Success State
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <DialogTitle className="text-xl mb-2">Übernahme erfolgreich!</DialogTitle>
            <DialogDescription className="text-base">
              Das Gespräch wurde erfolgreich übernommen. Sie werden nun automatisch weitergeleitet.
            </DialogDescription>
          </div>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Phone className="h-5 w-5 text-blue-600" />
                <span>Gespräch übernehmen</span>
              </DialogTitle>
              <DialogDescription>
                Sie sind dabei, das folgende Gespräch zu übernehmen. Bitte geben Sie einen Grund an.
              </DialogDescription>
            </DialogHeader>

            {/* Conversation Info */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={conversation.agentAvatar} alt={conversation.agentName} />
                  <AvatarFallback className={`${getAgentColor(conversation.agentName)} text-white`}>
                    {getInitials(conversation.agentName)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{conversation.agentName}</h3>
                  <p className="text-sm text-gray-600">{conversation.metadata.department}</p>
                </div>
                <Badge variant="outline" className="text-xs">
                  {conversation.status === 'active' ? 'Aktiv' : 'Inaktiv'}
                </Badge>
              </div>

              {conversation.callerInfo && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <User className="h-4 w-4" />
                  <span>{conversation.callerInfo.name || 'Unbekannter Anrufer'}</span>
                  {conversation.callerInfo.phone && (
                    <>
                      <span>•</span>
                      <span>{conversation.callerInfo.phone}</span>
                    </>
                  )}
                </div>
              )}

              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>Dauer: {formatConversationDuration(conversation.duration)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span>Nachrichten: {conversation.transcript.length}</span>
                </div>
              </div>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Reason Selection */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">Grund für die Übernahme</Label>
                <p className="text-sm text-gray-600 mb-3">
                  Wählen Sie den Hauptgrund für die Gesprächsübernahme aus.
                </p>
              </div>

              <RadioGroup value={selectedReason} onValueChange={setSelectedReason}>
                <div className="space-y-3">
                  {TAKEOVER_REASONS.map((reason) => (
                    <div key={reason.value} className="flex items-start space-x-3">
                      <RadioGroupItem 
                        value={reason.value} 
                        id={reason.value}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <Label 
                          htmlFor={reason.value} 
                          className="font-medium cursor-pointer"
                        >
                          {reason.label}
                        </Label>
                        <p className="text-sm text-gray-600 mt-1">
                          {reason.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            {/* Optional Note */}
            <div className="space-y-2">
              <Label htmlFor="note">
                Zusätzliche Notizen <span className="text-gray-500">(optional)</span>
              </Label>
              <Textarea
                id="note"
                placeholder="Weitere Details oder Kontext zur Übernahme..."
                value={note}
                onChange={(e) => setNote(e.target.value)}
                rows={3}
                maxLength={500}
              />
              <p className="text-xs text-gray-500 text-right">
                {note.length}/500 Zeichen
              </p>
            </div>

            {/* Warning for Active Conversations */}
            {conversation.status === 'active' && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Achtung:</strong> Dieses Gespräch ist derzeit aktiv. 
                  Die Übernahme wird den aktuellen Agent sofort benachrichtigen 
                  und das Gespräch zu Ihnen weiterleiten.
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={handleClose}
                disabled={loading}
              >
                Abbrechen
              </Button>
              <Button 
                onClick={handleTakeover}
                disabled={!selectedReason || loading}
                className="min-w-[120px]"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Übernehme...
                  </>
                ) : (
                  'Gespräch übernehmen'
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
