import * as React from "react"

const FAIL_BREAKPOINT = 768

export function useIsFail() {
  const [isFail, setIsFail] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${FAIL_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsFail(window.innerWidth < FAIL_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsFail(window.innerWidth < FAIL_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isFail
}
