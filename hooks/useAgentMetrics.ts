'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/lib/auth-context'
import { 
  AgentDashboardData, 
  DashboardSummary, 
  DashboardFilters, 
  DashboardAgentsResponse 
} from '@/types/dashboard'

interface UseAgentMetricsOptions {
  refreshInterval?: number
  autoRefresh?: boolean
}

interface UseAgentMetricsReturn {
  agents: AgentDashboardData[]
  summary: DashboardSummary | null
  loading: boolean
  error: string | null
  filters: DashboardFilters
  setFilters: (filters: Partial<DashboardFilters>) => void
  refresh: () => Promise<void>
  total: number
}

const DEFAULT_FILTERS: DashboardFilters = {
  search: '',
  status: 'all',
  sortBy: 'name',
  sortOrder: 'asc'
}

export function useAgentMetrics(options: UseAgentMetricsOptions = {}): UseAgentMetricsReturn {
  const { refreshInterval = 30000, autoRefresh = true } = options
  const { user, getAccessToken } = useAuth()
  
  const [agents, setAgents] = useState<AgentDashboardData[]>([])
  const [summary, setSummary] = useState<DashboardSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFiltersState] = useState<DashboardFilters>(DEFAULT_FILTERS)
  const [total, setTotal] = useState(0)

  const setFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }))
  }, [])

  const fetchAgentMetrics = useCallback(async () => {
    if (!user) {
      setError('Benutzer nicht authentifiziert')
      setLoading(false)
      return
    }

    try {
      setError(null)
      
      const accessToken = await getAccessToken()
      if (!accessToken) {
        throw new Error('Kein Access Token verfügbar')
      }

      // Build query parameters
      const params = new URLSearchParams()
      if (filters.search) params.append('search', filters.search)
      if (filters.status !== 'all') params.append('status', filters.status)
      params.append('sortBy', filters.sortBy)
      params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`/api/dashboard/agents?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Fehler beim Laden der Agent-Metriken')
      }

      const data: DashboardAgentsResponse = await response.json()
      
      setAgents(data.agents)
      setSummary(data.summary)
      setTotal(data.total)
    } catch (err) {
      console.error('Error fetching agent metrics:', err)
      setError(err instanceof Error ? err.message : 'Ein unerwarteter Fehler ist aufgetreten')
    } finally {
      setLoading(false)
    }
  }, [user, getAccessToken, filters])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchAgentMetrics()
  }, [fetchAgentMetrics])

  // Initial load and filter changes
  useEffect(() => {
    fetchAgentMetrics()
  }, [fetchAgentMetrics])

  // Auto-refresh interval
  useEffect(() => {
    if (!autoRefresh || !user) return

    const interval = setInterval(() => {
      fetchAgentMetrics()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, user, fetchAgentMetrics])

  return {
    agents,
    summary,
    loading,
    error,
    filters,
    setFilters,
    refresh,
    total
  }
}
