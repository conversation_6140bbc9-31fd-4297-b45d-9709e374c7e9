// Analytics Data Hooks für Story 4: Analytics-Reports und Metriken

import { useState, useEffect, useCallback } from 'react'
import { 
  AnalyticsQuery, 
  AnalyticsResponse,
  PeriodicReport,
  SuccessRateAnalysis,
  ConversationDurationStats,
  ToolUsageAnalytics
} from '@/types/analytics'
import axios from 'axios'

// Hook für periodische Reports
export function usePeriodicReport(query: AnalyticsQuery) {
  const [data, setData] = useState<AnalyticsResponse<PeriodicReport> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    if (!query.timeRange.startDate || !query.timeRange.endDate) return

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        startDate: query.timeRange.startDate.toISOString(),
        endDate: query.timeRange.endDate.toISOString(),
        period: query.timeRange.period
      })

      if (query.agentIds && query.agentIds.length > 0) {
        params.append('agentIds', query.agentIds.join(','))
      }

      const response = await axios.get(`/api/analytics/periodic-report?${params}`)
      setData(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der periodischen Reports')
      console.error('Fehler beim Laden der periodischen Reports:', err)
    } finally {
      setLoading(false)
    }
  }, [query])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Hook für Erfolgsrate-Analyse
export function useSuccessRateAnalysis(query: AnalyticsQuery) {
  const [data, setData] = useState<AnalyticsResponse<SuccessRateAnalysis> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    if (!query.timeRange.startDate || !query.timeRange.endDate) return

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        startDate: query.timeRange.startDate.toISOString(),
        endDate: query.timeRange.endDate.toISOString()
      })

      if (query.agentIds && query.agentIds.length > 0) {
        params.append('agentIds', query.agentIds.join(','))
      }

      if (query.groupBy) {
        params.append('groupBy', query.groupBy)
      }

      const response = await axios.get(`/api/analytics/success-rate?${params}`)
      setData(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der Erfolgsrate-Analyse')
      console.error('Fehler beim Laden der Erfolgsrate-Analyse:', err)
    } finally {
      setLoading(false)
    }
  }, [query])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Hook für Gesprächsdauer-Statistiken
export function useConversationDurationStats(query: AnalyticsQuery) {
  const [data, setData] = useState<AnalyticsResponse<ConversationDurationStats> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    if (!query.timeRange.startDate || !query.timeRange.endDate) return

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        startDate: query.timeRange.startDate.toISOString(),
        endDate: query.timeRange.endDate.toISOString()
      })

      if (query.agentIds && query.agentIds.length > 0) {
        params.append('agentIds', query.agentIds.join(','))
      }

      if (query.filters?.minDuration) {
        params.append('minDuration', query.filters.minDuration.toString())
      }

      if (query.filters?.maxDuration) {
        params.append('maxDuration', query.filters.maxDuration.toString())
      }

      const response = await axios.get(`/api/analytics/conversation-duration?${params}`)
      setData(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der Gesprächsdauer-Statistiken')
      console.error('Fehler beim Laden der Gesprächsdauer-Statistiken:', err)
    } finally {
      setLoading(false)
    }
  }, [query])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Hook für Tool-Nutzungs-Analytics
export function useToolUsageAnalytics(query: AnalyticsQuery) {
  const [data, setData] = useState<AnalyticsResponse<ToolUsageAnalytics> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    if (!query.timeRange.startDate || !query.timeRange.endDate) return

    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        startDate: query.timeRange.startDate.toISOString(),
        endDate: query.timeRange.endDate.toISOString()
      })

      if (query.agentIds && query.agentIds.length > 0) {
        params.append('agentIds', query.agentIds.join(','))
      }

      if (query.filters?.toolNames && query.filters.toolNames.length > 0) {
        params.append('toolNames', query.filters.toolNames.join(','))
      }

      const response = await axios.get(`/api/analytics/tool-usage?${params}`)
      setData(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der Tool-Nutzungs-Analytics')
      console.error('Fehler beim Laden der Tool-Nutzungs-Analytics:', err)
    } finally {
      setLoading(false)
    }
  }, [query])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Kombinierter Hook für alle Analytics-Daten
export function useAnalytics(query: AnalyticsQuery) {
  const periodicReport = usePeriodicReport(query)
  const successRateAnalysis = useSuccessRateAnalysis(query)
  const durationStats = useConversationDurationStats(query)
  const toolUsageAnalytics = useToolUsageAnalytics(query)

  const loading = periodicReport.loading || successRateAnalysis.loading || 
                  durationStats.loading || toolUsageAnalytics.loading

  const error = periodicReport.error || successRateAnalysis.error || 
                durationStats.error || toolUsageAnalytics.error

  const refetchAll = useCallback(() => {
    periodicReport.refetch()
    successRateAnalysis.refetch()
    durationStats.refetch()
    toolUsageAnalytics.refetch()
  }, [periodicReport.refetch, successRateAnalysis.refetch, durationStats.refetch, toolUsageAnalytics.refetch])

  return {
    data: {
      periodicReport: periodicReport.data,
      successRateAnalysis: successRateAnalysis.data,
      durationStats: durationStats.data,
      toolUsageAnalytics: toolUsageAnalytics.data
    },
    loading,
    error,
    refetch: refetchAll
  }
}

// Hook für Analytics-Query-Management
export function useAnalyticsQuery(initialQuery?: Partial<AnalyticsQuery>) {
  const [query, setQuery] = useState<AnalyticsQuery>(() => {
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    return {
      timeRange: {
        startDate: thirtyDaysAgo,
        endDate: now,
        period: 'monthly'
      },
      agentIds: [],
      ...initialQuery
    }
  })

  const updateTimeRange = useCallback((timeRange: Partial<AnalyticsQuery['timeRange']>) => {
    setQuery(prev => ({
      ...prev,
      timeRange: { ...prev.timeRange, ...timeRange }
    }))
  }, [])

  const updateAgentIds = useCallback((agentIds: string[]) => {
    setQuery(prev => ({ ...prev, agentIds }))
  }, [])

  const updateFilters = useCallback((filters: AnalyticsQuery['filters']) => {
    setQuery(prev => ({ ...prev, filters }))
  }, [])

  const setPresetRange = useCallback((preset: 'today' | 'yesterday' | 'last7days' | 'last30days' | 'last90days') => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    let startDate: Date
    let endDate: Date
    let period: AnalyticsQuery['timeRange']['period']

    switch (preset) {
      case 'today':
        startDate = today
        endDate = now
        period = 'daily'
        break
      case 'yesterday':
        startDate = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        endDate = today
        period = 'daily'
        break
      case 'last7days':
        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'weekly'
        break
      case 'last30days':
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'monthly'
        break
      case 'last90days':
        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
        endDate = now
        period = 'monthly'
        break
    }

    updateTimeRange({ startDate, endDate, period })
  }, [updateTimeRange])

  return {
    query,
    updateTimeRange,
    updateAgentIds,
    updateFilters,
    setPresetRange,
    setQuery
  }
}
