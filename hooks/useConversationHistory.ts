import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/lib/auth-context'
import { 
  ConversationHistoryItem, 
  ConversationHistoryResponse,
  ConversationFilters,
  DEFAULT_FILTERS,
  ConversationDetails
} from '@/types/history'

interface UseConversationHistoryOptions {
  initialFilters?: Partial<ConversationFilters>
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseConversationHistoryReturn {
  // Data
  conversations: ConversationHistoryItem[]
  total: number
  totalPages: number
  currentPage: number
  hasNext: boolean
  hasPrevious: boolean
  
  // State
  loading: boolean
  error: string | null
  filters: ConversationFilters
  
  // Actions
  setFilters: (filters: Partial<ConversationFilters>) => void
  resetFilters: () => void
  refresh: () => Promise<void>
  loadPage: (page: number) => Promise<void>
  
  // Conversation Details
  selectedConversation: ConversationDetails | null
  loadConversationDetails: (id: string) => Promise<void>
  clearSelectedConversation: () => void
  conversationDetailsLoading: boolean
  conversationDetailsError: string | null
}

export function useConversationHistory(
  options: UseConversationHistoryOptions = {}
): UseConversationHistoryReturn {
  const { 
    initialFilters = {}, 
    autoRefresh = false, 
    refreshInterval = 30000 
  } = options

  const { user, getAccessToken } = useAuth()
  
  // State
  const [conversations, setConversations] = useState<ConversationHistoryItem[]>([])
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFiltersState] = useState<ConversationFilters>({
    ...DEFAULT_FILTERS,
    ...initialFilters
  })

  // Conversation Details State
  const [selectedConversation, setSelectedConversation] = useState<ConversationDetails | null>(null)
  const [conversationDetailsLoading, setConversationDetailsLoading] = useState(false)
  const [conversationDetailsError, setConversationDetailsError] = useState<string | null>(null)

  const fetchConversations = useCallback(async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      setError(null)
      
      const accessToken = await getAccessToken()
      if (!accessToken) {
        throw new Error('Kein Access Token verfügbar')
      }

      // Build query parameters
      const params = new URLSearchParams()
      
      if (filters.search) params.append('search', filters.search)
      if (filters.agentIds.length > 0) params.append('agentIds', filters.agentIds.join(','))
      if (filters.status.length > 0) params.append('status', filters.status.join(','))
      if (filters.tags.length > 0) params.append('tags', filters.tags.join(','))
      
      params.append('sortBy', filters.sortBy)
      params.append('sortOrder', filters.sortOrder)
      params.append('page', filters.page.toString())
      params.append('pageSize', filters.pageSize.toString())

      // Date range
      if (filters.dateRange) {
        params.append('startDate', filters.dateRange.start.toISOString())
        params.append('endDate', filters.dateRange.end.toISOString())
      }

      // Duration range
      if (filters.durationRange.min > 0) {
        params.append('minDuration', filters.durationRange.min.toString())
      }
      if (filters.durationRange.max < Infinity) {
        params.append('maxDuration', filters.durationRange.max.toString())
      }

      const response = await fetch(`/api/history/conversations?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Fehler beim Laden der Gesprächshistorie')
      }

      const data: ConversationHistoryResponse = await response.json()
      
      // Convert date strings back to Date objects
      const conversationsWithDates = data.conversations.map(conv => ({
        ...conv,
        startTime: new Date(conv.startTime),
        endTime: new Date(conv.endTime)
      }))

      setConversations(conversationsWithDates)
      setTotal(data.total)
      setTotalPages(data.totalPages)
      setCurrentPage(data.currentPage)
      setHasNext(data.hasNext)
      setHasPrevious(data.hasPrevious)
    } catch (err) {
      console.error('Error fetching conversation history:', err)
      setError(err instanceof Error ? err.message : 'Ein unerwarteter Fehler ist aufgetreten')
    } finally {
      setLoading(false)
    }
  }, [user, getAccessToken, filters])

  const loadConversationDetails = useCallback(async (id: string) => {
    if (!user) return

    try {
      setConversationDetailsLoading(true)
      setConversationDetailsError(null)
      
      const accessToken = await getAccessToken()
      if (!accessToken) {
        throw new Error('Kein Access Token verfügbar')
      }

      const response = await fetch(`/api/history/conversations/${id}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Fehler beim Laden der Gesprächsdetails')
      }

      const data: ConversationDetails = await response.json()
      
      // Convert date strings back to Date objects
      const detailsWithDates: ConversationDetails = {
        ...data,
        startTime: new Date(data.startTime),
        endTime: new Date(data.endTime),
        transcript: data.transcript.map(entry => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        })),
        systemEvents: data.systemEvents.map(event => ({
          ...event,
          timestamp: new Date(event.timestamp)
        }))
      }

      setSelectedConversation(detailsWithDates)
    } catch (err) {
      console.error('Error fetching conversation details:', err)
      setConversationDetailsError(err instanceof Error ? err.message : 'Ein unerwarteter Fehler ist aufgetreten')
    } finally {
      setConversationDetailsLoading(false)
    }
  }, [user, getAccessToken])

  const setFilters = useCallback((newFilters: Partial<ConversationFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page !== undefined ? newFilters.page : 1 // Reset to page 1 when filters change
    }))
  }, [])

  const resetFilters = useCallback(() => {
    setFiltersState(DEFAULT_FILTERS)
  }, [])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchConversations()
  }, [fetchConversations])

  const loadPage = useCallback(async (page: number) => {
    setFilters({ page })
  }, [setFilters])

  const clearSelectedConversation = useCallback(() => {
    setSelectedConversation(null)
    setConversationDetailsError(null)
  }, [])

  // Initial load
  useEffect(() => {
    fetchConversations()
  }, [fetchConversations])

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh || !user) return

    const interval = setInterval(() => {
      if (!loading && user) {
        fetchConversations()
      }
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, user, fetchConversations, loading])

  return {
    // Data
    conversations,
    total,
    totalPages,
    currentPage,
    hasNext,
    hasPrevious,
    
    // State
    loading,
    error,
    filters,
    
    // Actions
    setFilters,
    resetFilters,
    refresh,
    loadPage,
    
    // Conversation Details
    selectedConversation,
    loadConversationDetails,
    clearSelectedConversation,
    conversationDetailsLoading,
    conversationDetailsError
  }
}
