'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/lib/auth-context'
import { 
  LiveConversation, 
  MonitoringFilters, 
  MonitoringApiResponse 
} from '@/types/monitoring'

interface UseLiveConversationsOptions {
  refreshInterval?: number
  autoRefresh?: boolean
}

interface UseLiveConversationsReturn {
  conversations: LiveConversation[]
  loading: boolean
  error: string | null
  filters: MonitoringFilters
  setFilters: (filters: Partial<MonitoringFilters>) => void
  refresh: () => Promise<void>
  total: number
  selectedConversation: LiveConversation | null
  setSelectedConversation: (conversation: LiveConversation | null) => void
}

const DEFAULT_FILTERS: MonitoringFilters = {
  search: '',
  status: 'all',
  agent: 'all',
  priority: 'all',
  department: 'all',
  sortBy: 'duration',
  sortOrder: 'desc'
}

export function useLiveConversations(options: UseLiveConversationsOptions = {}): UseLiveConversationsReturn {
  const { refreshInterval = 2000, autoRefresh = true } = options // 2 Sekunden für Live-Updates
  const { user, getAccessToken } = useAuth()
  
  const [conversations, setConversations] = useState<LiveConversation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFiltersState] = useState<MonitoringFilters>(DEFAULT_FILTERS)
  const [total, setTotal] = useState(0)
  const [selectedConversation, setSelectedConversation] = useState<LiveConversation | null>(null)

  const setFilters = useCallback((newFilters: Partial<MonitoringFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }))
  }, [])

  const fetchConversations = useCallback(async () => {
    if (!user) {
      // setError('Benutzer nicht authentifiziert')
      // setLoading(false)
      return
    }

    try {
      setError(null)
      
      const accessToken = await getAccessToken()
      if (!accessToken) {
        throw new Error('Kein Access Token verfügbar')
      }

      // Build query parameters
      const params = new URLSearchParams()
      if (filters.search) params.append('search', filters.search)
      if (filters.status !== 'all') params.append('status', filters.status)
      if (filters.agent !== 'all') params.append('agent', filters.agent)
      if (filters.priority !== 'all') params.append('priority', filters.priority)
      params.append('sortBy', filters.sortBy)
      params.append('sortOrder', filters.sortOrder)

      const response = await fetch(`/api/monitoring/conversations?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Fehler beim Laden der Gespräche')
      }

      const data: MonitoringApiResponse = await response.json()
      
      setConversations(data.conversations)
      setTotal(data.total)

      // Update selected conversation if it still exists
      if (selectedConversation) {
        const updatedSelected = data.conversations.find(c => c.id === selectedConversation.id)
        if (updatedSelected) {
          setSelectedConversation(updatedSelected)
        } else {
          // Conversation ended or no longer available
          setSelectedConversation(null)
        }
      }

    } catch (err) {
      console.error('Error fetching conversations:', err)
      setError(err instanceof Error ? err.message : 'Ein unerwarteter Fehler ist aufgetreten')
    } finally {
      setLoading(false)
    }
  }, [user, getAccessToken, filters, selectedConversation])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchConversations()
  }, [fetchConversations])

  // Initial load and filter changes
  useEffect(() => {
    if (user) {
      fetchConversations()
    }
  }, [fetchConversations, user])

  // Auto-refresh interval
  useEffect(() => {
    if (!autoRefresh || !user) return

    const interval = setInterval(() => {
      // Only refresh if not currently loading to avoid race conditions
      if (!loading && user) {
        fetchConversations()
      }
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, user, fetchConversations, loading])

  // Update conversation durations in real-time
  useEffect(() => {
    if (conversations.length === 0) return

    const interval = setInterval(() => {
      setConversations(prevConversations => 
        prevConversations.map(conv => ({
          ...conv,
          duration: Math.floor((Date.now() - conv.startTime.getTime()) / 1000)
        }))
      )

      // Also update selected conversation duration
      if (selectedConversation) {
        setSelectedConversation(prev => prev ? {
          ...prev,
          duration: Math.floor((Date.now() - prev.startTime.getTime()) / 1000)
        } : null)
      }
    }, 1000) // Update every second

    return () => clearInterval(interval)
  }, [conversations.length, selectedConversation])

  return {
    conversations,
    loading,
    error,
    filters,
    setFilters,
    refresh,
    total,
    selectedConversation,
    setSelectedConversation
  }
}
