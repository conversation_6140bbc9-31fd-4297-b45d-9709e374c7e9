// Report Generation Hooks für Story 4: Analytics-Reports und Metriken

import { useState, useCallback, useEffect } from 'react'
import { 
  ReportGenerationRequest,
  ReportGenerationResponse,
  ReportStatus,
  ScheduledReport
} from '@/types/analytics'
import axios from 'axios'

// Hook für Report-Generierung
export function useReportGeneration() {
  const [activeReports, setActiveReports] = useState<Map<string, ReportGenerationResponse>>(new Map())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Report generieren
  const generateReport = useCallback(async (request: ReportGenerationRequest): Promise<string | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await axios.post('/api/reports/generate', request)
      const reportResponse: ReportGenerationResponse = response.data

      // Report zur aktiven Liste hinzufügen
      setActiveReports(prev => new Map(prev.set(reportResponse.reportId, reportResponse)))

      return reportResponse.reportId
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Fehler bei der Report-Generierung'
      setError(errorMessage)
      console.error('Fehler bei der Report-Generierung:', err)
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Report-Status abrufen
  const getReportStatus = useCallback(async (reportId: string): Promise<ReportStatus | null> => {
    try {
      const response = await axios.get(`/api/reports/${reportId}/status`)
      const status: ReportStatus = response.data

      // Aktive Reports aktualisieren
      if (activeReports.has(reportId)) {
        setActiveReports(prev => {
          const updated = new Map(prev)
          const current = updated.get(reportId)
          if (current) {
            updated.set(reportId, {
              ...current,
              status: status.status,
              progress: status.progress,
              downloadUrl: status.downloadUrl,
              error: status.error
            })
          }
          return updated
        })
      }

      return status
    } catch (err: any) {
      console.error('Fehler beim Abrufen des Report-Status:', err)
      return null
    }
  }, [activeReports])

  // Report herunterladen
  const downloadReport = useCallback(async (reportId: string): Promise<boolean> => {
    try {
      const response = await axios.get(`/api/reports/${reportId}/download`, {
        responseType: 'blob'
      })

      // Dateiname aus Content-Disposition Header extrahieren
      const contentDisposition = response.headers['content-disposition']
      let filename = `report_${reportId}.pdf`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }

      // Download starten
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return true
    } catch (err: any) {
      console.error('Fehler beim Report-Download:', err)
      setError(err.response?.data?.error || 'Fehler beim Report-Download')
      return false
    }
  }, [])

  // Report aus aktiver Liste entfernen
  const removeActiveReport = useCallback((reportId: string) => {
    setActiveReports(prev => {
      const updated = new Map(prev)
      updated.delete(reportId)
      return updated
    })
  }, [])

  return {
    activeReports: Array.from(activeReports.values()),
    loading,
    error,
    generateReport,
    getReportStatus,
    downloadReport,
    removeActiveReport,
    clearError: () => setError(null)
  }
}

// Hook für Report-Status-Polling
export function useReportStatusPolling(reportId: string | null, interval: number = 2000) {
  const [status, setStatus] = useState<ReportStatus | null>(null)
  const [polling, setPolling] = useState(false)

  const { getReportStatus } = useReportGeneration()

  useEffect(() => {
    if (!reportId) {
      setStatus(null)
      setPolling(false)
      return
    }

    setPolling(true)
    
    const pollStatus = async () => {
      const currentStatus = await getReportStatus(reportId)
      if (currentStatus) {
        setStatus(currentStatus)
        
        // Polling stoppen wenn Report fertig oder fehlgeschlagen
        if (currentStatus.status === 'completed' || currentStatus.status === 'failed') {
          setPolling(false)
        }
      }
    }

    // Sofort einmal ausführen
    pollStatus()

    // Polling nur starten wenn Report noch nicht fertig
    const intervalId = setInterval(async () => {
      const currentStatus = await getReportStatus(reportId)
      if (currentStatus) {
        setStatus(currentStatus)
        
        if (currentStatus.status === 'completed' || currentStatus.status === 'failed') {
          clearInterval(intervalId)
          setPolling(false)
        }
      }
    }, interval)

    return () => {
      clearInterval(intervalId)
      setPolling(false)
    }
  }, [reportId, interval, getReportStatus])

  return { status, polling }
}

// Hook für geplante Reports
export function useScheduledReports() {
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Geplante Reports laden
  const fetchScheduledReports = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await axios.get('/api/reports/scheduled')
      setScheduledReports(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der geplanten Reports')
      console.error('Fehler beim Laden der geplanten Reports:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Geplanten Report erstellen
  const createScheduledReport = useCallback(async (
    reportData: Omit<ScheduledReport, 'id' | 'createdAt' | 'updatedAt' | 'nextRun'>
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await axios.post('/api/reports/scheduled', reportData)
      const newReport: ScheduledReport = response.data
      
      setScheduledReports(prev => [...prev, newReport])
      return true
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Erstellen des geplanten Reports')
      console.error('Fehler beim Erstellen des geplanten Reports:', err)
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  // Geplanten Report aktualisieren
  const updateScheduledReport = useCallback(async (
    id: string, 
    updates: Partial<ScheduledReport>
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await axios.patch(`/api/reports/scheduled/${id}`, updates)
      const updatedReport: ScheduledReport = response.data
      
      setScheduledReports(prev => 
        prev.map(report => report.id === id ? updatedReport : report)
      )
      return true
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Aktualisieren des geplanten Reports')
      console.error('Fehler beim Aktualisieren des geplanten Reports:', err)
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  // Geplanten Report löschen
  const deleteScheduledReport = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      await axios.delete(`/api/reports/scheduled/${id}`)
      setScheduledReports(prev => prev.filter(report => report.id !== id))
      return true
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Löschen des geplanten Reports')
      console.error('Fehler beim Löschen des geplanten Reports:', err)
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  // Geplanten Report aktivieren/deaktivieren
  const toggleScheduledReport = useCallback(async (id: string, enabled: boolean): Promise<boolean> => {
    return updateScheduledReport(id, { enabled })
  }, [updateScheduledReport])

  // Initial laden
  useEffect(() => {
    fetchScheduledReports()
  }, [fetchScheduledReports])

  return {
    scheduledReports,
    loading,
    error,
    fetchScheduledReports,
    createScheduledReport,
    updateScheduledReport,
    deleteScheduledReport,
    toggleScheduledReport,
    clearError: () => setError(null)
  }
}

// Hook für Report-Vorlagen
export function useReportTemplates() {
  const [templates, setTemplates] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTemplates = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await axios.get('/api/reports/templates')
      setTemplates(response.data)
    } catch (err: any) {
      setError(err.response?.data?.error || 'Fehler beim Laden der Report-Vorlagen')
      console.error('Fehler beim Laden der Report-Vorlagen:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchTemplates()
  }, [fetchTemplates])

  return {
    templates,
    loading,
    error,
    refetch: fetchTemplates,
    clearError: () => setError(null)
  }
}
