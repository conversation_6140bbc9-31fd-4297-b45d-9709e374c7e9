'use client'

import { useState, useCallback } from 'react'
import { useAuth } from '@/lib/auth-context'
import { TakeoverRequest, TakeoverResponse } from '@/types/monitoring'

interface UseTakeoverOptions {
  onSuccess?: (response: TakeoverResponse) => void
  onError?: (error: string) => void
}

interface UseTakeoverReturn {
  takeover: (request: Omit<TakeoverRequest, 'supervisorId' | 'timestamp'>) => Promise<TakeoverResponse>
  loading: boolean
  error: string | null
  lastResponse: TakeoverResponse | null
}

export function useTakeover(options: UseTakeoverOptions = {}): UseTakeoverReturn {
  const { onSuccess, onError } = options
  const { user, getAccessToken } = useAuth()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastResponse, setLastResponse] = useState<TakeoverResponse | null>(null)

  const takeover = useCallback(async (
    request: Omit<TakeoverRequest, 'supervisorId' | 'timestamp'>
  ): Promise<TakeoverResponse> => {
    if (!user) {
      const errorMsg = 'Benutzer nicht authentifiziert'
      setError(errorMsg)
      if (onError) onError(errorMsg)
      throw new Error(errorMsg)
    }

    setLoading(true)
    setError(null)

    try {
      const accessToken = await getAccessToken()
      if (!accessToken) {
        throw new Error('Kein Access Token verfügbar')
      }

      const response = await fetch('/api/monitoring/takeover', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Fehler bei der Gesprächsübernahme')
      }

      const data: TakeoverResponse = await response.json()
      
      setLastResponse(data)
      
      if (onSuccess) {
        onSuccess(data)
      }

      return data

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ein unerwarteter Fehler ist aufgetreten'
      console.error('Takeover error:', err)
      setError(errorMessage)
      
      if (onError) {
        onError(errorMessage)
      }
      
      throw err
    } finally {
      setLoading(false)
    }
  }, [user, getAccessToken, onSuccess, onError])

  return {
    takeover,
    loading,
    error,
    lastResponse
  }
}
