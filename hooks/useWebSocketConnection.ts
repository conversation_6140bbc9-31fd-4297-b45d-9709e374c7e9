'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { ConnectionStatus, WebSocketEvents, WebSocketCommands } from '@/types/dashboard'

interface UseWebSocketConnectionOptions {
  url?: string
  maxReconnectAttempts?: number
  reconnectInterval?: number
  enabled?: boolean
}

interface UseWebSocketConnectionReturn {
  connectionStatus: ConnectionStatus
  isConnected: boolean
  lastConnected?: Date
  sendMessage: <T extends keyof WebSocketCommands>(
    event: T,
    data: WebSocketCommands[T]
  ) => void
  subscribe: <T extends keyof WebSocketEvents>(
    event: T,
    callback: (data: WebSocketEvents[T]) => void
  ) => () => void
  connect: () => void
  disconnect: () => void
}

export function useWebSocketConnection(
  options: UseWebSocketConnectionOptions = {}
): UseWebSocketConnectionReturn {
  const {
    url = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001/ws/dashboard',
    maxReconnectAttempts = 5,
    reconnectInterval = 3000,
    enabled = true
  } = options

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    connected: false,
    reconnectAttempts: 0
  })

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const eventListenersRef = useRef<Map<string, Set<Function>>>(new Map())

  const connect = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const ws = new WebSocket(url)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('WebSocket connected')
        setConnectionStatus(prev => ({
          ...prev,
          connected: true,
          lastConnected: new Date(),
          reconnectAttempts: 0,
          error: undefined
        }))
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          const { type, data } = message

          // Trigger event listeners
          const listeners = eventListenersRef.current.get(type)
          if (listeners) {
            listeners.forEach(callback => {
              try {
                callback(data)
              } catch (error) {
                console.error('Error in WebSocket event listener:', error)
              }
            })
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setConnectionStatus(prev => ({
          ...prev,
          connected: false,
          error: event.reason || 'Verbindung getrennt'
        }))

        // Auto-reconnect if not manually closed
        if (event.code !== 1000 && enabled) {
          attemptReconnect()
        }
      }

      ws.onerror = (event) => {
        console.error('WebSocket error:', event)
        setConnectionStatus(prev => ({
          ...prev,
          connected: false,
          error: 'Verbindungsfehler'
        }))
      }
    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
      setConnectionStatus(prev => ({
        ...prev,
        connected: false,
        error: 'Fehler beim Verbindungsaufbau'
      }))
    }
  }, [url, enabled])

  const attemptReconnect = useCallback(() => {
    setConnectionStatus(prev => {
      if (prev.reconnectAttempts >= maxReconnectAttempts) {
        return {
          ...prev,
          error: 'Maximale Anzahl Verbindungsversuche erreicht'
        }
      }

      const newAttempts = prev.reconnectAttempts + 1
      console.log(`Reconnect attempt ${newAttempts}/${maxReconnectAttempts}`)

      reconnectTimeoutRef.current = setTimeout(() => {
        connect()
      }, reconnectInterval)

      return {
        ...prev,
        reconnectAttempts: newAttempts
      }
    })
  }, [maxReconnectAttempts, reconnectInterval, connect])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }

    setConnectionStatus({
      connected: false,
      reconnectAttempts: 0
    })
  }, [])

  const sendMessage = useCallback(<T extends keyof WebSocketCommands>(
    event: T,
    data: WebSocketCommands[T]
  ) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({ type: event, data })
      wsRef.current.send(message)
    } else {
      console.warn('WebSocket not connected, cannot send message:', event)
    }
  }, [])

  const subscribe = useCallback(<T extends keyof WebSocketEvents>(
    event: T,
    callback: (data: WebSocketEvents[T]) => void
  ) => {
    if (!eventListenersRef.current.has(event)) {
      eventListenersRef.current.set(event, new Set())
    }
    
    const listeners = eventListenersRef.current.get(event)!
    listeners.add(callback)

    // Return unsubscribe function
    return () => {
      listeners.delete(callback)
      if (listeners.size === 0) {
        eventListenersRef.current.delete(event)
      }
    }
  }, [])

  // Connect on mount if enabled
  useEffect(() => {
    if (enabled) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    connectionStatus,
    isConnected: connectionStatus.connected,
    lastConnected: connectionStatus.lastConnected,
    sendMessage,
    subscribe,
    connect,
    disconnect
  }
}
