export interface Voice {
  id: string;
  name: string;
  language: 'de-DE' | 'en-US';
  gender: 'male' | 'female';
  description: string;
}

export const AVAILABLE_VOICES: Voice[] = [
  // Deutsche Stimmen
  {
    id: 'de-DE-KatjaNeural',
    name: '<PERSON><PERSON>',
    language: 'de-DE',
    gender: 'female',
    description: 'Freundliche weibliche deutsche Stimme'
  },
  {
    id: 'de-DE-ConradNeural',
    name: '<PERSON>',
    language: 'de-DE',
    gender: 'male',
    description: 'Professionelle männliche deutsche Stimme'
  },
  {
    id: 'de-DE-AmalaNeural',
    name: '<PERSON><PERSON>',
    language: 'de-DE',
    gender: 'female',
    description: 'Warme weibliche deutsche Stimme'
  },
  {
    id: 'de-DE-BerndNeural',
    name: '<PERSON><PERSON>',
    language: 'de-DE',
    gender: 'male',
    description: 'Klare männliche deutsche Stimme'
  },
  
  // Englische Stimmen
  {
    id: 'en-US-JennyNeural',
    name: '<PERSON>',
    language: 'en-US',
    gender: 'female',
    description: 'Natural female American voice'
  },
  {
    id: 'en-US-GuyNeural',
    name: '<PERSON>',
    language: 'en-US',
    gender: 'male',
    description: 'Professional male American voice'
  },
  {
    id: 'en-US-AriaNeural',
    name: 'Aria',
    language: 'en-US',
    gender: 'female',
    description: 'Friendly female American voice'
  },
  {
    id: 'en-US-DavisNeural',
    name: 'Davis',
    language: 'en-US',
    gender: 'male',
    description: 'Clear male American voice'
  }
];

export const getVoicesByLanguage = (language: 'de-DE' | 'en-US'): Voice[] => {
  return AVAILABLE_VOICES.filter(voice => voice.language === language);
};

export const getVoiceById = (id: string): Voice | undefined => {
  return AVAILABLE_VOICES.find(voice => voice.id === id);
};

export const DEFAULT_VOICES = {
  'de-DE': 'de-DE-KatjaNeural',
  'en-US': 'en-US-JennyNeural'
} as const;
