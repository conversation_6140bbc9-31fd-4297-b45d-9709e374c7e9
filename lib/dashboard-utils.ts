import { AgentDashboardData, TrendDirection, TrendIndicator } from '@/types/dashboard'
import { MetricConfig } from '@/types/metrics'

/**
 * Formatiert eine <PERSON>uer in Sekunden zu einem lesbaren String
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`
  }
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.round(seconds % 60)
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  }
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
}

/**
 * Formatiert einen Prozentsatz mit der gewünschten Genauigkeit
 */
export function formatPercentage(value: number, precision: number = 1): string {
  return `${value.toFixed(precision)}%`
}

/**
 * Formatiert eine Zahl mit Tausendertrennzeichen
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('de-DE').format(value)
}

/**
 * Berechnet die Trend-Richtung basierend auf aktuellen und vorherigen Werten
 */
export function calculateTrend(current: number, previous: number): TrendIndicator {
  if (previous === 0) {
    return {
      value: current,
      direction: current > 0 ? 'up' : 'stable',
      percentage: 0
    }
  }
  
  const change = current - previous
  const percentage = (change / previous) * 100
  
  let direction: TrendDirection = 'stable'
  if (Math.abs(percentage) > 1) { // Nur signifikante Änderungen anzeigen
    direction = percentage > 0 ? 'up' : 'down'
  }
  
  return {
    value: current,
    direction,
    percentage: Math.abs(percentage)
  }
}

/**
 * Gibt die CSS-Klasse für den Agent-Status zurück
 */
export function getStatusColor(status: AgentDashboardData['status']): string {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'idle':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'error':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'inactive':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * Gibt die deutsche Übersetzung für den Agent-Status zurück
 */
export function getStatusLabel(status: AgentDashboardData['status']): string {
  switch (status) {
    case 'active':
      return 'Aktiv'
    case 'idle':
      return 'Bereit'
    case 'error':
      return 'Fehler'
    case 'inactive':
      return 'Inaktiv'
    default:
      return 'Unbekannt'
  }
}

/**
 * Gibt das Icon für den Agent-Status zurück
 */
export function getStatusIcon(status: AgentDashboardData['status']): string {
  switch (status) {
    case 'active':
      return '🟢'
    case 'idle':
      return '🟡'
    case 'error':
      return '🔴'
    case 'inactive':
      return '⚫'
    default:
      return '❓'
  }
}

/**
 * Formatiert die letzte Aktivität zu einem relativen Zeitstring
 */
export function formatLastActivity(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (diffMinutes < 1) {
    return 'Gerade eben'
  } else if (diffMinutes < 60) {
    return `vor ${diffMinutes} Min`
  } else if (diffMinutes < 1440) { // 24 hours
    const hours = Math.floor(diffMinutes / 60)
    return `vor ${hours} Std`
  } else {
    const days = Math.floor(diffMinutes / 1440)
    return `vor ${days} Tag${days > 1 ? 'en' : ''}`
  }
}

/**
 * Gibt das Trend-Pfeil-Symbol zurück
 */
export function getTrendArrow(direction: TrendDirection): string {
  switch (direction) {
    case 'up':
      return '↗'
    case 'down':
      return '↘'
    case 'stable':
      return '→'
    default:
      return '→'
  }
}

/**
 * Gibt die CSS-Klasse für die Trend-Farbe zurück
 */
export function getTrendColor(direction: TrendDirection, isPositiveTrend: boolean = true): string {
  if (direction === 'stable') {
    return 'text-gray-500'
  }
  
  const isGoodTrend = isPositiveTrend ? direction === 'up' : direction === 'down'
  return isGoodTrend ? 'text-green-600' : 'text-red-600'
}

/**
 * Sortiert Agenten basierend auf dem gewählten Kriterium
 */
export function sortAgents(
  agents: AgentDashboardData[],
  sortBy: string,
  sortOrder: 'asc' | 'desc'
): AgentDashboardData[] {
  return [...agents].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'status':
        // Sortierung: active > idle > error > inactive
        const statusOrder = { active: 0, idle: 1, error: 2, inactive: 3 }
        aValue = statusOrder[a.status]
        bValue = statusOrder[b.status]
        break
      case 'activity':
        aValue = a.lastActivity.getTime()
        bValue = b.lastActivity.getTime()
        break
      case 'performance':
        aValue = a.todayMetrics.successRate
        bValue = b.todayMetrics.successRate
        break
      default:
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
    }

    if (sortOrder === 'desc') {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })
}

/**
 * Filtert Agenten basierend auf Suchkriterien
 */
export function filterAgents(
  agents: AgentDashboardData[],
  search: string,
  status: string
): AgentDashboardData[] {
  let filtered = agents

  // Text-Suche
  if (search.trim()) {
    const searchLower = search.toLowerCase()
    filtered = filtered.filter(agent =>
      agent.name.toLowerCase().includes(searchLower) ||
      agent.id.toLowerCase().includes(searchLower)
    )
  }

  // Status-Filter
  if (status !== 'all') {
    filtered = filtered.filter(agent => agent.status === status)
  }

  return filtered
}

/**
 * Berechnet die Gesamtstatistiken für eine Liste von Agenten
 */
export function calculateSummaryStats(agents: AgentDashboardData[]) {
  const totalAgents = agents.length
  const activeAgents = agents.filter(a => a.status === 'active').length
  const totalCalls = agents.reduce((sum, agent) => sum + agent.todayMetrics.totalCalls, 0)
  const totalSuccessful = agents.reduce((sum, agent) => sum + agent.todayMetrics.successfulCalls, 0)
  const averageSuccessRate = totalCalls > 0 ? (totalSuccessful / totalCalls) * 100 : 0
  const averageUtilization = totalAgents > 0 
    ? agents.reduce((sum, agent) => sum + agent.todayMetrics.utilization, 0) / totalAgents 
    : 0

  return {
    totalAgents,
    activeAgents,
    totalCalls,
    averageSuccessRate,
    averageUtilization
  }
}
