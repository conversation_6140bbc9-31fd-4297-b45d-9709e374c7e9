import { 
  ConversationHistoryItem, 
  ConversationStatus,
  ConversationPriority,
  TranscriptEntry,
  SystemEvent,
  STATUS_LABELS,
  STATUS_COLORS,
  PRIORITY_LABELS,
  PRIORITY_COLORS,
  SENTIMENT_LABELS,
  SENTIMENT_COLORS,
  SENTIMENT_EMOJIS,
  SYSTEM_EVENT_LABELS,
  SYSTEM_EVENT_ICONS,
  RATING_LABELS,
  RESOLUTION_STATUS_LABELS,
  RESOLUTION_STATUS_COLORS
} from '@/types/history'

/**
 * Formatiert e<PERSON>uer in Sekunden zu einem lesbaren String
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
  }
}

/**
 * Formatiert ein Datum zu einem lesbaren String
 */
export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('de-DE', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

/**
 * Formatiert ein Datum zu einem kurzen String (nur Datum)
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('de-DE', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date)
}

/**
 * Formatiert eine Zeit zu einem kurzen String (nur Zeit)
 */
export function formatTime(date: Date): string {
  return new Intl.DateTimeFormat('de-DE', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

/**
 * Gibt das Label für einen Gesprächsstatus zurück
 */
export function getStatusLabel(status: ConversationStatus): string {
  return STATUS_LABELS[status] || status
}

/**
 * Gibt die CSS-Klassen für einen Gesprächsstatus zurück
 */
export function getStatusColor(status: ConversationStatus): string {
  return STATUS_COLORS[status] || 'bg-gray-100 text-gray-800 border-gray-200'
}

/**
 * Gibt das Label für eine Priorität zurück
 */
export function getPriorityLabel(priority: ConversationPriority): string {
  return PRIORITY_LABELS[priority] || priority
}

/**
 * Gibt die CSS-Klassen für eine Priorität zurück
 */
export function getPriorityColor(priority: ConversationPriority): string {
  return PRIORITY_COLORS[priority] || 'bg-gray-100 text-gray-800'
}

/**
 * Gibt das Label für ein Sentiment zurück
 */
export function getSentimentLabel(sentiment: 'positive' | 'neutral' | 'negative'): string {
  return SENTIMENT_LABELS[sentiment] || sentiment
}

/**
 * Gibt die CSS-Klassen für ein Sentiment zurück
 */
export function getSentimentColor(sentiment: 'positive' | 'neutral' | 'negative'): string {
  return SENTIMENT_COLORS[sentiment] || 'text-gray-600'
}

/**
 * Gibt das Emoji für ein Sentiment zurück
 */
export function getSentimentEmoji(sentiment: 'positive' | 'neutral' | 'negative'): string {
  return SENTIMENT_EMOJIS[sentiment] || '😐'
}

/**
 * Gibt das Label für einen System-Event-Typ zurück
 */
export function getSystemEventLabel(type: SystemEvent['type']): string {
  return SYSTEM_EVENT_LABELS[type] || type
}

/**
 * Gibt das Icon für einen System-Event-Typ zurück
 */
export function getSystemEventIcon(type: SystemEvent['type']): string {
  return SYSTEM_EVENT_ICONS[type] || '📋'
}

/**
 * Gibt das Label für eine Bewertung zurück
 */
export function getRatingLabel(rating: number): string {
  return RATING_LABELS[rating as keyof typeof RATING_LABELS] || `${rating} Sterne`
}

/**
 * Gibt das Label für einen Resolution-Status zurück
 */
export function getResolutionStatusLabel(status: 'resolved' | 'unresolved' | 'escalated'): string {
  return RESOLUTION_STATUS_LABELS[status] || status
}

/**
 * Gibt die CSS-Klassen für einen Resolution-Status zurück
 */
export function getResolutionStatusColor(status: 'resolved' | 'unresolved' | 'escalated'): string {
  return RESOLUTION_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800'
}

/**
 * Berechnet die durchschnittliche Bewertung einer Liste von Gesprächen
 */
export function calculateAverageRating(conversations: ConversationHistoryItem[]): number {
  const ratedConversations = conversations.filter(conv => conv.rating !== undefined)
  if (ratedConversations.length === 0) return 0
  
  const totalRating = ratedConversations.reduce((sum, conv) => sum + (conv.rating || 0), 0)
  return Math.round((totalRating / ratedConversations.length) * 10) / 10
}

/**
 * Berechnet die durchschnittliche Gesprächsdauer einer Liste von Gesprächen
 */
export function calculateAverageDuration(conversations: ConversationHistoryItem[]): number {
  if (conversations.length === 0) return 0
  
  const totalDuration = conversations.reduce((sum, conv) => sum + conv.duration, 0)
  return Math.round(totalDuration / conversations.length)
}

/**
 * Gruppiert Gespräche nach Status
 */
export function groupConversationsByStatus(conversations: ConversationHistoryItem[]): Record<ConversationStatus, number> {
  return conversations.reduce((acc, conv) => {
    acc[conv.status] = (acc[conv.status] || 0) + 1
    return acc
  }, {} as Record<ConversationStatus, number>)
}

/**
 * Gruppiert Gespräche nach Agent
 */
export function groupConversationsByAgent(conversations: ConversationHistoryItem[]): Record<string, ConversationHistoryItem[]> {
  return conversations.reduce((acc, conv) => {
    if (!acc[conv.agentName]) {
      acc[conv.agentName] = []
    }
    acc[conv.agentName].push(conv)
    return acc
  }, {} as Record<string, ConversationHistoryItem[]>)
}

/**
 * Filtert Gespräche nach einem Suchbegriff
 */
export function filterConversationsBySearch(
  conversations: ConversationHistoryItem[], 
  searchTerm: string
): ConversationHistoryItem[] {
  if (!searchTerm.trim()) return conversations
  
  const term = searchTerm.toLowerCase()
  return conversations.filter(conv => 
    conv.agentName.toLowerCase().includes(term) ||
    conv.callerInfo?.name?.toLowerCase().includes(term) ||
    conv.callerInfo?.phone?.includes(term) ||
    conv.summary?.toLowerCase().includes(term) ||
    conv.tags.some(tag => tag.toLowerCase().includes(term))
  )
}

/**
 * Sortiert Gespräche nach einem bestimmten Feld
 */
export function sortConversations(
  conversations: ConversationHistoryItem[],
  sortBy: 'startTime' | 'duration' | 'agentName' | 'rating',
  sortOrder: 'asc' | 'desc' = 'desc'
): ConversationHistoryItem[] {
  return [...conversations].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'startTime':
        aValue = a.startTime.getTime()
        bValue = b.startTime.getTime()
        break
      case 'duration':
        aValue = a.duration
        bValue = b.duration
        break
      case 'agentName':
        aValue = a.agentName.toLowerCase()
        bValue = b.agentName.toLowerCase()
        break
      case 'rating':
        aValue = a.rating || 0
        bValue = b.rating || 0
        break
      default:
        aValue = a.startTime.getTime()
        bValue = b.startTime.getTime()
    }

    if (sortOrder === 'desc') {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })
}

/**
 * Extrahiert alle einzigartigen Tags aus einer Liste von Gesprächen
 */
export function extractUniqueTags(conversations: ConversationHistoryItem[]): string[] {
  const allTags = conversations.flatMap(conv => conv.tags)
  return [...new Set(allTags)].sort()
}

/**
 * Extrahiert alle einzigartigen Agenten aus einer Liste von Gesprächen
 */
export function extractUniqueAgents(conversations: ConversationHistoryItem[]): Array<{id: string, name: string}> {
  const agentMap = new Map<string, string>()
  
  conversations.forEach(conv => {
    agentMap.set(conv.agentId, conv.agentName)
  })
  
  return Array.from(agentMap.entries()).map(([id, name]) => ({ id, name })).sort((a, b) => a.name.localeCompare(b.name))
}

/**
 * Berechnet Statistiken für eine Liste von Gesprächen
 */
export function calculateConversationStats(conversations: ConversationHistoryItem[]) {
  const total = conversations.length
  const statusDistribution = groupConversationsByStatus(conversations)
  const averageDuration = calculateAverageDuration(conversations)
  const averageRating = calculateAverageRating(conversations)
  
  const completedConversations = conversations.filter(conv => conv.status === 'completed')
  const successRate = total > 0 ? Math.round((completedConversations.length / total) * 100) : 0
  
  const ratedConversations = conversations.filter(conv => conv.rating !== undefined)
  const ratingDistribution = ratedConversations.reduce((acc, conv) => {
    const rating = conv.rating!
    acc[rating] = (acc[rating] || 0) + 1
    return acc
  }, {} as Record<number, number>)
  
  return {
    total,
    statusDistribution,
    averageDuration,
    averageRating,
    successRate,
    ratingDistribution,
    totalRated: ratedConversations.length
  }
}

/**
 * Formatiert eine Dateigröße in Bytes zu einem lesbaren String
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Generiert eine Farbe basierend auf einem String (für Agent-Avatare)
 */
export function generateColorFromString(str: string): string {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const hue = hash % 360
  return `hsl(${hue}, 70%, 50%)`
}

/**
 * Extrahiert Initialen aus einem Namen
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

/**
 * Prüft ob ein Datum heute ist
 */
export function isToday(date: Date): boolean {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

/**
 * Prüft ob ein Datum gestern war
 */
export function isYesterday(date: Date): boolean {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return date.toDateString() === yesterday.toDateString()
}

/**
 * Formatiert ein Datum relativ (heute, gestern, oder Datum)
 */
export function formatRelativeDate(date: Date): string {
  if (isToday(date)) {
    return 'Heute'
  } else if (isYesterday(date)) {
    return 'Gestern'
  } else {
    return formatDate(date)
  }
}

/**
 * Debounce-Funktion für Suchfunktionalität
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Validiert eine Telefonnummer (einfache deutsche Validierung)
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^(\+49|0)[1-9]\d{1,14}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

/**
 * Formatiert eine Telefonnummer für die Anzeige
 */
export function formatPhoneNumber(phone: string): string {
  // Einfache Formatierung für deutsche Nummern
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.startsWith('49')) {
    return `+49 ${cleaned.slice(2, 4)} ${cleaned.slice(4)}`
  } else if (cleaned.startsWith('0')) {
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`
  }
  
  return phone
}
