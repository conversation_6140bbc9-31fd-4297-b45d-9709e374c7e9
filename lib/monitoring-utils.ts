import { 
  LiveConversation, 
  TranscriptEntry,
  CONVERSATION_STATUS_COLORS,
  CONVERSATION_STATUS_LABELS,
  PRIORITY_COLORS,
  PRIORITY_LABELS,
  SENTIMENT_EMOJIS,
  SENTIMENT_COLORS,
  EVENT_TYPE_ICONS,
  TAKEOVER_REASONS
} from '@/types/monitoring'

/**
 * Formatiert eine Dauer in Sekunden zu einem lesbaren String (MM:SS oder HH:MM:SS)
 */
export function formatConversationDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}

/**
 * Formatiert einen Zeitstempel zu einem lesbaren String (HH:MM:SS)
 */
export function formatTimestamp(date: Date): string {
  return date.toLocaleTimeString('de-DE', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * Formatiert die letzte Aktivität zu einem relativen Zeitstring
 */
export function formatLastActivity(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)

  if (diffSeconds < 10) {
    return 'Gerade eben'
  } else if (diffSeconds < 60) {
    return `vor ${diffSeconds}s`
  } else if (diffSeconds < 3600) {
    const minutes = Math.floor(diffSeconds / 60)
    return `vor ${minutes}m`
  } else {
    const hours = Math.floor(diffSeconds / 3600)
    return `vor ${hours}h`
  }
}

/**
 * Gibt die CSS-Klasse für den Gesprächsstatus zurück
 */
export function getConversationStatusColor(status: LiveConversation['status']): string {
  return CONVERSATION_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800 border-gray-200'
}

/**
 * Gibt die deutsche Übersetzung für den Gesprächsstatus zurück
 */
export function getConversationStatusLabel(status: LiveConversation['status']): string {
  return CONVERSATION_STATUS_LABELS[status] || 'Unbekannt'
}

/**
 * Gibt die CSS-Klasse für die Priorität zurück
 */
export function getPriorityColor(priority: LiveConversation['metadata']['priority']): string {
  return PRIORITY_COLORS[priority] || 'bg-gray-100 text-gray-800'
}

/**
 * Gibt die deutsche Übersetzung für die Priorität zurück
 */
export function getPriorityLabel(priority: LiveConversation['metadata']['priority']): string {
  return PRIORITY_LABELS[priority] || 'Unbekannt'
}

/**
 * Gibt das Emoji für das Sentiment zurück
 */
export function getSentimentEmoji(sentiment: TranscriptEntry['sentiment']): string {
  if (!sentiment) return ''
  return SENTIMENT_EMOJIS[sentiment] || ''
}

/**
 * Gibt die CSS-Klasse für die Sentiment-Farbe zurück
 */
export function getSentimentColor(sentiment: TranscriptEntry['sentiment']): string {
  if (!sentiment) return 'text-gray-600'
  return SENTIMENT_COLORS[sentiment] || 'text-gray-600'
}

/**
 * Gibt das Icon für den Event-Type zurück
 */
export function getEventTypeIcon(eventType: TranscriptEntry['eventType']): string {
  return EVENT_TYPE_ICONS[eventType] || '📝'
}

/**
 * Gibt die deutsche Übersetzung für den Übernahmegrund zurück
 */
export function getTakeoverReasonLabel(reason: keyof typeof TAKEOVER_REASONS): string {
  return TAKEOVER_REASONS[reason] || 'Unbekannt'
}

/**
 * Berechnet die Verbindungsqualität als Sterne-String
 */
export function formatConnectionQuality(quality: number): string {
  const stars = '★'.repeat(quality) + '☆'.repeat(5 - quality)
  return stars
}

/**
 * Formatiert die Latenz mit Einheit
 */
export function formatLatency(latency: number): string {
  return `${latency}ms`
}

/**
 * Formatiert den Audio-Level als Prozentsatz
 */
export function formatAudioLevel(level: number): string {
  return `${level}%`
}

/**
 * Gibt die CSS-Klasse für die Audio-Level-Anzeige zurück
 */
export function getAudioLevelColor(level: number): string {
  if (level === 0) return 'bg-gray-300'
  if (level < 30) return 'bg-red-400'
  if (level < 70) return 'bg-yellow-400'
  return 'bg-green-400'
}

/**
 * Prüft ob ein Gespräch eine Warnung benötigt (lange Stille)
 */
export function shouldShowSilenceWarning(conversation: LiveConversation): boolean {
  const now = new Date()
  const timeSinceLastActivity = now.getTime() - conversation.lastActivity.getTime()
  const silenceThreshold = 30 * 1000 // 30 Sekunden

  return conversation.status === 'active' && timeSinceLastActivity > silenceThreshold
}

/**
 * Prüft ob ein Gespräch eine Qualitätswarnung benötigt
 */
export function shouldShowQualityWarning(conversation: LiveConversation): boolean {
  return conversation.qualityMetrics.connectionQuality <= 2 || 
         conversation.qualityMetrics.latency > 500
}

/**
 * Sortiert Gespräche basierend auf dem gewählten Kriterium
 */
export function sortConversations(
  conversations: LiveConversation[],
  sortBy: string,
  sortOrder: 'asc' | 'desc'
): LiveConversation[] {
  return [...conversations].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy) {
      case 'duration':
        aValue = a.duration
        bValue = b.duration
        break
      case 'startTime':
        aValue = a.startTime.getTime()
        bValue = b.startTime.getTime()
        break
      case 'agentName':
        aValue = a.agentName.toLowerCase()
        bValue = b.agentName.toLowerCase()
        break
      case 'priority':
        const priorityOrder = { low: 0, medium: 1, high: 2 }
        aValue = priorityOrder[a.metadata.priority as keyof typeof priorityOrder]
        bValue = priorityOrder[b.metadata.priority as keyof typeof priorityOrder]
        break
      default:
        aValue = a.duration
        bValue = b.duration
    }

    if (sortOrder === 'desc') {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })
}

/**
 * Filtert Gespräche basierend auf Suchkriterien
 */
export function filterConversations(
  conversations: LiveConversation[],
  search: string,
  status: string,
  agent: string,
  priority: string
): LiveConversation[] {
  let filtered = conversations

  // Text-Suche
  if (search.trim()) {
    const searchLower = search.toLowerCase()
    filtered = filtered.filter(conv =>
      conv.agentName.toLowerCase().includes(searchLower) ||
      conv.callerInfo?.name?.toLowerCase().includes(searchLower) ||
      conv.id.toLowerCase().includes(searchLower)
    )
  }

  // Status-Filter
  if (status !== 'all') {
    filtered = filtered.filter(conv => conv.status === status)
  }

  // Agent-Filter
  if (agent !== 'all') {
    filtered = filtered.filter(conv => conv.agentId === agent)
  }

  // Prioritäts-Filter
  if (priority !== 'all') {
    filtered = filtered.filter(conv => conv.metadata.priority === priority)
  }

  return filtered
}

/**
 * Berechnet Statistiken für eine Liste von Gesprächen
 */
export function calculateConversationStats(conversations: LiveConversation[]) {
  const total = conversations.length
  const active = conversations.filter(c => c.status === 'active').length
  const onHold = conversations.filter(c => c.status === 'on-hold').length
  const connecting = conversations.filter(c => c.status === 'connecting').length
  const ending = conversations.filter(c => c.status === 'ending').length

  const averageDuration = total > 0 
    ? conversations.reduce((sum, conv) => sum + conv.duration, 0) / total 
    : 0

  const averageQuality = total > 0
    ? conversations.reduce((sum, conv) => sum + conv.qualityMetrics.connectionQuality, 0) / total
    : 0

  return {
    total,
    active,
    onHold,
    connecting,
    ending,
    averageDuration,
    averageQuality
  }
}

/**
 * Generiert eine Farbe basierend auf dem Agent-Namen (für Avatars)
 */
export function getAgentColor(agentName: string): string {
  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-teal-500'
  ]
  
  const hash = agentName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  return colors[hash % colors.length]
}

/**
 * Extrahiert Initialen aus einem Namen
 */
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
