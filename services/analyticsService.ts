// Analytics-Service für Story 4: Analytics-Reports und Metriken

import { 
  AnalyticsTimeRange, 
  AnalyticsQuery, 
  AnalyticsResponse,
  PeriodicReport,
  SuccessRateAnalysis,
  ConversationDurationStats,
  ToolUsageAnalytics,
  TrendData,
  BenchmarkComparison
} from '@/types/analytics'
import { supabase } from '@/lib/supabase'

export class AnalyticsService {
  private static instance: AnalyticsService
  private cache = new Map<string, { data: any; expiry: Date }>()

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService()
    }
    return AnalyticsService.instance
  }

  // Periodische Reports generieren
  async generatePeriodicReport(query: AnalyticsQuery): Promise<AnalyticsResponse<PeriodicReport>> {
    const cacheKey = `periodic_${JSON.stringify(query)}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const { data: conversations, error } = await supabase
        .from('conversations')
        .select(`
          id,
          agent_id,
          start_time,
          end_time,
          status,
          duration,
          success,
          agents (
            id,
            name
          )
        `)
        .gte('start_time', query.timeRange.startDate.toISOString())
        .lte('start_time', query.timeRange.endDate.toISOString())
        .in('agent_id', query.agentIds || [])

      if (error) throw error

      const report = await this.processPeriodicReportData(conversations, query)
      const response: AnalyticsResponse<PeriodicReport> = {
        data: report,
        timeRange: query.timeRange,
        totalRecords: conversations.length,
        generatedAt: new Date()
      }

      this.setCache(cacheKey, response, 15) // 15 Minuten Cache
      return response
    } catch (error) {
      console.error('Fehler beim Generieren des periodischen Reports:', error)
      throw error
    }
  }

  // Erfolgsrate-Analyse
  async generateSuccessRateAnalysis(query: AnalyticsQuery): Promise<AnalyticsResponse<SuccessRateAnalysis>> {
    const cacheKey = `success_rate_${JSON.stringify(query)}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const { data: conversations, error } = await supabase
        .from('conversations')
        .select(`
          id,
          agent_id,
          start_time,
          success,
          agents (
            id,
            name
          )
        `)
        .gte('start_time', query.timeRange.startDate.toISOString())
        .lte('start_time', query.timeRange.endDate.toISOString())
        .in('agent_id', query.agentIds || [])

      if (error) throw error

      const analysis = await this.processSuccessRateData(conversations, query)
      const response: AnalyticsResponse<SuccessRateAnalysis> = {
        data: analysis,
        timeRange: query.timeRange,
        totalRecords: conversations.length,
        generatedAt: new Date()
      }

      this.setCache(cacheKey, response, 10) // 10 Minuten Cache
      return response
    } catch (error) {
      console.error('Fehler bei der Erfolgsrate-Analyse:', error)
      throw error
    }
  }

  // Gesprächsdauer-Statistiken
  async generateDurationStats(query: AnalyticsQuery): Promise<AnalyticsResponse<ConversationDurationStats>> {
    const cacheKey = `duration_stats_${JSON.stringify(query)}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const { data: conversations, error } = await supabase
        .from('conversations')
        .select(`
          id,
          agent_id,
          start_time,
          duration,
          success,
          agents (
            id,
            name
          )
        `)
        .gte('start_time', query.timeRange.startDate.toISOString())
        .lte('start_time', query.timeRange.endDate.toISOString())
        .in('agent_id', query.agentIds || [])
        .not('duration', 'is', null)

      if (error) throw error

      const stats = await this.processDurationStatsData(conversations, query)
      const response: AnalyticsResponse<ConversationDurationStats> = {
        data: stats,
        timeRange: query.timeRange,
        totalRecords: conversations.length,
        generatedAt: new Date()
      }

      this.setCache(cacheKey, response, 20) // 20 Minuten Cache
      return response
    } catch (error) {
      console.error('Fehler bei den Gesprächsdauer-Statistiken:', error)
      throw error
    }
  }

  // Tool-Nutzungs-Analytics
  async generateToolUsageAnalytics(query: AnalyticsQuery): Promise<AnalyticsResponse<ToolUsageAnalytics>> {
    const cacheKey = `tool_usage_${JSON.stringify(query)}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const { data: toolUsage, error } = await supabase
        .from('tool_usage_logs')
        .select(`
          id,
          conversation_id,
          agent_id,
          tool_name,
          execution_time,
          success,
          error_message,
          timestamp,
          conversations (
            start_time
          ),
          agents (
            id,
            name
          )
        `)
        .gte('timestamp', query.timeRange.startDate.toISOString())
        .lte('timestamp', query.timeRange.endDate.toISOString())
        .in('agent_id', query.agentIds || [])

      if (error) throw error

      const analytics = await this.processToolUsageData(toolUsage, query)
      const response: AnalyticsResponse<ToolUsageAnalytics> = {
        data: analytics,
        timeRange: query.timeRange,
        totalRecords: toolUsage.length,
        generatedAt: new Date()
      }

      this.setCache(cacheKey, response, 30) // 30 Minuten Cache
      return response
    } catch (error) {
      console.error('Fehler bei der Tool-Nutzungs-Analyse:', error)
      throw error
    }
  }

  // Private Hilfsmethoden
  private async processPeriodicReportData(conversations: any[], query: AnalyticsQuery): Promise<PeriodicReport> {
    const totalConversations = conversations.length
    const successfulConversations = conversations.filter(c => c.success).length
    const successRate = totalConversations > 0 ? (successfulConversations / totalConversations) * 100 : 0

    // Durchschnittliche Gesprächsdauer berechnen
    const durationsInSeconds = conversations
      .filter(c => c.duration)
      .map(c => c.duration)
    const averageDuration = durationsInSeconds.length > 0 
      ? durationsInSeconds.reduce((sum, d) => sum + d, 0) / durationsInSeconds.length 
      : 0

    // Peak-Stunden analysieren
    const hourCounts = new Map<number, number>()
    conversations.forEach(c => {
      const hour = new Date(c.start_time).getHours()
      hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1)
    })
    const peakHours = Array.from(hourCounts.entries())
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)

    // Agent-Performance aggregieren
    const agentPerformance = this.aggregateAgentPerformance(conversations)

    return {
      id: `report_${Date.now()}`,
      title: `Periodischer Report ${query.timeRange.period}`,
      timeRange: query.timeRange,
      generatedAt: new Date(),
      metrics: {
        totalConversations,
        successfulConversations,
        successRate,
        averageDuration,
        peakHours,
        agentPerformance
      }
    }
  }

  private async processSuccessRateData(conversations: any[], query: AnalyticsQuery): Promise<SuccessRateAnalysis> {
    const totalConversations = conversations.length
    const successfulConversations = conversations.filter(c => c.success).length
    const overallRate = totalConversations > 0 ? (successfulConversations / totalConversations) * 100 : 0

    // Erfolgsrate nach Agent
    const agentStats = new Map<string, { total: number; successful: number; name: string }>()
    conversations.forEach(c => {
      const agentId = c.agent_id
      const current = agentStats.get(agentId) || { total: 0, successful: 0, name: c.agents?.name || 'Unbekannt' }
      current.total++
      if (c.success) current.successful++
      agentStats.set(agentId, current)
    })

    const byAgent = Array.from(agentStats.entries()).map(([agentId, stats]) => ({
      agentId,
      agentName: stats.name,
      rate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0,
      trend: 0, // TODO: Implementiere Trend-Berechnung
      conversationCount: stats.total
    }))

    // Erfolgsrate nach Tageszeit
    const hourStats = new Map<number, { total: number; successful: number }>()
    conversations.forEach(c => {
      const hour = new Date(c.start_time).getHours()
      const current = hourStats.get(hour) || { total: 0, successful: 0 }
      current.total++
      if (c.success) current.successful++
      hourStats.set(hour, current)
    })

    const byTimeOfDay = Array.from(hourStats.entries()).map(([hour, stats]) => ({
      hour,
      rate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0,
      conversationCount: stats.total
    }))

    // Erfolgsrate nach Wochentag
    const dayNames = ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag']
    const dayStats = new Map<number, { total: number; successful: number }>()
    conversations.forEach(c => {
      const day = new Date(c.start_time).getDay()
      const current = dayStats.get(day) || { total: 0, successful: 0 }
      current.total++
      if (c.success) current.successful++
      dayStats.set(day, current)
    })

    const byDayOfWeek = Array.from(dayStats.entries()).map(([day, stats]) => ({
      day,
      dayName: dayNames[day],
      rate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0,
      conversationCount: stats.total
    }))

    return {
      overall: {
        rate: overallRate,
        trend: 0, // TODO: Implementiere Trend-Berechnung
        benchmark: 85 // Standard-Benchmark
      },
      byAgent,
      byTimeOfDay,
      byDayOfWeek
    }
  }

  private async processDurationStatsData(conversations: any[], query: AnalyticsQuery): Promise<ConversationDurationStats> {
    const durations = conversations
      .filter(c => c.duration && c.duration > 0)
      .map(c => c.duration)
      .sort((a, b) => a - b)

    if (durations.length === 0) {
      return this.getEmptyDurationStats()
    }

    // Grundlegende Statistiken
    const sum = durations.reduce((a, b) => a + b, 0)
    const average = sum / durations.length
    const median = this.calculateMedian(durations)
    const min = durations[0]
    const max = durations[durations.length - 1]
    const standardDeviation = this.calculateStandardDeviation(durations, average)

    // Perzentile
    const percentiles = {
      p25: this.calculatePercentile(durations, 25),
      p50: median,
      p75: this.calculatePercentile(durations, 75),
      p95: this.calculatePercentile(durations, 95)
    }

    // Verteilung
    const short = durations.filter(d => d < 120).length // < 2 Min
    const normal = durations.filter(d => d >= 120 && d <= 600).length // 2-10 Min
    const long = durations.filter(d => d > 600).length // > 10 Min

    // Histogramm
    const histogram = this.createDurationHistogram(durations)

    // Korrelationen
    const correlations = {
      durationVsSuccess: this.calculateDurationSuccessCorrelation(conversations),
      durationVsTimeOfDay: this.calculateDurationTimeCorrelation(conversations),
      durationVsAgent: this.calculateDurationAgentCorrelation(conversations)
    }

    return {
      summary: {
        average,
        median,
        min,
        max,
        standardDeviation,
        percentiles
      },
      distribution: {
        short,
        normal,
        long
      },
      histogram,
      correlations
    }
  }

  // Weitere private Hilfsmethoden...
  private aggregateAgentPerformance(conversations: any[]): any[] {
    // TODO: Implementiere Agent-Performance-Aggregation
    return []
  }

  private calculateMedian(values: number[]): number {
    const mid = Math.floor(values.length / 2)
    return values.length % 2 !== 0 ? values[mid] : (values[mid - 1] + values[mid]) / 2
  }

  private calculatePercentile(values: number[], percentile: number): number {
    const index = (percentile / 100) * (values.length - 1)
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    const weight = index % 1
    
    if (upper >= values.length) return values[values.length - 1]
    return values[lower] * (1 - weight) + values[upper] * weight
  }

  private calculateStandardDeviation(values: number[], mean: number): number {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2))
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length
    return Math.sqrt(avgSquaredDiff)
  }

  private createDurationHistogram(durations: number[]): Array<{ bucket: string; count: number; percentage: number }> {
    const buckets = [
      { min: 0, max: 60, label: '0-1' },
      { min: 60, max: 120, label: '1-2' },
      { min: 120, max: 300, label: '2-5' },
      { min: 300, max: 600, label: '5-10' },
      { min: 600, max: 1200, label: '10-20' },
      { min: 1200, max: Infinity, label: '20+' }
    ]

    return buckets.map(bucket => {
      const count = durations.filter(d => d >= bucket.min && d < bucket.max).length
      const percentage = (count / durations.length) * 100
      return {
        bucket: bucket.label,
        count,
        percentage
      }
    })
  }

  private calculateDurationSuccessCorrelation(conversations: any[]): number {
    // TODO: Implementiere Korrelationsberechnung
    return 0
  }

  private calculateDurationTimeCorrelation(conversations: any[]): Array<{ hour: number; averageDuration: number }> {
    // TODO: Implementiere Zeit-Korrelation
    return []
  }

  private calculateDurationAgentCorrelation(conversations: any[]): Array<{ agentId: string; agentName: string; averageDuration: number }> {
    // TODO: Implementiere Agent-Korrelation
    return []
  }

  private getEmptyDurationStats(): ConversationDurationStats {
    return {
      summary: {
        average: 0,
        median: 0,
        min: 0,
        max: 0,
        standardDeviation: 0,
        percentiles: { p25: 0, p50: 0, p75: 0, p95: 0 }
      },
      distribution: { short: 0, normal: 0, long: 0 },
      histogram: [],
      correlations: {
        durationVsSuccess: 0,
        durationVsTimeOfDay: [],
        durationVsAgent: []
      }
    }
  }

  private async processToolUsageData(toolUsage: any[], query: AnalyticsQuery): Promise<ToolUsageAnalytics> {
    // TODO: Implementiere Tool-Usage-Analyse
    return {
      toolStats: [],
      agentToolUsage: [],
      toolFlows: [],
      recommendations: []
    }
  }

  // Cache-Management
  private getFromCache(key: string): any {
    const cached = this.cache.get(key)
    if (cached && cached.expiry > new Date()) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: any, minutesToLive: number): void {
    const expiry = new Date()
    expiry.setMinutes(expiry.getMinutes() + minutesToLive)
    this.cache.set(key, { data, expiry })
  }
}
