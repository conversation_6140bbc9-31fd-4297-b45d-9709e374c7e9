// Export-Service für Story 4: Analytics-Reports und Metriken

import { ExportOptions, ExportResult } from '@/types/analytics'
import { supabase } from '@/lib/supabase'

export class ExportService {
  private static instance: ExportService

  static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService()
    }
    return ExportService.instance
  }

  // Hauptexport-Methode
  async exportReport(
    data: any,
    format: 'pdf' | 'excel' | 'csv',
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      switch (format) {
        case 'pdf':
          return await this.exportToPDF(data, options)
        case 'excel':
          return await this.exportToExcel(data, options)
        case 'csv':
          return await this.exportToCSV(data, options)
        default:
          throw new Error(`Nicht unterstütztes Export-Format: ${format}`)
      }
    } catch (error) {
      console.error('Fehler beim Export:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // PDF-Export
  private async exportToPDF(data: any, options: ExportOptions): Promise<ExportResult> {
    try {
      // PDF-Generierung mit jsPDF
      const { jsPDF } = await import('jspdf')
      const doc = new jsPDF()

      // Header
      doc.setFontSize(20)
      doc.text('Analytics Report', 20, 30)
      
      doc.setFontSize(12)
      doc.text(`Generiert am: ${new Date().toLocaleDateString('de-DE')}`, 20, 45)

      let yPosition = 60

      // Report-spezifische Inhalte
      if (data.metrics) {
        // Periodischer Report
        yPosition = this.addPeriodicReportToPDF(doc, data, yPosition)
      } else if (data.overall) {
        // Erfolgsrate-Analyse
        yPosition = this.addSuccessRateAnalysisToPDF(doc, data, yPosition)
      } else if (data.summary) {
        // Gesprächsdauer-Statistiken
        yPosition = this.addDurationStatsToPDF(doc, data, yPosition)
      } else if (data.toolStats) {
        // Tool-Nutzungs-Analytics
        yPosition = this.addToolUsageAnalyticsToPDF(doc, data, yPosition)
      }

      // PDF als Blob generieren
      const pdfBlob = doc.output('blob')
      const filename = `analytics_report_${Date.now()}.pdf`

      // Upload zu Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(filename, pdfBlob, {
          contentType: 'application/pdf',
          upsert: false
        })

      if (uploadError) throw uploadError

      // Public URL generieren
      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(filename)

      return {
        success: true,
        downloadUrl: urlData.publicUrl,
        filename,
        size: pdfBlob.size
      }
    } catch (error) {
      console.error('Fehler beim PDF-Export:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // Excel-Export
  private async exportToExcel(data: any, options: ExportOptions): Promise<ExportResult> {
    try {
      const XLSX = await import('xlsx')
      const workbook = XLSX.utils.book_new()

      // Report-spezifische Sheets erstellen
      if (data.metrics) {
        // Periodischer Report
        this.addPeriodicReportToExcel(workbook, data, XLSX)
      } else if (data.overall) {
        // Erfolgsrate-Analyse
        this.addSuccessRateAnalysisToExcel(workbook, data, XLSX)
      } else if (data.summary) {
        // Gesprächsdauer-Statistiken
        this.addDurationStatsToExcel(workbook, data, XLSX)
      } else if (data.toolStats) {
        // Tool-Nutzungs-Analytics
        this.addToolUsageAnalyticsToExcel(workbook, data, XLSX)
      }

      // Excel-Datei generieren
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      const excelBlob = new Blob([excelBuffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      })
      const filename = `analytics_report_${Date.now()}.xlsx`

      // Upload zu Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(filename, excelBlob, {
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          upsert: false
        })

      if (uploadError) throw uploadError

      // Public URL generieren
      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(filename)

      return {
        success: true,
        downloadUrl: urlData.publicUrl,
        filename,
        size: excelBlob.size
      }
    } catch (error) {
      console.error('Fehler beim Excel-Export:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // CSV-Export
  private async exportToCSV(data: any, options: ExportOptions): Promise<ExportResult> {
    try {
      let csvContent = ''

      // Report-spezifische CSV-Generierung
      if (data.metrics) {
        csvContent = this.generatePeriodicReportCSV(data)
      } else if (data.overall) {
        csvContent = this.generateSuccessRateAnalysisCSV(data)
      } else if (data.summary) {
        csvContent = this.generateDurationStatsCSV(data)
      } else if (data.toolStats) {
        csvContent = this.generateToolUsageAnalyticsCSV(data)
      }

      const csvBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const filename = `analytics_report_${Date.now()}.csv`

      // Upload zu Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('reports')
        .upload(filename, csvBlob, {
          contentType: 'text/csv',
          upsert: false
        })

      if (uploadError) throw uploadError

      // Public URL generieren
      const { data: urlData } = supabase.storage
        .from('reports')
        .getPublicUrl(filename)

      return {
        success: true,
        downloadUrl: urlData.publicUrl,
        filename,
        size: csvBlob.size
      }
    } catch (error) {
      console.error('Fehler beim CSV-Export:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // PDF-Hilfsmethoden
  private addPeriodicReportToPDF(doc: any, data: any, yPosition: number): number {
    doc.setFontSize(16)
    doc.text('Periodischer Report', 20, yPosition)
    yPosition += 20

    doc.setFontSize(12)
    doc.text(`Gesamtgespräche: ${data.metrics.totalConversations}`, 20, yPosition)
    yPosition += 10
    doc.text(`Erfolgreiche Gespräche: ${data.metrics.successfulConversations}`, 20, yPosition)
    yPosition += 10
    doc.text(`Erfolgsrate: ${data.metrics.successRate.toFixed(1)}%`, 20, yPosition)
    yPosition += 10
    doc.text(`Durchschnittliche Dauer: ${Math.round(data.metrics.averageDuration)} Sekunden`, 20, yPosition)
    yPosition += 20

    return yPosition
  }

  private addSuccessRateAnalysisToPDF(doc: any, data: any, yPosition: number): number {
    doc.setFontSize(16)
    doc.text('Erfolgsrate-Analyse', 20, yPosition)
    yPosition += 20

    doc.setFontSize(12)
    doc.text(`Gesamterfolgsrate: ${data.overall.rate.toFixed(1)}%`, 20, yPosition)
    yPosition += 10
    doc.text(`Benchmark: ${data.overall.benchmark}%`, 20, yPosition)
    yPosition += 20

    // Agent-Performance
    if (data.byAgent && data.byAgent.length > 0) {
      doc.text('Performance nach Agent:', 20, yPosition)
      yPosition += 10
      
      data.byAgent.forEach((agent: any) => {
        doc.text(`${agent.agentName}: ${agent.rate.toFixed(1)}% (${agent.conversationCount} Gespräche)`, 25, yPosition)
        yPosition += 8
      })
    }

    return yPosition + 10
  }

  private addDurationStatsToPDF(doc: any, data: any, yPosition: number): number {
    doc.setFontSize(16)
    doc.text('Gesprächsdauer-Statistiken', 20, yPosition)
    yPosition += 20

    doc.setFontSize(12)
    doc.text(`Durchschnitt: ${Math.round(data.summary.average)} Sekunden`, 20, yPosition)
    yPosition += 10
    doc.text(`Median: ${Math.round(data.summary.median)} Sekunden`, 20, yPosition)
    yPosition += 10
    doc.text(`Min: ${Math.round(data.summary.min)} Sekunden`, 20, yPosition)
    yPosition += 10
    doc.text(`Max: ${Math.round(data.summary.max)} Sekunden`, 20, yPosition)
    yPosition += 20

    return yPosition
  }

  private addToolUsageAnalyticsToPDF(doc: any, data: any, yPosition: number): number {
    doc.setFontSize(16)
    doc.text('Tool-Nutzungs-Analytics', 20, yPosition)
    yPosition += 20

    if (data.toolStats && data.toolStats.length > 0) {
      doc.setFontSize(12)
      doc.text('Tool-Statistiken:', 20, yPosition)
      yPosition += 10

      data.toolStats.forEach((tool: any) => {
        doc.text(`${tool.toolName}: ${tool.usageCount} Nutzungen (${tool.successRate.toFixed(1)}% Erfolg)`, 25, yPosition)
        yPosition += 8
      })
    }

    return yPosition + 10
  }

  // Excel-Hilfsmethoden
  private addPeriodicReportToExcel(workbook: any, data: any, XLSX: any): void {
    // Übersicht-Sheet
    const overviewData = [
      ['Metrik', 'Wert'],
      ['Gesamtgespräche', data.metrics.totalConversations],
      ['Erfolgreiche Gespräche', data.metrics.successfulConversations],
      ['Erfolgsrate (%)', data.metrics.successRate.toFixed(1)],
      ['Durchschnittliche Dauer (Sek)', Math.round(data.metrics.averageDuration)]
    ]
    const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData)
    XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Übersicht')

    // Peak-Stunden-Sheet
    if (data.metrics.peakHours && data.metrics.peakHours.length > 0) {
      const peakHoursData = [
        ['Stunde', 'Anzahl Gespräche'],
        ...data.metrics.peakHours.map((peak: any) => [peak.hour, peak.count])
      ]
      const peakHoursSheet = XLSX.utils.aoa_to_sheet(peakHoursData)
      XLSX.utils.book_append_sheet(workbook, peakHoursSheet, 'Peak-Stunden')
    }
  }

  private addSuccessRateAnalysisToExcel(workbook: any, data: any, XLSX: any): void {
    // Übersicht-Sheet
    const overviewData = [
      ['Metrik', 'Wert'],
      ['Gesamterfolgsrate (%)', data.overall.rate.toFixed(1)],
      ['Benchmark (%)', data.overall.benchmark],
      ['Trend (%)', data.overall.trend.toFixed(1)]
    ]
    const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData)
    XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Übersicht')

    // Agent-Performance-Sheet
    if (data.byAgent && data.byAgent.length > 0) {
      const agentData = [
        ['Agent-Name', 'Erfolgsrate (%)', 'Anzahl Gespräche', 'Trend (%)'],
        ...data.byAgent.map((agent: any) => [
          agent.agentName,
          agent.rate.toFixed(1),
          agent.conversationCount,
          agent.trend.toFixed(1)
        ])
      ]
      const agentSheet = XLSX.utils.aoa_to_sheet(agentData)
      XLSX.utils.book_append_sheet(workbook, agentSheet, 'Agent-Performance')
    }
  }

  private addDurationStatsToExcel(workbook: any, data: any, XLSX: any): void {
    // Statistiken-Sheet
    const statsData = [
      ['Statistik', 'Wert (Sekunden)'],
      ['Durchschnitt', Math.round(data.summary.average)],
      ['Median', Math.round(data.summary.median)],
      ['Minimum', Math.round(data.summary.min)],
      ['Maximum', Math.round(data.summary.max)],
      ['Standardabweichung', Math.round(data.summary.standardDeviation)],
      ['25. Perzentil', Math.round(data.summary.percentiles.p25)],
      ['75. Perzentil', Math.round(data.summary.percentiles.p75)],
      ['95. Perzentil', Math.round(data.summary.percentiles.p95)]
    ]
    const statsSheet = XLSX.utils.aoa_to_sheet(statsData)
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Statistiken')

    // Histogramm-Sheet
    if (data.histogram && data.histogram.length > 0) {
      const histogramData = [
        ['Zeitbereich (Min)', 'Anzahl', 'Prozent'],
        ...data.histogram.map((bucket: any) => [
          bucket.bucket,
          bucket.count,
          bucket.percentage.toFixed(1)
        ])
      ]
      const histogramSheet = XLSX.utils.aoa_to_sheet(histogramData)
      XLSX.utils.book_append_sheet(workbook, histogramSheet, 'Verteilung')
    }
  }

  private addToolUsageAnalyticsToExcel(workbook: any, data: any, XLSX: any): void {
    // Tool-Statistiken-Sheet
    if (data.toolStats && data.toolStats.length > 0) {
      const toolStatsData = [
        ['Tool-Name', 'Nutzungen', 'Erfolgsrate (%)', 'Ø Ausführungszeit (ms)', 'Fehlerrate (%)'],
        ...data.toolStats.map((tool: any) => [
          tool.toolName,
          tool.usageCount,
          tool.successRate.toFixed(1),
          Math.round(tool.averageExecutionTime),
          tool.errorRate.toFixed(1)
        ])
      ]
      const toolStatsSheet = XLSX.utils.aoa_to_sheet(toolStatsData)
      XLSX.utils.book_append_sheet(workbook, toolStatsSheet, 'Tool-Statistiken')
    }
  }

  // CSV-Hilfsmethoden
  private generatePeriodicReportCSV(data: any): string {
    let csv = 'Metrik,Wert\n'
    csv += `Gesamtgespräche,${data.metrics.totalConversations}\n`
    csv += `Erfolgreiche Gespräche,${data.metrics.successfulConversations}\n`
    csv += `Erfolgsrate (%),${data.metrics.successRate.toFixed(1)}\n`
    csv += `Durchschnittliche Dauer (Sek),${Math.round(data.metrics.averageDuration)}\n`
    return csv
  }

  private generateSuccessRateAnalysisCSV(data: any): string {
    let csv = 'Agent-Name,Erfolgsrate (%),Anzahl Gespräche\n'
    if (data.byAgent) {
      data.byAgent.forEach((agent: any) => {
        csv += `${agent.agentName},${agent.rate.toFixed(1)},${agent.conversationCount}\n`
      })
    }
    return csv
  }

  private generateDurationStatsCSV(data: any): string {
    let csv = 'Statistik,Wert (Sekunden)\n'
    csv += `Durchschnitt,${Math.round(data.summary.average)}\n`
    csv += `Median,${Math.round(data.summary.median)}\n`
    csv += `Minimum,${Math.round(data.summary.min)}\n`
    csv += `Maximum,${Math.round(data.summary.max)}\n`
    return csv
  }

  private generateToolUsageAnalyticsCSV(data: any): string {
    let csv = 'Tool-Name,Nutzungen,Erfolgsrate (%)\n'
    if (data.toolStats) {
      data.toolStats.forEach((tool: any) => {
        csv += `${tool.toolName},${tool.usageCount},${tool.successRate.toFixed(1)}\n`
      })
    }
    return csv
  }
}
