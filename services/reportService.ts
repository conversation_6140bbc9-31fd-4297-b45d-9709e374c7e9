// Report-Service für Story 4: Analytics-Reports und Metriken

import { 
  ReportGenerationRequest,
  ReportGenerationResponse,
  ReportStatus,
  ScheduledReport,
  ExportResult
} from '@/types/analytics'
import { 
  GeneratedReport,
  ReportTemplate,
  ReportSchedule,
  ReportDelivery
} from '@/types/reports'
import { AnalyticsService } from './analyticsService'
import { ExportService } from './exportService'
import { supabase } from '@/lib/supabase'

export class ReportService {
  private static instance: ReportService
  private analyticsService: AnalyticsService
  private exportService: ExportService
  private reportQueue = new Map<string, ReportGenerationResponse>()

  constructor() {
    this.analyticsService = AnalyticsService.getInstance()
    this.exportService = ExportService.getInstance()
  }

  static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService()
    }
    return ReportService.instance
  }

  // Report-Generierung
  async generateReport(request: ReportGenerationRequest): Promise<ReportGenerationResponse> {
    const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const response: ReportGenerationResponse = {
      reportId,
      status: 'queued',
      progress: 0,
      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000) // 5 Minuten Schätzung
    }

    this.reportQueue.set(reportId, response)

    // Asynchrone Report-Generierung starten
    this.processReportGeneration(reportId, request).catch(error => {
      console.error(`Fehler bei Report-Generierung ${reportId}:`, error)
      this.updateReportStatus(reportId, 'failed', 0, undefined, error.message)
    })

    return response
  }

  // Report-Status abrufen
  async getReportStatus(reportId: string): Promise<ReportStatus | null> {
    const queuedReport = this.reportQueue.get(reportId)
    if (queuedReport) {
      return {
        reportId,
        status: queuedReport.status,
        progress: queuedReport.progress || 0,
        createdAt: new Date(),
        downloadUrl: queuedReport.downloadUrl,
        error: queuedReport.error
      }
    }

    // Aus Datenbank laden
    const { data: report, error } = await supabase
      .from('generated_reports')
      .select('*')
      .eq('id', reportId)
      .single()

    if (error || !report) return null

    return {
      reportId: report.id,
      status: report.status,
      progress: report.progress,
      createdAt: new Date(report.created_at),
      completedAt: report.completed_at ? new Date(report.completed_at) : undefined,
      downloadUrl: report.download_url,
      expiresAt: report.expires_at ? new Date(report.expires_at) : undefined,
      error: report.error
    }
  }

  // Geplante Reports verwalten
  async createScheduledReport(schedule: Omit<ScheduledReport, 'id' | 'createdAt' | 'updatedAt' | 'nextRun'>): Promise<ScheduledReport> {
    const nextRun = this.calculateNextRun(schedule.schedule)
    
    const { data: savedSchedule, error } = await supabase
      .from('scheduled_reports')
      .insert({
        name: schedule.name,
        report_type: schedule.reportType,
        schedule: schedule.schedule,
        config: schedule.config,
        recipients: schedule.recipients,
        enabled: schedule.enabled,
        next_run: nextRun.toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return {
      id: savedSchedule.id,
      name: savedSchedule.name,
      reportType: savedSchedule.report_type,
      schedule: savedSchedule.schedule,
      config: savedSchedule.config,
      recipients: savedSchedule.recipients,
      enabled: savedSchedule.enabled,
      nextRun,
      createdAt: new Date(savedSchedule.created_at),
      updatedAt: new Date(savedSchedule.updated_at)
    }
  }

  async getScheduledReports(): Promise<ScheduledReport[]> {
    const { data: schedules, error } = await supabase
      .from('scheduled_reports')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error

    return schedules.map(schedule => ({
      id: schedule.id,
      name: schedule.name,
      reportType: schedule.report_type,
      schedule: schedule.schedule,
      config: schedule.config,
      recipients: schedule.recipients,
      enabled: schedule.enabled,
      lastRun: schedule.last_run ? new Date(schedule.last_run) : undefined,
      nextRun: new Date(schedule.next_run),
      createdAt: new Date(schedule.created_at),
      updatedAt: new Date(schedule.updated_at)
    }))
  }

  async updateScheduledReport(id: string, updates: Partial<ScheduledReport>): Promise<ScheduledReport> {
    const updateData: any = {}
    
    if (updates.name) updateData.name = updates.name
    if (updates.schedule) {
      updateData.schedule = updates.schedule
      updateData.next_run = this.calculateNextRun(updates.schedule).toISOString()
    }
    if (updates.config) updateData.config = updates.config
    if (updates.recipients) updateData.recipients = updates.recipients
    if (typeof updates.enabled === 'boolean') updateData.enabled = updates.enabled

    const { data: updatedSchedule, error } = await supabase
      .from('scheduled_reports')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    return {
      id: updatedSchedule.id,
      name: updatedSchedule.name,
      reportType: updatedSchedule.report_type,
      schedule: updatedSchedule.schedule,
      config: updatedSchedule.config,
      recipients: updatedSchedule.recipients,
      enabled: updatedSchedule.enabled,
      lastRun: updatedSchedule.last_run ? new Date(updatedSchedule.last_run) : undefined,
      nextRun: new Date(updatedSchedule.next_run),
      createdAt: new Date(updatedSchedule.created_at),
      updatedAt: new Date(updatedSchedule.updated_at)
    }
  }

  async deleteScheduledReport(id: string): Promise<void> {
    const { error } = await supabase
      .from('scheduled_reports')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Report-Templates verwalten
  async getReportTemplates(): Promise<ReportTemplate[]> {
    const { data: templates, error } = await supabase
      .from('report_templates')
      .select('*')
      .order('name')

    if (error) throw error

    return templates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      type: template.type,
      sections: template.sections,
      defaultConfig: template.default_config,
      createdAt: new Date(template.created_at),
      updatedAt: new Date(template.updated_at)
    }))
  }

  // Private Methoden
  private async processReportGeneration(reportId: string, request: ReportGenerationRequest): Promise<void> {
    try {
      this.updateReportStatus(reportId, 'processing', 10)

      // Analytics-Daten abrufen
      let analyticsData: any
      const query = {
        timeRange: request.timeRange,
        agentIds: request.config.agentIds
      }

      this.updateReportStatus(reportId, 'processing', 30)

      switch (request.reportType) {
        case 'periodic':
          const periodicResponse = await this.analyticsService.generatePeriodicReport(query)
          analyticsData = periodicResponse.data
          break
        case 'success_rate':
          const successResponse = await this.analyticsService.generateSuccessRateAnalysis(query)
          analyticsData = successResponse.data
          break
        case 'duration_stats':
          const durationResponse = await this.analyticsService.generateDurationStats(query)
          analyticsData = durationResponse.data
          break
        case 'tool_usage':
          const toolResponse = await this.analyticsService.generateToolUsageAnalytics(query)
          analyticsData = toolResponse.data
          break
        default:
          throw new Error(`Unbekannter Report-Typ: ${request.reportType}`)
      }

      this.updateReportStatus(reportId, 'processing', 60)

      // Export generieren
      const exportResult = await this.exportService.exportReport(
        analyticsData,
        request.config.format,
        {
          format: request.config.format,
          includeCharts: request.config.includeCharts || false,
          language: request.config.language || 'de'
        }
      )

      this.updateReportStatus(reportId, 'processing', 90)

      if (!exportResult.success) {
        throw new Error(exportResult.error || 'Export fehlgeschlagen')
      }

      // Report in Datenbank speichern
      await this.saveGeneratedReport(reportId, request, analyticsData, exportResult)

      this.updateReportStatus(reportId, 'completed', 100, exportResult.downloadUrl)

    } catch (error) {
      console.error(`Fehler bei Report-Generierung ${reportId}:`, error)
      this.updateReportStatus(reportId, 'failed', 0, undefined, error.message)
    }
  }

  private updateReportStatus(
    reportId: string, 
    status: ReportGenerationResponse['status'], 
    progress: number,
    downloadUrl?: string,
    error?: string
  ): void {
    const report = this.reportQueue.get(reportId)
    if (report) {
      report.status = status
      report.progress = progress
      if (downloadUrl) report.downloadUrl = downloadUrl
      if (error) report.error = error
      this.reportQueue.set(reportId, report)
    }
  }

  private async saveGeneratedReport(
    reportId: string,
    request: ReportGenerationRequest,
    data: any,
    exportResult: ExportResult
  ): Promise<void> {
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // 7 Tage Aufbewahrung

    const { error } = await supabase
      .from('generated_reports')
      .insert({
        id: reportId,
        report_type: request.reportType,
        config: request.config,
        data,
        status: 'completed',
        progress: 100,
        download_url: exportResult.downloadUrl,
        filename: exportResult.filename,
        file_size: exportResult.size,
        expires_at: expiresAt.toISOString(),
        completed_at: new Date().toISOString()
      })

    if (error) throw error
  }

  private calculateNextRun(schedule: ScheduledReport['schedule']): Date {
    const now = new Date()
    const [hours, minutes] = schedule.time.split(':').map(Number)
    
    const nextRun = new Date(now)
    nextRun.setHours(hours, minutes, 0, 0)

    switch (schedule.frequency) {
      case 'daily':
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1)
        }
        break
      case 'weekly':
        const targetDay = schedule.dayOfWeek || 1 // Montag als Standard
        const currentDay = nextRun.getDay()
        let daysUntilTarget = targetDay - currentDay
        if (daysUntilTarget <= 0 || (daysUntilTarget === 0 && nextRun <= now)) {
          daysUntilTarget += 7
        }
        nextRun.setDate(nextRun.getDate() + daysUntilTarget)
        break
      case 'monthly':
        const targetDate = schedule.dayOfMonth || 1
        nextRun.setDate(targetDate)
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1)
        }
        break
    }

    return nextRun
  }

  // Geplante Reports ausführen (wird von Cron-Job aufgerufen)
  async executeScheduledReports(): Promise<void> {
    const now = new Date()
    
    const { data: dueReports, error } = await supabase
      .from('scheduled_reports')
      .select('*')
      .eq('enabled', true)
      .lte('next_run', now.toISOString())

    if (error) {
      console.error('Fehler beim Abrufen geplanter Reports:', error)
      return
    }

    for (const scheduledReport of dueReports) {
      try {
        await this.executeScheduledReport(scheduledReport)
      } catch (error) {
        console.error(`Fehler beim Ausführen des geplanten Reports ${scheduledReport.id}:`, error)
      }
    }
  }

  private async executeScheduledReport(scheduledReport: any): Promise<void> {
    // Report-Request erstellen
    const request: ReportGenerationRequest = {
      reportType: scheduledReport.report_type,
      timeRange: this.calculateTimeRangeForSchedule(scheduledReport.schedule),
      config: scheduledReport.config
    }

    // Report generieren
    const response = await this.generateReport(request)

    // Nächsten Ausführungstermin berechnen
    const nextRun = this.calculateNextRun(scheduledReport.schedule)

    // Scheduled Report aktualisieren
    await supabase
      .from('scheduled_reports')
      .update({
        last_run: new Date().toISOString(),
        next_run: nextRun.toISOString()
      })
      .eq('id', scheduledReport.id)

    // E-Mail-Versand planen (falls Recipients vorhanden)
    if (scheduledReport.recipients && scheduledReport.recipients.length > 0) {
      await this.scheduleReportDelivery(response.reportId, scheduledReport.recipients)
    }
  }

  private calculateTimeRangeForSchedule(schedule: any): any {
    const now = new Date()
    let startDate: Date
    let endDate = new Date(now)

    switch (schedule.frequency) {
      case 'daily':
        startDate = new Date(now)
        startDate.setDate(startDate.getDate() - 1)
        break
      case 'weekly':
        startDate = new Date(now)
        startDate.setDate(startDate.getDate() - 7)
        break
      case 'monthly':
        startDate = new Date(now)
        startDate.setMonth(startDate.getMonth() - 1)
        break
      default:
        startDate = new Date(now)
        startDate.setDate(startDate.getDate() - 1)
    }

    return {
      startDate,
      endDate,
      period: schedule.frequency === 'daily' ? 'daily' : schedule.frequency === 'weekly' ? 'weekly' : 'monthly'
    }
  }

  private async scheduleReportDelivery(reportId: string, recipients: any[]): Promise<void> {
    // TODO: Implementiere E-Mail-Versand-Scheduling
    console.log(`Report-Delivery geplant für Report ${reportId} an ${recipients.length} Empfänger`)
  }
}
