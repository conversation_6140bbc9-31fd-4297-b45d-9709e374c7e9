// Modellkonfiguration
export interface ModelConfiguration {
  provider: 'openai' | 'google' | 'openrouter';
  model: string;
  startMessageMode: 'agent_starts' | 'user_starts';
  openingMessage: string;
  temperature: number; // 0-1
  maxTokens: number;
}

// Sprachausgabe (Voice) - erweitert
export interface VoiceConfiguration {
  voiceProvider: 'elevenlabs' | 'cartesia';
  voice: string; // bestehend
  ttsModel: string;
  backgroundSound?: string;
  backgroundSoundUrl?: string;
  minCharacters: number;
  speechSpeed: number; // 0.5-2.0
}

// Spracherkennung (Transcriber)
export interface TranscriberConfiguration {
  provider: 'deepgram';
  recognitionModel: string;
  backgroundNoiseFilter: boolean;
  confidenceThreshold: number; // 0-1
  numberFormat: 'digits' | 'words';
}

// Werkzeuge (Tools)
export interface ToolsConfiguration {
  selectedTools: string[];
  allowCallEnd: boolean;
  allowKeypad: boolean;
  forwardingNumber?: string;
}

// Analysefunktionen
export interface AnalyticsConfiguration {
  // Zusammenfassung
  summaryTimeout: number;
  summaryPrompt: string;
  minMessagesForAnalysis: number;
  // Erfolgsauswertung
  evaluationPrompt: string;
  // Strukturierte Datenerfassung
  dataExtractionPrompt: string;
  dataExtractionTimeout: number;
}

// Erweiterte Einstellungen
export interface AdvancedSettings {
  // Datenschutz
  hipaaCompliant: boolean;
  pciCompliant: boolean;
  recordAudio: boolean;
  audioFormat: 'mp3' | 'wav' | 'flac';
  recordVideo: boolean;

  // Startzeitpunkt der Sprachausgabe
  speechStartDelay: number;
  intelligentEndpointDetection: boolean;
  punctuationDelay: number;
  noPunctuationDelay: number;
  numberDelay: number;

  // Voicemail-Erkennung
  voicemailProvider: 'jasz' | 'google' | 'openai' | 'twilio';

  // Sprechstopp-Regelung
  userWordStopCount: number;
  userSpeechDuration: number;
  interruptionPause: number;

  // Timeouts für Anrufende
  silenceTimeout: number;
  maxCallDuration: number;

  // Zifferneingabe via Tastatur
  digitInputEnabled: boolean;
  digitInputTimeout: number;
  digitInputSeparator: string;
}

export interface Agent {
  id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  system_prompt: string;
  voice: string;
  language: 'de-DE' | 'en-US';
  status: 'active' | 'inactive';

  // Erweiterte Konfigurationen
  modelConfig?: ModelConfiguration;
  voiceConfig?: VoiceConfiguration;
  transcriberConfig?: TranscriberConfiguration;
  toolsConfig?: ToolsConfiguration;
  analyticsConfig?: AnalyticsConfiguration;
  advancedSettings?: AdvancedSettings;
}

export interface CreateAgentRequest {
  name: string;
  description?: string;
  system_prompt: string;
  voice: string;
  language: 'de-DE' | 'en-US';
  status?: 'active' | 'inactive';

  // Erweiterte Konfigurationen
  modelConfig?: ModelConfiguration;
  voiceConfig?: VoiceConfiguration;
  transcriberConfig?: TranscriberConfiguration;
  toolsConfig?: ToolsConfiguration;
  analyticsConfig?: AnalyticsConfiguration;
  advancedSettings?: AdvancedSettings;
}

export interface UpdateAgentRequest extends Partial<CreateAgentRequest> {
  id: string;
}

export interface AgentListResponse {
  agents: Agent[];
  total: number;
}

export interface AgentResponse {
  agent: Agent;
}

export interface ApiError {
  error: string;
  message: string;
  status: number;
}
