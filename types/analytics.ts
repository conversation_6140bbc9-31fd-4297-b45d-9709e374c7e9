// Analytics-spezifische TypeScript-Interfaces für Story 4

export interface AnalyticsTimeRange {
  startDate: Date
  endDate: Date
  period: 'daily' | 'weekly' | 'monthly' | 'custom'
}

export interface PeriodicReport {
  id: string
  title: string
  timeRange: AnalyticsTimeRange
  generatedAt: Date
  metrics: {
    totalConversations: number
    successfulConversations: number
    successRate: number
    averageDuration: number
    peakHours: Array<{ hour: number; count: number }>
    agentPerformance: AgentPerformanceMetric[]
  }
}

export interface SuccessRateAnalysis {
  overall: {
    rate: number
    trend: number // percentage change
    benchmark: number
  }
  byAgent: Array<{
    agentId: string
    agentName: string
    rate: number
    trend: number
    conversationCount: number
  }>
  byTimeOfDay: Array<{
    hour: number
    rate: number
    conversationCount: number
  }>
  byDayOfWeek: Array<{
    day: number // 0-6 (Sonntag-Samstag)
    dayName: string
    rate: number
    conversationCount: number
  }>
}

export interface ConversationDurationStats {
  summary: {
    average: number
    median: number
    min: number
    max: number
    standardDeviation: number
    percentiles: {
      p25: number
      p50: number
      p75: number
      p95: number
    }
  }
  distribution: {
    short: number // < 2 min
    normal: number // 2-10 min
    long: number // > 10 min
  }
  histogram: Array<{
    bucket: string // "0-1", "1-2", etc.
    count: number
    percentage: number
  }>
  correlations: {
    durationVsSuccess: number
    durationVsTimeOfDay: Array<{
      hour: number
      averageDuration: number
    }>
    durationVsAgent: Array<{
      agentId: string
      agentName: string
      averageDuration: number
    }>
  }
}

export interface ToolUsageAnalytics {
  toolStats: Array<{
    toolName: string
    usageCount: number
    successRate: number
    averageExecutionTime: number
    errorRate: number
    trend: number
  }>
  agentToolUsage: Array<{
    agentId: string
    agentName: string
    toolUsage: Array<{
      toolName: string
      count: number
      successRate: number
    }>
  }>
  toolFlows: Array<{
    sequence: string[]
    frequency: number
    successRate: number
  }>
  recommendations: Array<{
    type: 'unused_tool' | 'performance_issue' | 'optimization'
    message: string
    toolName?: string
    agentId?: string
    priority: 'low' | 'medium' | 'high'
  }>
}

// Report-Generation-Interfaces
export interface ReportGenerationRequest {
  reportType: 'periodic' | 'success_rate' | 'duration_stats' | 'tool_usage'
  timeRange: AnalyticsTimeRange
  config: {
    agentIds?: string[]
    includeCharts?: boolean
    format: 'pdf' | 'excel' | 'csv'
    language: 'de' | 'en'
  }
}

export interface ReportGenerationResponse {
  reportId: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress?: number
  estimatedCompletion?: Date
  downloadUrl?: string
  error?: string
}

export interface ReportStatus {
  reportId: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  progress: number
  createdAt: Date
  completedAt?: Date
  downloadUrl?: string
  expiresAt?: Date
  error?: string
}

// Scheduled Reports
export interface ScheduledReport {
  id: string
  name: string
  reportType: 'periodic' | 'success_rate' | 'duration_stats' | 'tool_usage'
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly'
    time: string // HH:MM format
    dayOfWeek?: number // 0-6 for weekly
    dayOfMonth?: number // 1-31 for monthly
  }
  config: {
    agentIds?: string[]
    format: 'pdf' | 'excel' | 'csv'
    language: 'de' | 'en'
  }
  recipients: Array<{
    email: string
    name?: string
  }>
  enabled: boolean
  lastRun?: Date
  nextRun: Date
  createdAt: Date
  updatedAt: Date
}

// Analytics Query Interfaces
export interface AnalyticsQuery {
  timeRange: AnalyticsTimeRange
  agentIds?: string[]
  filters?: {
    successOnly?: boolean
    minDuration?: number
    maxDuration?: number
    toolNames?: string[]
  }
  groupBy?: 'agent' | 'hour' | 'day' | 'week' | 'month'
  limit?: number
  offset?: number
}

export interface AnalyticsResponse<T> {
  data: T
  timeRange: AnalyticsTimeRange
  totalRecords: number
  generatedAt: Date
  cacheExpiry?: Date
}

// Utility Types
export interface ChartDataPoint {
  label: string
  value: number
  timestamp?: Date
  metadata?: Record<string, any>
}

export interface TrendData {
  current: number
  previous: number
  change: number
  changePercentage: number
  trend: 'up' | 'down' | 'stable'
}

export interface BenchmarkComparison {
  metric: string
  current: number
  target: number
  benchmark: number
  status: 'above' | 'at' | 'below'
  deviation: number
}

// Error Handling
export interface AnalyticsError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
}

// Export-spezifische Interfaces
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  includeCharts: boolean
  language: 'de' | 'en'
  template?: string
  customFields?: Array<{
    name: string
    value: string
  }>
}

export interface ExportResult {
  success: boolean
  downloadUrl?: string
  filename?: string
  size?: number
  error?: string
}
