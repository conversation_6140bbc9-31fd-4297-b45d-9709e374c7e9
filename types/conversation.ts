// Gesprächs-spezifische TypeScript-Interfaces

export interface Conversation {
  id: string
  agentId: string
  callerId?: string
  status: ConversationStatus
  startTime: Date
  endTime?: Date
  duration?: number // in seconds
  transcript: ConversationMessage[]
  metadata: ConversationMetadata
  qualityMetrics?: ConversationQualityMetrics
  recordings?: ConversationRecording[]
}

export type ConversationStatus = 
  | 'queued'
  | 'connecting' 
  | 'active' 
  | 'on-hold' 
  | 'transferring'
  | 'ending' 
  | 'completed' 
  | 'failed' 
  | 'abandoned'

export interface ConversationMessage {
  id: string
  conversationId: string
  timestamp: Date
  speaker: 'agent' | 'caller' | 'system'
  content: string
  messageType: MessageType
  confidence?: number // 0-1 für Spracherkennung
  sentiment?: SentimentAnalysis
  metadata?: MessageMetadata
}

export type MessageType = 
  | 'text'
  | 'audio'
  | 'system_event'
  | 'tool_invocation'
  | 'transfer_request'
  | 'hold_request'
  | 'error'

export interface MessageMetadata {
  toolName?: string
  toolParameters?: Record<string, any>
  transferTarget?: string
  errorCode?: string
  systemEventType?: string
  audioUrl?: string
  audioDuration?: number
}

export interface SentimentAnalysis {
  score: number // -1 to 1
  label: 'positive' | 'neutral' | 'negative'
  confidence: number // 0-1
  emotions?: {
    joy?: number
    anger?: number
    fear?: number
    sadness?: number
    surprise?: number
  }
}

export interface ConversationMetadata {
  priority: 'low' | 'medium' | 'high' | 'urgent'
  tags: string[]
  department?: string
  category?: string
  language: string
  callerInfo?: CallerInfo
  agentInfo?: AgentInfo
  businessContext?: BusinessContext
}

export interface CallerInfo {
  name?: string
  phone?: string
  email?: string
  location?: string
  customerSegment?: string
  previousInteractions?: number
  preferredLanguage?: string
  timezone?: string
}

export interface AgentInfo {
  id: string
  name: string
  avatar?: string
  department?: string
  skills: string[]
  experienceLevel: 'junior' | 'senior' | 'expert'
  currentLoad: number // 0-100%
}

export interface BusinessContext {
  intent?: string
  product?: string
  issue?: string
  resolution?: string
  followUpRequired?: boolean
  customerSatisfaction?: number // 1-5
  escalationLevel?: number // 0-3
}

export interface ConversationQualityMetrics {
  overallScore: number // 1-5
  connectionQuality: number // 1-5
  audioQuality: number // 1-5
  responseTime: number // average in seconds
  resolutionTime: number // total in seconds
  transferCount: number
  holdTime: number // total in seconds
  customerSatisfaction?: number // 1-5
  agentPerformance?: AgentPerformanceMetrics
}

export interface AgentPerformanceMetrics {
  responseAccuracy: number // 0-1
  empathyScore: number // 1-5
  problemSolvingScore: number // 1-5
  communicationScore: number // 1-5
  toolUsageEfficiency: number // 0-1
  adherenceToScript: number // 0-1
}

export interface ConversationRecording {
  id: string
  conversationId: string
  type: 'full' | 'agent_only' | 'caller_only'
  url: string
  duration: number // in seconds
  format: 'mp3' | 'wav' | 'ogg'
  size: number // in bytes
  createdAt: Date
  expiresAt?: Date
  transcribed: boolean
  metadata?: {
    sampleRate?: number
    bitRate?: number
    channels?: number
  }
}

// Gesprächs-Events für WebSocket
export interface ConversationEvent {
  id: string
  conversationId: string
  type: ConversationEventType
  timestamp: Date
  data: any
  source: 'agent' | 'caller' | 'system' | 'supervisor'
}

export type ConversationEventType =
  | 'conversation_started'
  | 'conversation_ended'
  | 'message_sent'
  | 'message_received'
  | 'hold_started'
  | 'hold_ended'
  | 'transfer_initiated'
  | 'transfer_completed'
  | 'tool_invoked'
  | 'quality_updated'
  | 'recording_started'
  | 'recording_stopped'
  | 'supervisor_joined'
  | 'supervisor_left'
  | 'error_occurred'

// Gesprächs-Statistiken
export interface ConversationStats {
  totalConversations: number
  activeConversations: number
  completedConversations: number
  averageDuration: number
  averageQualityScore: number
  totalHoldTime: number
  totalTransfers: number
  resolutionRate: number // 0-1
  customerSatisfactionAverage: number // 1-5
  peakConcurrentConversations: number
  timeRange: {
    start: Date
    end: Date
  }
}

// Filter für Gesprächsliste
export interface ConversationFilters {
  status?: ConversationStatus[]
  agentIds?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  priority?: ConversationMetadata['priority'][]
  department?: string[]
  tags?: string[]
  minDuration?: number
  maxDuration?: number
  minQualityScore?: number
  hasRecording?: boolean
  search?: string
}

// Sortieroptionen
export interface ConversationSortOptions {
  field: 'startTime' | 'endTime' | 'duration' | 'qualityScore' | 'agentName' | 'priority'
  order: 'asc' | 'desc'
}

// Paginierung
export interface ConversationPagination {
  page: number
  limit: number
  total: number
  hasNext: boolean
  hasPrevious: boolean
}

// API Response für Gesprächsliste
export interface ConversationListResponse {
  conversations: Conversation[]
  pagination: ConversationPagination
  stats: ConversationStats
  filters: ConversationFilters
}

// Gesprächs-Export-Optionen
export interface ConversationExportOptions {
  format: 'json' | 'csv' | 'pdf' | 'xlsx'
  includeTranscript: boolean
  includeRecordings: boolean
  includeMetrics: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  filters?: ConversationFilters
}

// Gesprächs-Archivierung
export interface ConversationArchiveOptions {
  retentionPeriod: number // in days
  includeRecordings: boolean
  compressionLevel: 'none' | 'low' | 'medium' | 'high'
  encryptionEnabled: boolean
}

// Utility Functions Types
export interface ConversationUtils {
  formatDuration: (seconds: number) => string
  getStatusColor: (status: ConversationStatus) => string
  getStatusLabel: (status: ConversationStatus) => string
  getPriorityColor: (priority: ConversationMetadata['priority']) => string
  getPriorityLabel: (priority: ConversationMetadata['priority']) => string
  calculateQualityScore: (metrics: ConversationQualityMetrics) => number
  getSentimentEmoji: (sentiment: SentimentAnalysis['label']) => string
  formatTimestamp: (date: Date) => string
}
