// Dashboard-spezifische TypeScript-Interfaces für Agent-Performance Monitoring

export interface AgentDashboardData {
  id: string
  name: string
  avatar?: string
  status: 'active' | 'idle' | 'error' | 'inactive'
  currentCalls: number
  lastActivity: Date
  todayMetrics: {
    totalCalls: number
    successfulCalls: number
    successRate: number
    averageDuration: number // in seconds
    utilization: number // percentage
  }
  yesterdayComparison: {
    callsChange: number // percentage
    successRateChange: number // percentage
    durationChange: number // percentage
  }
}

export interface DashboardSummary {
  totalAgents: number
  activeAgents: number
  totalCallsToday: number
  averageSuccessRate: number
  totalUtilization: number
  lastUpdated: Date
}

export interface DashboardFilters {
  search: string
  status: 'all' | 'active' | 'idle' | 'error' | 'inactive'
  sortBy: 'name' | 'status' | 'activity' | 'performance'
  sortOrder: 'asc' | 'desc'
}

// WebSocket Events für Echtzeit-Updates
export interface WebSocketEvents {
  'agent:status_changed': {
    agentId: string
    status: AgentDashboardData['status']
    timestamp: Date
  }
  'agent:metrics_updated': {
    agentId: string
    metrics: Partial<AgentDashboardData['todayMetrics']>
    timestamp: Date
  }
  'dashboard:summary_updated': DashboardSummary
  'agent:call_started': {
    agentId: string
    callId: string
    timestamp: Date
  }
  'agent:call_ended': {
    agentId: string
    callId: string
    duration: number
    success: boolean
    timestamp: Date
  }
  // Monitoring Events
  'conversation:started': any
  'conversation:updated': any
  'conversation:ended': any
  'transcript:new_entry': any
  'audio:level_update': any
  'quality:update': any
  'takeover:completed': any
  'takeover:failed': any
}

// WebSocket Commands
export interface WebSocketCommands {
  'dashboard:subscribe': {
    agentIds?: string[]
  }
  'dashboard:unsubscribe': {
    agentIds?: string[]
  }
  'dashboard:get_summary': {}
}

// API Response Types
export interface DashboardAgentsResponse {
  agents: AgentDashboardData[]
  summary: DashboardSummary
  total: number
}

export interface DashboardApiError {
  error: string
  message: string
  status: number
}

// Utility Types
export type AgentStatus = AgentDashboardData['status']
export type SortField = DashboardFilters['sortBy']
export type SortOrder = DashboardFilters['sortOrder']

// Trend-Indikatoren
export type TrendDirection = 'up' | 'down' | 'stable'

export interface TrendIndicator {
  value: number
  direction: TrendDirection
  percentage: number
}

// Performance-Metriken für Anzeige
export interface DisplayMetrics {
  totalCalls: {
    value: number
    trend: TrendIndicator
  }
  successRate: {
    value: number
    trend: TrendIndicator
  }
  averageDuration: {
    value: number // in seconds
    trend: TrendIndicator
    formatted: string // z.B. "2m 30s"
  }
  utilization: {
    value: number
    trend: TrendIndicator
  }
}

// Connection Status für WebSocket
export interface ConnectionStatus {
  connected: boolean
  lastConnected?: Date
  reconnectAttempts: number
  error?: string
}

// Dashboard-Konfiguration
export interface DashboardConfig {
  refreshInterval: number // in milliseconds
  maxReconnectAttempts: number
  enableRealtime: boolean
  defaultPageSize: number
  autoRefresh: boolean
}
