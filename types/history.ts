// Gesprächshistorie TypeScript-Interfaces

export interface ConversationHistoryItem {
  id: string
  startTime: Date
  endTime: Date
  duration: number // in seconds
  agentId: string
  agentName: string
  callerId?: string
  callerInfo?: {
    name?: string
    phone?: string
    email?: string
    location?: string
  }
  status: 'completed' | 'abandoned' | 'failed' | 'transferred'
  rating?: number // 1-5
  summary?: string
  tags: string[]
  toolsUsed: string[]
  metadata: {
    department?: string
    priority: 'low' | 'medium' | 'high'
    category?: string
  }
}

export interface ConversationDetails extends ConversationHistoryItem {
  transcript: TranscriptEntry[]
  systemEvents: SystemEvent[]
  audioRecording?: {
    url: string
    duration: number
    format: string
    size: number
  }
  agentNotes?: string
  qualityMetrics?: {
    averageResponseTime: number
    sentimentScore: number
    resolutionStatus: 'resolved' | 'unresolved' | 'escalated'
  }
}

export interface TranscriptEntry {
  id: string
  timestamp: Date
  speaker: 'agent' | 'caller' | 'system'
  content: string
  confidence?: number // 0-1
  sentiment?: 'positive' | 'neutral' | 'negative'
  eventType: 'message' | 'tool_use' | 'transfer' | 'hold' | 'system'
  metadata?: {
    toolName?: string
    transferTarget?: string
    systemEvent?: string
  }
}

export interface SystemEvent {
  id: string
  timestamp: Date
  type: 'tool_used' | 'transfer' | 'hold' | 'resume' | 'note_added' | 'tag_added'
  description: string
  metadata?: {
    toolName?: string
    transferTarget?: string
    note?: string
    tag?: string
  }
}

export interface ConversationFilters {
  dateRange: {
    start: Date
    end: Date
  }
  agentIds: string[]
  status: ('completed' | 'abandoned' | 'failed' | 'transferred')[]
  durationRange: {
    min: number // in seconds
    max: number // in seconds
  }
  search: string
  tags: string[]
  sortBy: 'startTime' | 'duration' | 'agentName' | 'rating'
  sortOrder: 'asc' | 'desc'
  page: number
  pageSize: number
}

export interface ConversationHistoryResponse {
  conversations: ConversationHistoryItem[]
  total: number
  totalPages: number
  currentPage: number
  hasNext: boolean
  hasPrevious: boolean
}

export interface ConversationExportOptions {
  format: 'csv' | 'pdf'
  filters: Partial<ConversationFilters>
  includeTranscript: boolean
  includeSystemEvents: boolean
  includeMetadata: boolean
}

// Utility Types
export type ConversationStatus = ConversationHistoryItem['status']
export type ConversationPriority = ConversationHistoryItem['metadata']['priority']
export type SystemEventType = SystemEvent['type']
export type TranscriptSpeaker = TranscriptEntry['speaker']
export type SortField = ConversationFilters['sortBy']
export type SortOrder = ConversationFilters['sortOrder']

// Filter Presets
export const DATE_RANGE_PRESETS = {
  today: 'Heute',
  yesterday: 'Gestern',
  last7days: 'Letzte 7 Tage',
  last30days: 'Letzte 30 Tage',
  thisMonth: 'Dieser Monat',
  lastMonth: 'Letzter Monat',
  custom: 'Benutzerdefiniert'
} as const

export const DURATION_RANGES = {
  short: { min: 0, max: 60, label: '< 1 Minute' },
  medium: { min: 60, max: 300, label: '1-5 Minuten' },
  long: { min: 300, max: 900, label: '5-15 Minuten' },
  veryLong: { min: 900, max: Infinity, label: '> 15 Minuten' }
} as const

export const STATUS_LABELS = {
  completed: 'Abgeschlossen',
  abandoned: 'Abgebrochen',
  failed: 'Fehlgeschlagen',
  transferred: 'Weitergeleitet'
} as const

export const STATUS_COLORS = {
  completed: 'bg-green-100 text-green-800 border-green-200',
  abandoned: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  failed: 'bg-red-100 text-red-800 border-red-200',
  transferred: 'bg-blue-100 text-blue-800 border-blue-200'
} as const

export const PRIORITY_LABELS = {
  low: 'Niedrig',
  medium: 'Mittel',
  high: 'Hoch'
} as const

export const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-red-100 text-red-800'
} as const

export const SYSTEM_EVENT_LABELS = {
  tool_used: 'Tool verwendet',
  transfer: 'Weitergeleitet',
  hold: 'Warteschleife',
  resume: 'Fortgesetzt',
  note_added: 'Notiz hinzugefügt',
  tag_added: 'Tag hinzugefügt'
} as const

export const SYSTEM_EVENT_ICONS = {
  tool_used: '🔧',
  transfer: '📞',
  hold: '⏸️',
  resume: '▶️',
  note_added: '📝',
  tag_added: '🏷️'
} as const

// Default Filter Values
export const DEFAULT_FILTERS: ConversationFilters = {
  dateRange: {
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    end: new Date()
  },
  agentIds: [],
  status: [],
  durationRange: {
    min: 0,
    max: Infinity
  },
  search: '',
  tags: [],
  sortBy: 'startTime',
  sortOrder: 'desc',
  page: 1,
  pageSize: 50
}

// Pagination Constants
export const PAGE_SIZE_OPTIONS = [25, 50, 100, 200] as const
export const DEFAULT_PAGE_SIZE = 50

// Search Configuration
export const SEARCH_DEBOUNCE_MS = 300
export const MIN_SEARCH_LENGTH = 2

// Export Configuration
export const EXPORT_FORMATS = {
  csv: 'CSV-Datei',
  pdf: 'PDF-Dokument'
} as const

export const MAX_EXPORT_RECORDS = 10000

// Audio Configuration
export const SUPPORTED_AUDIO_FORMATS = ['mp3', 'wav', 'ogg'] as const
export const MAX_AUDIO_SIZE_MB = 100

// Rating Configuration
export const RATING_LABELS = {
  1: 'Sehr schlecht',
  2: 'Schlecht',
  3: 'Durchschnittlich',
  4: 'Gut',
  5: 'Sehr gut'
} as const

// Sentiment Configuration
export const SENTIMENT_LABELS = {
  positive: 'Positiv',
  neutral: 'Neutral',
  negative: 'Negativ'
} as const

export const SENTIMENT_COLORS = {
  positive: 'text-green-600',
  neutral: 'text-gray-600',
  negative: 'text-red-600'
} as const

export const SENTIMENT_EMOJIS = {
  positive: '😊',
  neutral: '😐',
  negative: '😟'
} as const

// Resolution Status Configuration
export const RESOLUTION_STATUS_LABELS = {
  resolved: 'Gelöst',
  unresolved: 'Ungelöst',
  escalated: 'Eskaliert'
} as const

export const RESOLUTION_STATUS_COLORS = {
  resolved: 'bg-green-100 text-green-800',
  unresolved: 'bg-yellow-100 text-yellow-800',
  escalated: 'bg-red-100 text-red-800'
} as const

// Error Types
export interface HistoryError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

// Loading States
export interface HistoryLoadingState {
  conversations: boolean
  details: boolean
  export: boolean
  audio: boolean
}

// API Response Types
export interface HistoryApiResponse<T = any> {
  success: boolean
  data?: T
  error?: HistoryError
  timestamp: Date
}

export interface ConversationStatsResponse {
  totalConversations: number
  averageDuration: number
  statusDistribution: Record<ConversationStatus, number>
  ratingDistribution: Record<number, number>
  topAgents: Array<{
    agentId: string
    agentName: string
    conversationCount: number
    averageRating: number
  }>
  timeRange: {
    start: Date
    end: Date
  }
}
