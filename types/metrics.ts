// Metriken-spezifische TypeScript-Interfaces

export interface BaseMetric {
  timestamp: Date
  value: number
}

export interface CallMetric extends BaseMetric {
  agentId: string
  callId: string
  duration: number // in seconds
  success: boolean
  endReason: 'completed' | 'abandoned' | 'error' | 'transferred'
}

export interface AgentPerformanceMetric {
  agentId: string
  date: Date
  metrics: {
    totalCalls: number
    successfulCalls: number
    failedCalls: number
    averageDuration: number
    totalDuration: number
    utilization: number // percentage
    customerSatisfaction?: number // 1-5 rating
  }
}

export interface SystemMetric extends BaseMetric {
  type: 'cpu' | 'memory' | 'connections' | 'latency'
  unit: string
}

export interface MetricTimeRange {
  start: Date
  end: Date
  granularity: 'minute' | 'hour' | 'day' | 'week' | 'month'
}

export interface MetricQuery {
  agentIds?: string[]
  timeRange: MetricTimeRange
  metricTypes: string[]
  aggregation?: 'sum' | 'avg' | 'min' | 'max' | 'count'
}

export interface MetricResponse {
  data: BaseMetric[]
  timeRange: MetricTimeRange
  total: number
}

// Aggregierte Metriken für Dashboard-Anzeige
export interface AggregatedMetrics {
  period: 'today' | 'yesterday' | 'week' | 'month'
  totalCalls: number
  successfulCalls: number
  successRate: number
  averageDuration: number
  totalDuration: number
  activeAgents: number
  peakConcurrentCalls: number
  averageWaitTime: number
}

// Echtzeit-Metriken
export interface RealtimeMetrics {
  timestamp: Date
  activeCalls: number
  queuedCalls: number
  availableAgents: number
  busyAgents: number
  systemLoad: number
}

// Trend-Analyse
export interface TrendAnalysis {
  metric: string
  currentValue: number
  previousValue: number
  change: number
  changePercentage: number
  trend: 'increasing' | 'decreasing' | 'stable'
  significance: 'high' | 'medium' | 'low'
}

// Performance-Benchmarks
export interface PerformanceBenchmark {
  metric: string
  target: number
  current: number
  status: 'above' | 'at' | 'below'
  threshold: {
    warning: number
    critical: number
  }
}

// Metriken-Konfiguration
export interface MetricConfig {
  name: string
  displayName: string
  unit: string
  format: 'number' | 'percentage' | 'duration' | 'currency'
  precision: number
  showTrend: boolean
  color?: string
}

export interface MetricAlert {
  id: string
  metricName: string
  condition: 'above' | 'below' | 'equals'
  threshold: number
  severity: 'info' | 'warning' | 'error' | 'critical'
  enabled: boolean
  lastTriggered?: Date
}

// Utility-Funktionen für Metriken-Formatierung
export interface MetricFormatter {
  formatValue: (value: number, config: MetricConfig) => string
  formatDuration: (seconds: number) => string
  formatPercentage: (value: number, precision?: number) => string
  formatTrend: (current: number, previous: number) => TrendAnalysis
}
