// Monitoring-spezifische TypeScript-Interfaces für Echtzeit-Gesprächsüberwachung

export interface LiveConversation {
  id: string
  agentId: string
  agentName: string
  agentAvatar?: string
  callerId?: string
  callerInfo?: {
    name?: string
    phone?: string
    location?: string
  }
  status: 'connecting' | 'active' | 'on-hold' | 'ending'
  startTime: Date
  duration: number // in seconds
  lastActivity: Date
  transcript: TranscriptEntry[]
  qualityMetrics: {
    connectionQuality: number // 1-5
    latency: number // in ms
    audioLevel: {
      agent: number // 0-100
      caller: number // 0-100
    }
  }
  metadata: {
    priority: 'low' | 'medium' | 'high'
    tags: string[]
    department?: string
  }
}

export interface TranscriptEntry {
  id: string
  timestamp: Date
  speaker: 'agent' | 'caller' | 'system'
  content: string
  confidence?: number // 0-1
  sentiment?: 'positive' | 'neutral' | 'negative'
  eventType: 'message' | 'tool_use' | 'transfer' | 'hold' | 'system'
  metadata?: {
    toolName?: string
    transferTarget?: string
    systemEvent?: string
  }
}

export interface TakeoverRequest {
  conversationId: string
  reason: 'quality_issue' | 'escalation' | 'training' | 'technical_problem' | 'other'
  note?: string
  supervisorId: string
  timestamp: Date
}

export interface TakeoverResponse {
  success: boolean
  message: string
  conversationId: string
  timestamp: Date
}

// WebSocket Events für Monitoring
export interface MonitoringWebSocketEvents {
  'conversation:started': LiveConversation
  'conversation:updated': {
    conversationId: string
    updates: Partial<LiveConversation>
  }
  'conversation:ended': {
    conversationId: string
    endTime: Date
    reason: string
  }
  'transcript:new_entry': {
    conversationId: string
    entry: TranscriptEntry
  }
  'audio:level_update': {
    conversationId: string
    levels: { agent: number, caller: number }
  }
  'quality:update': {
    conversationId: string
    metrics: LiveConversation['qualityMetrics']
  }
  'takeover:completed': {
    conversationId: string
    supervisorId: string
    timestamp: Date
  }
  'takeover:failed': {
    conversationId: string
    error: string
    timestamp: Date
  }
}

// WebSocket Commands für Monitoring
export interface MonitoringWebSocketCommands {
  'monitoring:subscribe': {
    agentIds?: string[]
    conversationIds?: string[]
  }
  'monitoring:unsubscribe': {
    agentIds?: string[]
    conversationIds?: string[]
  }
  'conversation:takeover': TakeoverRequest
  'conversation:get_details': {
    conversationId: string
  }
}

// Filter für Monitoring-Dashboard
export interface MonitoringFilters {
  search: string
  status: 'all' | 'connecting' | 'active' | 'on-hold' | 'ending'
  agent: string // 'all' oder Agent-ID
  priority: 'all' | 'low' | 'medium' | 'high'
  department: string // 'all' oder Department-Name
  sortBy: 'duration' | 'startTime' | 'agentName' | 'priority'
  sortOrder: 'asc' | 'desc'
}

// Monitoring-Dashboard-Zustand
export interface MonitoringState {
  conversations: LiveConversation[]
  selectedConversation: LiveConversation | null
  filters: MonitoringFilters
  connectionStatus: {
    connected: boolean
    lastConnected?: Date
    error?: string
  }
  loading: boolean
  error: string | null
}

// Audio-Level-Daten
export interface AudioLevelData {
  conversationId: string
  timestamp: Date
  levels: {
    agent: number
    caller: number
  }
  isMuted: {
    agent: boolean
    caller: boolean
  }
}

// Qualitäts-Metriken
export interface QualityMetrics {
  conversationId: string
  connectionQuality: number // 1-5 Sterne
  latency: number // in ms
  audioQuality: number // 1-5 Sterne
  packetLoss: number // Prozentsatz
  jitter: number // in ms
  timestamp: Date
}

// Alert-Definitionen
export interface MonitoringAlert {
  id: string
  conversationId: string
  type: 'long_silence' | 'connection_issue' | 'quality_degradation' | 'call_dropped'
  severity: 'info' | 'warning' | 'error' | 'critical'
  message: string
  timestamp: Date
  acknowledged: boolean
  acknowledgedBy?: string
  acknowledgedAt?: Date
}

// Übernahme-Gründe
export const TAKEOVER_REASONS = {
  quality_issue: 'Qualitätsproblem',
  escalation: 'Eskalation',
  training: 'Schulung/Training',
  technical_problem: 'Technisches Problem',
  other: 'Sonstiges'
} as const

// Status-Farben für UI
export const CONVERSATION_STATUS_COLORS = {
  connecting: 'bg-blue-100 text-blue-800 border-blue-200',
  active: 'bg-green-100 text-green-800 border-green-200',
  'on-hold': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  ending: 'bg-orange-100 text-orange-800 border-orange-200'
} as const

// Status-Labels für UI
export const CONVERSATION_STATUS_LABELS = {
  connecting: 'Verbindung wird aufgebaut',
  active: 'Aktiv',
  'on-hold': 'Warteschleife',
  ending: 'Wird beendet'
} as const

// Prioritäts-Farben
export const PRIORITY_COLORS = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-red-100 text-red-800'
} as const

// Prioritäts-Labels
export const PRIORITY_LABELS = {
  low: 'Niedrig',
  medium: 'Mittel',
  high: 'Hoch'
} as const

// Sentiment-Emojis
export const SENTIMENT_EMOJIS = {
  positive: '😊',
  neutral: '😐',
  negative: '😟'
} as const

// Sentiment-Farben
export const SENTIMENT_COLORS = {
  positive: 'text-green-600',
  neutral: 'text-gray-600',
  negative: 'text-red-600'
} as const

// Event-Type-Icons
export const EVENT_TYPE_ICONS = {
  message: '💬',
  tool_use: '🔧',
  transfer: '📞',
  hold: '⏸️',
  system: '⚙️'
} as const

// Utility Types
export type ConversationStatus = LiveConversation['status']
export type TranscriptSpeaker = TranscriptEntry['speaker']
export type TranscriptEventType = TranscriptEntry['eventType']
export type TakeoverReason = TakeoverRequest['reason']
export type ConversationPriority = LiveConversation['metadata']['priority']
export type SentimentType = TranscriptEntry['sentiment']

// API Response Types
export interface MonitoringApiResponse {
  conversations: LiveConversation[]
  total: number
  timestamp: Date
}

export interface ConversationDetailsResponse {
  conversation: LiveConversation
  fullTranscript: TranscriptEntry[]
  qualityHistory: QualityMetrics[]
  alerts: MonitoringAlert[]
}

// Error Types
export interface MonitoringError {
  code: string
  message: string
  conversationId?: string
  timestamp: Date
}
