// Report-spezifische TypeScript-Interfaces für Story 4

import { AnalyticsTimeRange, ExportOptions } from './analytics'

export interface ReportTemplate {
  id: string
  name: string
  description: string
  type: 'periodic' | 'success_rate' | 'duration_stats' | 'tool_usage'
  sections: ReportSection[]
  defaultConfig: ReportConfig
  createdAt: Date
  updatedAt: Date
}

export interface ReportSection {
  id: string
  title: string
  type: 'metrics' | 'chart' | 'table' | 'text'
  order: number
  config: {
    chartType?: 'line' | 'bar' | 'pie' | 'heatmap' | 'histogram'
    dataSource: string
    filters?: Record<string, any>
    formatting?: {
      showTrend?: boolean
      showBenchmark?: boolean
      precision?: number
    }
  }
  visible: boolean
}

export interface ReportConfig {
  title: string
  subtitle?: string
  timeRange: AnalyticsTimeRange
  agentIds?: string[]
  includeExecutiveSummary: boolean
  includeTrendAnalysis: boolean
  includeBenchmarks: boolean
  includeRecommendations: boolean
  branding: {
    logo?: string
    companyName?: string
    colors?: {
      primary: string
      secondary: string
    }
  }
  exportOptions: ExportOptions
}

export interface GeneratedReport {
  id: string
  templateId: string
  title: string
  config: ReportConfig
  status: 'generating' | 'completed' | 'failed'
  progress: number
  sections: GeneratedReportSection[]
  metadata: {
    generatedAt: Date
    generatedBy: string
    dataRange: AnalyticsTimeRange
    totalRecords: number
    processingTime: number
  }
  files: {
    pdf?: string
    excel?: string
    csv?: string
  }
  error?: string
}

export interface GeneratedReportSection {
  id: string
  title: string
  type: 'metrics' | 'chart' | 'table' | 'text'
  content: {
    data?: any[]
    chartData?: ChartData
    metrics?: MetricsSummary
    text?: string
  }
  status: 'pending' | 'completed' | 'failed'
  error?: string
}

export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'heatmap' | 'histogram' | 'sankey'
  data: Array<{
    label: string
    value: number
    color?: string
    metadata?: Record<string, any>
  }>
  config: {
    xAxis?: {
      label: string
      type: 'category' | 'time' | 'value'
    }
    yAxis?: {
      label: string
      type: 'value' | 'percentage'
      min?: number
      max?: number
    }
    colors?: string[]
    showLegend?: boolean
    showGrid?: boolean
  }
}

export interface MetricsSummary {
  primary: Array<{
    label: string
    value: number | string
    unit?: string
    trend?: {
      direction: 'up' | 'down' | 'stable'
      percentage: number
    }
    benchmark?: {
      value: number
      status: 'above' | 'at' | 'below'
    }
  }>
  secondary?: Array<{
    label: string
    value: number | string
    unit?: string
  }>
}

// Report-Scheduling
export interface ReportSchedule {
  id: string
  name: string
  description?: string
  templateId: string
  config: ReportConfig
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    time: string // HH:MM
    timezone: string
    dayOfWeek?: number // 0-6 for weekly
    dayOfMonth?: number // 1-31 for monthly
    weekOfMonth?: number // 1-4 for monthly
  }
  recipients: Array<{
    email: string
    name?: string
    role?: string
  }>
  enabled: boolean
  lastRun?: {
    date: Date
    reportId: string
    status: 'success' | 'failed'
    error?: string
  }
  nextRun: Date
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

// Report-Delivery
export interface ReportDelivery {
  id: string
  reportId: string
  scheduleId?: string
  method: 'email' | 'webhook' | 'download'
  recipients: Array<{
    email?: string
    webhookUrl?: string
    status: 'pending' | 'sent' | 'failed'
    sentAt?: Date
    error?: string
  }>
  status: 'pending' | 'completed' | 'failed'
  createdAt: Date
  completedAt?: Date
}

// Report-Analytics (Meta-Analytics)
export interface ReportUsageAnalytics {
  reportId: string
  templateId: string
  metrics: {
    generationTime: number
    fileSize: number
    downloadCount: number
    viewCount: number
    shareCount: number
  }
  userInteractions: Array<{
    userId: string
    action: 'generated' | 'downloaded' | 'viewed' | 'shared'
    timestamp: Date
    metadata?: Record<string, any>
  }>
}

// Report-Validation
export interface ReportValidation {
  isValid: boolean
  errors: Array<{
    field: string
    message: string
    code: string
  }>
  warnings: Array<{
    field: string
    message: string
    code: string
  }>
}

// Report-Cache
export interface ReportCache {
  key: string
  reportId: string
  data: any
  createdAt: Date
  expiresAt: Date
  size: number
  hitCount: number
}

// Utility Types für Reports
export type ReportStatus = 'draft' | 'generating' | 'completed' | 'failed' | 'expired'
export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json'
export type ReportFrequency = 'once' | 'daily' | 'weekly' | 'monthly' | 'quarterly'

// Report-Permissions
export interface ReportPermissions {
  canGenerate: boolean
  canSchedule: boolean
  canExport: boolean
  canShare: boolean
  canDelete: boolean
  allowedFormats: ReportFormat[]
  maxReportsPerDay?: number
}

// Report-Audit
export interface ReportAuditLog {
  id: string
  reportId: string
  userId: string
  action: 'created' | 'generated' | 'downloaded' | 'shared' | 'deleted'
  timestamp: Date
  metadata: {
    userAgent?: string
    ipAddress?: string
    additionalData?: Record<string, any>
  }
}
